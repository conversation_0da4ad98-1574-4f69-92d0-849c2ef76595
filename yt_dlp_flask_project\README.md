# Stems Finder Flask Application

A Flask web application that helps you find matching video stems from YouTube URLs or titles by searching through Google Sheets data.

## Features

- **YouTube URL Search**: Extract video information and find matching stems
- **Title/Keyword Search**: Search for stems using video titles or keywords
- **Google Sheets Integration**: Search across multiple Google Sheets
- **Google Drive Integration**: Direct links to Google Drive files
- **Responsive UI**: Modern Bootstrap-based interface
- **Real-time Search**: Fast fuzzy matching with scoring
- **Duration Matching**: Color-coded results based on duration accuracy

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Google API Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google Sheets API and Google Drive API
4. Create a Service Account:
   - Go to IAM & Admin > Service Accounts
   - Click "Create Service Account"
   - Fill in the details and create
   - Generate a JSON key file
5. Share your Google Sheets with the service account email
6. Rename the downloaded JSON file to `credentials.json` and place it in the project directory

### 3. Configuration

The application is configured to search through these Google Sheets:
- Edited Main Sheet
- Social Media Catalog(SG)
- Social Media Catalog(IF)
- Social Media Catalog(IG)
- Social Media Catalog(CP)
- Copy Social Media Catalog(SG)
- Copy Social Media Catalog(IF)
- Copy Social Media Catalog(IG)

Google Sheet ID: `1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE`
Google Drive Folder: `https://drive.google.com/drive/folders/1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG`

### 4. Run the Application

```bash
python app.py
```

The application will run on `http://localhost:8080` (not port 5000 as requested)

## Usage

### Search by YouTube URL
1. Paste a YouTube URL in the URL search form
2. Click "Search by URL"
3. The app will extract video title and duration
4. Search through all Google Sheets for matching stems
5. Results show matching score, duration comparison, and Google Drive links

### Search by Title/Keyword
1. Enter video title or keywords in the title search form
2. Click "Search by Title"
3. The app searches through all sheets for matching filenames
4. Results show relevance scores and sheet sources

## API Endpoints

- `GET /` - Main application page
- `POST /match_by_url` - Search by YouTube URL
- `POST /match_by_title` - Search by title/keywords
- `GET /health` - Health check endpoint

## Features Explained

### Fuzzy Matching
- Uses fuzzywuzzy library for intelligent text matching
- Combines token-based scoring with keyword overlap
- Scores results from 0-100+ for relevance

### Duration Matching
- Green: Duration matches within 20 seconds
- Red: Duration differs by more than 20 seconds
- Blue: Title-only searches (no duration comparison)

### Google Sheets Integration
- Searches through multiple sheets sequentially
- Automatically detects filename and duration columns
- Handles various column naming conventions

### Error Handling
- Graceful fallback to local CSV if Google Sheets unavailable
- Comprehensive error logging
- User-friendly error messages

## Troubleshooting

### Common Issues

1. **"Credentials file not found"**
   - Ensure `credentials.json` is in the project directory
   - Check file permissions

2. **"Failed to fetch video title"**
   - Verify YouTube URL is valid and accessible
   - Check internet connection
   - Some videos may be region-restricted

3. **"No data found in sheet"**
   - Verify sheet names match configuration
   - Check if service account has access to the sheets
   - Ensure sheets contain data

### Logs
Check `app.log` for detailed error information and debugging.

## Development

### Project Structure
```
yt_dlp_flask_project/
├── app.py                 # Main Flask application
├── templates/
│   └── index.html        # Web interface
├── requirements.txt      # Python dependencies
├── credentials.json      # Google API credentials (not in repo)
├── credentials_template.json  # Template for credentials
└── README.md            # This file
```

### Adding New Sheets
To add new sheets to search, update the `SHEET_NAMES` list in the `Config` class in `app.py`.

### Customizing Search
Modify the `smart_clean()` and `extract_keywords()` functions to adjust text processing and matching logic.
