# Archives Stems Finder Pro v3.0 OAuth2

A professional Flask application for searching archives and stems with Google OAuth2 authentication, Google Drive integration, and automatic cache management.

## 🚀 Features

- **🔐 Google OAuth2 Authentication** - Secure login with email-based access control
- **🔍 Enhanced Search** - Multi-strategy search across video IDs, OCD numbers, VP numbers, and filenames
- **📁 Google Drive Integration** - Direct "Open in Drive" links for results with OCD/VP numbers
- **🔄 Cache Management** - Manual refresh with progress bar + automatic 3:00 AM refresh
- **📊 Real-time Statistics** - Live cache status and performance metrics
- **🌐 NGINX Compatible** - ProxyFix middleware and relative URLs for reverse proxy support
- **⚡ High Performance** - Search 21,000+ records in under 1 second

## 📁 Project Structure

```
yt_dlp_flask_project/
├── app.py                    # Main Flask application
├── requirements.txt          # Python dependencies
├── run_app.bat              # Windows startup script
├── setup_env.bat            # Environment setup script
├── credentials.json         # Google Sheets API credentials
├── archives_cache.csv       # Cached search data
├── data/
│   └── acl_file.csv        # Email access control list
├── templates/
│   ├── main.html           # Main search interface
│   └── error.html          # Error page template
├── GOOGLE_OAUTH_SETUP.md   # OAuth setup instructions
├── IMPLEMENTATION_SUMMARY.md # Technical details
└── final_working_app.py    # Reference implementation
```

## 🛠️ Setup Instructions

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Environment Variables
Set these environment variables (or use `setup_env.bat`):
```bash
set GOOGLE_CLIENT_ID=your-google-client-id
set GOOGLE_CLIENT_SECRET=your-google-client-secret
set SECRET_KEY=your-secret-key
```

### 3. Configure Access Control
Edit `data/acl_file.csv` to add authorized email addresses:
```csv
email
<EMAIL>
<EMAIL>
```

### 4. Run the Application
```bash
# Windows
.\run_app.bat

# Or manually
python app.py
```

## 🔍 Search Functionality

The application supports searching for:
- **Video IDs**: `aEQsJ2LVKbM`, `V9krqoYDI_U`, `DgQQqwIwfj0`
- **OCD Numbers**: `OCD-17588`, `OCD-16693`
- **VP Numbers**: `VP-37160`, `VP-17523`
- **Filenames**: Any part of the filename
- **Fuzzy Matching**: Partial and approximate matches

## 📁 Google Drive Integration

Results with valid OCD/VP numbers show a green "📁 Open in Drive" button that opens:
`https://drive.google.com/drive/search?q=OCD-17588`

## 🔄 Cache Management

- **Manual Refresh**: Click "🔄 Manual Refresh" button with progress bar
- **Automatic Refresh**: Daily at 3:00 AM
- **Real-time Status**: Shows cache size, last updated, next refresh time

## 🔐 Security Features

- **OAuth2 Authentication**: Google sign-in required
- **Email-based ACL**: Only authorized emails can access
- **Session Management**: Secure session handling
- **NGINX Compatible**: ProxyFix for reverse proxy deployment

## 🌐 Production Deployment

For NGINX reverse proxy, the app uses:
- `url_for()` for all internal links
- `ProxyFix` middleware for proper header handling
- Relative URLs throughout the application

## 📊 Performance

- **Search Speed**: < 1 second for 21,000+ records
- **Cache Size**: 21,838 rows loaded
- **Concurrent Users**: Supports 80+ simultaneous users
- **Memory Efficient**: Optimized pandas operations

## 🔧 Technical Details

- **Framework**: Flask 2.3.3
- **Authentication**: Authlib OAuth2
- **Data Processing**: Pandas
- **Search Engine**: FuzzyWuzzy + custom algorithms
- **Scheduling**: APScheduler for automatic refresh
- **Frontend**: Vanilla JavaScript with modern UI

## 📝 API Endpoints

- `GET /` - Main search interface
- `POST /search` - Perform search
- `GET /cache/status` - Cache information
- `POST /cache/refresh` - Manual cache refresh
- `GET /auth/login` - OAuth login
- `GET /auth/callback` - OAuth callback
- `GET /logout` - User logout

## 🚀 Quick Start

1. Clone the repository
2. Run `setup_env.bat` to set environment variables
3. Edit `data/acl_file.csv` with authorized emails
4. Run `run_app.bat`
5. Open `http://127.0.0.1:5000`
6. Login with authorized Google account
7. Start searching!

## 📞 Support

For issues or questions, check the implementation files:
- `GOOGLE_OAUTH_SETUP.md` - OAuth configuration
- `IMPLEMENTATION_SUMMARY.md` - Technical details
- `final_working_app.py` - Reference implementation
