#!/usr/bin/env python3
"""
Debug the sophisticated search to see why it's not finding matches
"""

import requests

def debug_sophisticated_search():
    """Debug the sophisticated search step by step"""
    
    print("🔧 DEBUGGING SOPHISTICATED SEARCH")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # First, test that regular search works
    print(f"\n📋 STEP 1: Test regular search")
    
    regular_queries = [
        "cancer",
        "prevent",
        "Can-We-Prevent-Cancer",
        "Why-Is-God-Giving-Problems"
    ]
    
    for query in regular_queries:
        response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        if response.status_code == 200:
            result = response.json()
            matches = result.get('total_matches', 0)
            print(f"   '{query}': {matches} matches")
            
            if matches > 0:
                first_match = result['results'][0]
                filename = first_match.get('filename', 'N/A')
                print(f"      First: {filename[:50]}...")
        else:
            print(f"   '{query}': Error {response.status_code}")
    
    # Test the sophisticated queries
    print(f"\n📋 STEP 2: Test sophisticated queries")
    
    sophisticated_queries = [
        "A tip to prevent cancer?",
        "Why is God giving problems?"
    ]
    
    for query in sophisticated_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        
        if response.status_code == 200:
            result = response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"   Status: 200 OK")
            print(f"   Matches: {matches}")
            print(f"   Time: {search_time:.3f}s")
            
            if matches > 0:
                for i, match in enumerate(result.get('results', [])[:3], 1):
                    filename = match.get('filename', 'N/A')
                    match_type = match.get('match_type', 'Unknown')
                    matched_var = match.get('matched_variation', 'N/A')
                    
                    print(f"   Match {i}:")
                    print(f"      File: {filename[:50]}...")
                    print(f"      Type: {match_type}")
                    print(f"      Matched: '{matched_var}'")
            else:
                print(f"   ❌ No matches found")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
    
    # Test if the queries are being detected as sophisticated
    print(f"\n📋 STEP 3: Check query detection")
    
    test_queries = [
        "A tip to prevent cancer?",
        "why is god giving problems?",  # lowercase
        "regular query"
    ]
    
    for query in test_queries:
        query_lower = query.lower().strip()
        is_sophisticated = query_lower in ["a tip to prevent cancer?", "why is god giving problems?"]
        print(f"   '{query}' -> sophisticated: {is_sophisticated}")

def test_manual_processing():
    """Test the processing steps manually"""
    
    print(f"\n📋 STEP 4: Manual processing test")
    print("=" * 60)
    
    import re
    
    queries = [
        "A tip to prevent cancer?",
        "Why is God giving problems?"
    ]
    
    for query in queries:
        print(f"\n🔍 Processing: '{query}'")
        
        # Step 1: Remove special characters
        cleaned = re.sub(r'[^\w\s]', '', query)
        print(f"   Cleaned: '{cleaned}'")
        
        # Step 2: Replace spaces with hyphens
        processed = cleaned.strip().replace(' ', '-')
        print(f"   Processed: '{processed}'")
        
        # Step 3: Split into words
        words = cleaned.strip().split()
        print(f"   Words: {words}")
        
        # Step 4: Create splits
        if len(words) > 2:
            first_2 = '-'.join(words[:2])
            second_2 = '-'.join(words[2:])
            print(f"   2-word split: '{first_2}' + '{second_2}'")
            
            if len(words) > 3:
                first_3 = '-'.join(words[:3])
                second_3 = '-'.join(words[3:])
                print(f"   3-word split: '{first_3}' + '{second_3}'")
        
        # Test what we should search for
        search_terms = [processed]
        if len(words) > 2:
            search_terms.extend([first_2, second_2])
            if len(words) > 3:
                search_terms.extend([first_3, second_3])
        
        # Add individual meaningful words
        meaningful_words = [w for w in words if len(w) > 3]
        search_terms.extend(meaningful_words)
        
        print(f"   Search terms: {search_terms}")

if __name__ == "__main__":
    print("🚀 SOPHISTICATED SEARCH DEBUG")
    print("Finding out why sophisticated search returns 0 matches")
    
    # Debug sophisticated search
    debug_sophisticated_search()
    
    # Test manual processing
    test_manual_processing()
    
    print("\n" + "=" * 60)
    print("🎯 SOPHISTICATED SEARCH DEBUG COMPLETE")
    print("=" * 60)
    
    print("💡 Check the results above to identify the issue")
    print("🔧 The algorithm steps are correct, but search execution may have issues")
    
    print("\n🚀 Sophisticated search debug complete!")
