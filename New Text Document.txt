import tkinter as tk
from tkinter import messagebox, ttk
import yt_dlp
import pandas as pd
import re
import pyperclip
import time
import threading
import webbrowser
from PIL import Image, ImageTk
import os
import json
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    filename='error.log',
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Check for required dependencies
def check_dependencies():
    missing = []
    for module in ['yt_dlp', 'pandas', 'fuzzywuzzy', 'pyperclip', 'PIL']:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    if missing:
        error_msg = f"Missing dependencies: {', '.join(missing)}. Please install them using:\n pip install {' '.join(missing)}"
        logging.error(error_msg)
        messagebox.showerror("Dependency Error", error_msg)
        root.quit()

# Function to clean the text
def smart_clean(text):
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = re.sub(r'[_\-]', ' ', text)
    text = re.sub(r'\d{1,2}[a-z]{0,2}[- ]?(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*[- ]?\d{2,4}', '', text)
    text = re.sub(r'(sharings|stems|english|hindi|tamil|mins|secs|\d{2,4})', '', text)
    text = re.sub(r'[^a-z\s]', '', text)
    return text.strip()

# Function to extract keywords from text
def extract_keywords(text):
    text = smart_clean(text)
    return set(text.split())

# Function to format seconds into minutes and seconds
def format_seconds(hms_string):
    try:
        hms_string = hms_string.strip()
        if hms_string.isdigit():
            total_seconds = int(hms_string)
        else:
            parts = hms_string.split(':')
            if len(parts) == 2:
                m, s = map(int, parts)
                total_seconds = m * 60 + s
            elif len(parts) == 3:
                h, m, s = map(int, parts)
                total_seconds = h * 3600 + m * 60 + s
            else:
                raise ValueError("Invalid duration format")
        return f"{total_seconds // 60}m {total_seconds % 60}s", total_seconds
    except Exception as e:
        logging.error(f"Invalid duration format: {hms_string} - {str(e)}")
        messagebox.showwarning("Warning", f"Invalid duration format: {hms_string}")
        return "??m ??s", 0

# Function to get video info from YouTube
def get_video_info(url):
    try:
        ydl_opts = {
            'quiet': True,
            'forcejson': True,
            'noplaylist': True,
            'nocheckcertificate': True
        }
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info_dict = ydl.extract_info(url, download=False)
            title = info_dict.get('title', None)
            duration = info_dict.get('duration', 0)
            duration_str = f"{duration // 60}m {duration % 60}s"
            return title, duration_str, duration
    except Exception as e:
        logging.error(f"Error fetching video info: {str(e)}")
        messagebox.showerror("Error", f"Error fetching video info: {e}")
        return None, None, 0

# Function to load CSV data
csv_cache = None
def load_csv(csv_url):
    global csv_cache
    if csv_cache is None:
        try:
            csv_cache = pd.read_csv(csv_url)
        except Exception as e:
            logging.error(f"Error fetching CSV: {str(e)}")
            messagebox.showerror("Error", f"Error fetching CSV: {e}")
            return None
    return csv_cache

# Function to match filenames from Google Sheets CSV data
def match_filename(video_title, csv_url, top_n=5):
    from fuzzywuzzy import fuzz
    df = load_csv(csv_url)
    if df is None:
        return []
    if 'filename' not in df.columns or 'duration' not in df.columns or 'drive_link' not in df.columns:
        error_msg = "CSV must contain 'filename', 'duration', and 'drive_link' columns."
        logging.error(error_msg)
        messagebox.showerror("Error", error_msg)
        return []

    filenames = df['filename'].fillna('').tolist()
    durations = df['duration'].fillna("00:00:00").tolist()
    drive_links = df['drive_link'].fillna('').tolist()

    video_clean = smart_clean(video_title)
    video_keywords = extract_keywords(video_title)

    match_results = []

    for filename, file_duration, drive_link in zip(filenames, durations, drive_links):
        file_clean = smart_clean(filename)
        file_keywords = extract_keywords(filename)
        keyword_overlap = len(video_keywords.intersection(file_keywords))
        token_score = fuzz.token_set_ratio(video_clean, file_clean)
        combined_score = token_score + (keyword_overlap * 5)

        match_results.append((filename, combined_score, file_duration, drive_link))

    match_results.sort(key=lambda x: x[1], reverse=True)
    return match_results[:top_n]

# Function to truncate long text for display
def truncate_text(text, max_length=30):
    return text if len(text) <= max_length else text[:max_length-3] + "..."

# Theme management
class Theme:
    def __init__(self):
        self.is_dark = False
        self.light_colors = {
            'bg': '#f5f5f5',
            'fg': '#333333',
            'entry_bg': '#ffffff',
            'button_bg': '#4CAF50',
            'button_fg': '#ffffff',
            'hover_bg': '#45a049',
            'result_bg': '#e0e0e0',
            'frame_bg': '#ffffff',
            'table_bg': '#ffffff',
            'table_alt': '#f0f0f0'
        }
        self.dark_colors = {
            'bg': '#2d2d2d',
            'fg': '#e0e0e0',
            'entry_bg': '#3c3c3c',
            'button_bg': '#4CAF50',
            'button_fg': '#ffffff',
            'hover_bg': '#45a049',
            'result_bg': '#4a4a4a',
            'frame_bg': '#3c3c3c',
            'table_bg': '#3c3c3c',
            'table_alt': '#4a4a4a'
        }

    def get_colors(self):
        return self.dark_colors if self.is_dark else self.light_colors

    def toggle(self):
        self.is_dark = not self.is_dark
        apply_theme()

# Settings management
class Settings:
    def __init__(self):
        self.filename = 'settings.json'
        self.defaults = {
            'font_size': 'medium',
            'theme': 'light',
            'result_limit': 5
        }
        self.settings = self.load_settings()

    def load_settings(self):
        try:
            if os.path.exists(self.filename):
                with open(self.filename, 'r') as f:
                    return json.load(f)
            return self.defaults
        except Exception as e:
            logging.error(f"Error loading settings: {str(e)}")
            return self.defaults

    def save_settings(self):
        try:
            with open(self.filename, 'w') as f:
                json.dump(self.settings, f, indent=4)
        except Exception as e:
            logging.error(f"Error saving settings: {str(e)}")

    def get_font_size(self):
        sizes = {'small': 12, 'medium': 14, 'large': 16}
        return sizes.get(self.settings['font_size'], 14)

    def get_result_limit(self):
        return self.settings['result_limit']

    def update(self, key, value):
        self.settings[key] = value
        self.save_settings()

# Apply theme to widgets
def apply_theme():
    colors = theme.get_colors()
    font_size = settings.get_font_size()
    root.configure(bg=colors['bg'])
    main_frame.configure(bg=colors['bg'])
    url_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    url_entry.configure(bg=colors['entry_bg'], fg=colors['fg'], font=("Roboto", font_size))
    match_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size))
    search_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    search_entry.configure(bg=colors['entry_bg'], fg=colors['fg'], font=("Roboto", font_size))
    search_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size))
    ocd_vp_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    ocd_vp_entry.configure(bg=colors['entry_bg'], fg=colors['fg'], font=("Roboto", font_size))
    ocd_vp_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size))
    video_id_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    video_id_entry.configure(bg=colors['entry_bg'], fg=colors['fg'], font=("Roboto", font_size))
    video_id_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size))
    theme_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size-2))
    settings_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size-2))
    score_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    result_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size+2, 'bold'))
    timer_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size-2))
    canvas.configure(bg=colors['frame_bg'])
    tree.configure(style="Custom.Treeview")
    style.configure("Custom.Treeview", background=colors['table_bg'], foreground=colors['fg'], font=("Roboto", font_size-2))
    style.configure("Custom.Treeview.Heading", background=colors['table_bg'], foreground=colors['fg'], font=("Roboto", font_size, 'bold'))

# Load icons
def load_icon(filename, size=(20, 20)):
    try:
        path = os.path.join("icons", filename)
        if os.path.exists(path):
            img = Image.open(path).resize(size, Image.LANCZOS)
            return ImageTk.PhotoImage(img)
    except Exception as e:
        logging.error(f"Error loading icon {filename}: {str(e)}")
    return None

# Settings panel
def open_settings():
    dialog = tk.Toplevel(root)
    dialog.title("Settings")
    dialog.geometry("400x300")
    dialog.transient(root)
    dialog.grab_set()
    dialog.configure(bg=theme.get_colors()['bg'])

    tk.Label(dialog, text="Settings", font=("Roboto", 16, 'bold'), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(pady=10)

    # Font size
    tk.Label(dialog, text="Font Size:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    font_var = tk.StringVar(value=settings.settings['font_size'])
    font_menu = ttk.Combobox(dialog, textvariable=font_var, values=['small', 'medium', 'large'], state='readonly', font=("Roboto", 12))
    font_menu.pack(fill="x", padx=20, pady=5)

    # Theme
    tk.Label(dialog, text="Theme:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    theme_var = tk.StringVar(value=settings.settings['theme'])
    theme_menu = ttk.Combobox(dialog, textvariable=theme_var, values=['light', 'dark'], state='readonly', font=("Roboto", 12))
    theme_menu.pack(fill="x", padx=20, pady=5)

    # Result limit
    tk.Label(dialog, text="Result Limit:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    limit_var = tk.IntVar(value=settings.settings['result_limit'])
    limit_spin = tk.Spinbox(dialog, from_=1, to=10, textvariable=limit_var, font=("Roboto", 12))
    limit_spin.pack(fill="x", padx=20, pady=5)

    def save():
        settings.update('font_size', font_var.get())
        settings.update('theme', theme_var.get())
        settings.update('result_limit', limit_var.get())
        if theme_var.get() == 'dark' and not theme.is_dark:
            theme.toggle()
        elif theme_var.get() == 'light' and theme.is_dark:
            theme.toggle()
        apply_theme()
        dialog.destroy()

    btn_frame = tk.Frame(dialog, bg=theme.get_colors()['bg'])
    btn_frame.pack(pady=20)
    tk.Button(btn_frame, text="Save", command=save, font=("Roboto", 12), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat").pack(side="left", padx=5)
    tk.Button(btn_frame, text="Cancel", command=dialog.destroy, font=("Roboto", 12), bg='#d32f2f', fg='#ffffff', activebackground='#b71c1c', relief="flat").pack(side="left", padx=5)

# Function to search by OCD/VP number
def search_by_ocd_vp():
    ocd_vp = ocd_vp_entry.get().strip()
    if not ocd_vp:
        messagebox.showwarning("Input", "Please enter an OCD/VP number.")
        ocd_vp_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
        root.after(2000, lambda: ocd_vp_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
        return

    def run_task():
        start_time = time.time()
        csv_url = "https://docs.google.com/spreadsheets/d/1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE/export?format=csv"
        df = load_csv(csv_url)
        if df is None:
            return
        if 'filename' not in df.columns or 'duration' not in df.columns or 'ocd_vp' not in df.columns or 'drive_link' not in df.columns:
            error_msg = "CSV must contain 'filename', 'duration', 'ocd_vp', and 'drive_link' columns."
            logging.error(error_msg)
            root.after(0, lambda: messagebox.showerror("Error", error_msg))
            return

        matches = df[df['ocd_vp'].astype(str).str.strip().str.lower() == ocd_vp.lower()]
        root.after(0, lambda: animate_progress(100 if not matches.empty else 0))
        root.after(0, lambda: score_label.configure(text=f"Best Match Score: {100 if not matches.empty else 0}%"))

        root.after(0, lambda: tree.delete(*tree.get_children()))
        if matches.empty:
            tree.insert("", "end", values=("No matches found.", "", ""), tags=('error',))
        else:
            for idx, row in matches.iterrows():
                filename = row['filename']
                file_duration = row['duration']
                drive_link = row['drive_link']
                file_duration_fmt, _ = format_seconds(file_duration)
                tag = 'even' if idx % 2 == 0 else 'odd'
                tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link)), tags=(tag, filename, drive_link))

        end_time = time.time()
        elapsed_time = round(end_time - start_time, 2)
        root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

    threading.Thread(target=run_task, daemon=True).start()

# Function to search by Video ID
def search_by_video_id():
    video_id = video_id_entry.get().strip()
    if not video_id:
        messagebox.showwarning("Input", "Please enter a Video ID.")
        video_id_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
        root.after(2000, lambda: video_id_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
        return

    def run_task():
        start_time = time.time()
        csv_url = "https://docs.google.com/spreadsheets/d/1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE/export?format=csv"
        df = load_csv(csv_url)
        if df is None:
            return
        if 'filename' not in df.columns or 'duration' not in df.columns or 'video_id' not in df.columns or 'drive_link' not in df.columns:
            error_msg = "CSV must contain 'filename', 'duration', 'video_id', and 'drive_link' columns."
            logging.error(error_msg)
            root.after(0, lambda: messagebox.showerror("Error", error_msg))
            return

        matches = df[df['video_id'].astype(str).str.strip().str.lower() == video_id.lower()]
        root.after(0, lambda: animate_progress(100 if not matches.empty else 0))
        root.after(0, lambda: score_label.configure(text=f"Best Match Score: {100 if not matches.empty else 0}%"))

        root.after(0, lambda: tree.delete(*tree.get_children()))
        if matches.empty:
            tree.insert("", "end", values=("No matches found.", "", ""), tags=('error',))
        else:
            for idx, row in matches.iterrows():
                filename = row['filename']
                file_duration = row['duration']
                drive_link = row['drive_link']
                file_duration_fmt, _ = format_seconds(file_duration)
                tag = 'even' if idx % 2 == 0 else 'odd'
                tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link)), tags=(tag, filename, drive_link))

        end_time = time.time()
        elapsed_time = round(end_time - start_time, 2)
        root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

    threading.Thread(target=run_task, daemon=True).start()

# Function to search by title
def search_by_title():
    search_term = search_entry.get().strip()
    if not search_term:
        messagebox.showwarning("Input", "Please enter a search term.")
        search_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
        root.after(2000, lambda: search_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
        return

    def run_task():
        start_time = time.time()
        csv_url = "https://docs.google.com/spreadsheets/d/1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE/export?format=csv"
        top_matches = match_filename(search_term, csv_url, top_n=settings.get_result_limit())

        root.after(0, lambda: tree.delete(*tree.get_children()))
        for idx, (filename, score, file_duration, drive_link) in enumerate(top_matches):
            file_duration_fmt, _ = format_seconds(file_duration)
            tag = 'even' if idx % 2 == 0 else 'odd'
            tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link)), tags=(tag, filename, drive_link))

        end_time = time.time()
        elapsed_time = round(end_time - start_time, 2)
        root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

    threading.Thread(target=run_task, daemon=True).start()

# Function to handle match click event
def on_match_click():
    url = url_entry.get().strip()
    if not url:
        messagebox.showwarning("Input", "Please paste a YouTube URL.")
        url_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
        root.after(2000, lambda: url_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
        return

    def run_task():
        start_time = time.time()
        url = url_entry.get()
        video_title, yt_duration, yt_duration_seconds = get_video_info(url)
        if not video_title:
            return

        root.after(0, lambda: result_label.configure(text=f"🎥 Title: {video_title}\n🕒 YT Duration: {yt_duration}"))
        csv_url = "https://docs.google.com/spreadsheets/d/1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE/export?format=csv"
        top_matches = match_filename(video_title, csv_url, top_n=settings.get_result_limit())

        root.after(0, lambda: animate_progress(top_matches[0][1] if top_matches else 0))
        root.after(0, lambda: score_label.configure(text=f"Best Match Score: {top_matches[0][1] if top_matches else 0}%"))

        root.after(0, lambda: tree.delete(*tree.get_children()))
        for idx, (filename, score, file_duration, drive_link) in enumerate(top_matches):
            file_duration_fmt, file_duration_seconds = format_seconds(file_duration)
            duration_diff = abs(yt_duration_seconds - file_duration_seconds)
            color = "green" if duration_diff <= 2 else "#90EE90" if duration_diff <= 7 else "red"
            tag = 'even' if idx % 2 == 0 else 'odd'
            tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link), yt_duration), tags=(tag, filename, drive_link, color))

        end_time = time.time()
        elapsed_time = round(end_time - start_time, 2)
        root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

    threading.Thread(target=run_task, daemon=True).start()

# Function to handle match selection
def on_match_select(event):
    item = tree.selection()
    if not item:
        return
    tags = tree.item(item, "tags")
    if 'error' in tags:
        return
    filename = tags[1]  # Second tag is filename
    drive_link = tags[2]  # Third tag is drive_link

    dialog = tk.Toplevel(root)
    dialog.title("Copy Selection")
    dialog.geometry("400x250")
    dialog.transient(root)
    dialog.grab_set()
    dialog.configure(bg=theme.get_colors()['bg'])

    tk.Label(dialog, text="What would you like to copy?", font=("Roboto", 16), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'], image=copy_icon, compound="left").pack(pady=20)

    def copy_filename():
        pyperclip.copy(filename)
        messagebox.showinfo("Copied", f"Copied: {truncate_text(filename, 50)}")
        dialog.destroy()

    def copy_drive_link():
        pyperclip.copy(drive_link)
        messagebox.showinfo("Copied", f"Copied: {truncate_text(drive_link, 50)}")
        dialog.destroy()

    def cancel():
        dialog.destroy()

    btn_frame = tk.Frame(dialog, bg=theme.get_colors()['bg'])
    btn_frame.pack(pady=20)
    tk.Button(btn_frame, text="Copy Filename", command=copy_filename, font=("Roboto", 12), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", image=copy_icon, compound="left").pack(side="left", padx=5)
    tk.Button(btn_frame, text="Copy Drive Link", command=copy_drive_link, font=("Roboto", 12), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", image=link_icon, compound="left").pack(side="left", padx=5)
    tk.Button(btn_frame, text="Cancel", command=cancel, font=("Roboto", 12), bg='#d32f2f', fg='#ffffff', activebackground='#b71c1c', relief="flat").pack(side="left", padx=5)

    dialog.bind('<Return>', lambda e: copy_filename())
    dialog.bind('d', lambda e: copy_drive_link())
    dialog.bind('<Escape>', lambda e: cancel())

    dialog.update_idletasks()
    x = root.winfo_x() + (root.winfo_width() - dialog.winfo_width()) // 2
    y = root.winfo_y() + (root.winfo_height() - dialog.winfo_height()) // 2
    dialog.geometry(f"+{x}+{y}")

# Function to handle drive link click
def on_drive_link_click(event):
    item = tree.selection()
    if not item:
        return
    tags = tree.item(item, "tags")
    if 'error' in tags:
        return
    drive_link = tags[2]  # Third tag is drive_link
    webbrowser.open(drive_link)

# Function to animate progress bar
def animate_progress(target_value, current=0, steps=20, delay=50):
    if current >= target_value:
        score_bar.configure(value=target_value)
        return
    increment = (target_value - current) / steps
    score_bar.configure(value=current + increment)
    root.after(delay, lambda: animate_progress(target_value, current + increment, steps, delay))

# Function to sort table by column
def treeview_sort_column(tree, col, reverse):
    data = [(tree.set(child, col), child) for child in tree.get_children()]
    try:
        data.sort(key=lambda x: float(x[0]) if x[0].replace('.', '', 1).isdigit() else x[0], reverse=reverse)
    except:
        data.sort(reverse=reverse)
    for index, (_, child) in enumerate(data):
        tree.move(child, '', index)
    tree.heading(col, command=lambda: treeview_sort_column(tree, col, not reverse))

# ---------------------- GUI SETUP ----------------------

root = tk.Tk()
root.title("🎬 YouTube Title to Stems Matcher")
root.geometry("1200x900")
root.minsize(800, 600)

theme = Theme()
settings = Settings()

# Load icons
search_icon = load_icon("search.png") or tk.PhotoImage(width=20, height=20)
youtube_icon = load_icon("youtube.png") or tk.PhotoImage(width=20, height=20)
link_icon = load_icon("link.png") or tk.PhotoImage(width=20, height=20)
settings_icon = load_icon("settings.png") or tk.PhotoImage(width=20, height=20)
copy_icon = load_icon("copy.png") or tk.PhotoImage(width=20, height=20)

# Configure grid weights for resizing
root.grid_rowconfigure(0, weight=1)
root.grid_columnconfigure(0, weight=1)

# Check dependencies
check_dependencies()

# Main frame with padding
main_frame = tk.Frame(root, bg=theme.get_colors()['bg'], padx=20, pady=20)
main_frame.grid(row=0, column=0, sticky="nsew")
main_frame.grid_rowconfigure(11, weight=1)
main_frame.grid_columnconfigure(0, weight=1)

# Paste YouTube URL section
url_label = tk.Label(main_frame, text="🔗 Paste YouTube URL:", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'], image=youtube_icon, compound="left")
url_label.grid(row=0, column=0, sticky="w", pady=(0, 5))

url_entry = tk.Entry(main_frame, width=60, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['entry_bg'], fg=theme.get_colors()['fg'], relief="flat", highlightthickness=1, highlightbackground='gray')
url_entry.grid(row=1, column=0, sticky="ew", pady=5)
url_entry.insert(0, "Enter YouTube URL here")
url_entry.bind("<FocusIn>", lambda e: url_entry.delete(0, tk.END) if url_entry.get() == "Enter YouTube URL here" else None)
url_entry.bind("<Return>", lambda e: on_match_click())

match_button = tk.Button(main_frame, text="Match Filename", command=on_match_click, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=search_icon, compound="left")
match_button.grid(row=2, column=0, pady=10)
match_button.bind("<Enter>", lambda e: match_button.configure(bg=theme.get_colors()['hover_bg']))
match_button.bind("<Leave>", lambda e: match_button.configure(bg=theme.get_colors()['button_bg']))

# Search by title/keyword section
search_label = tk.Label(main_frame, text="📝 Search by Title/Keyword:", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
search_label.grid(row=3, column=0, sticky="w", pady=(10, 5))

search_entry = tk.Entry(main_frame, width=60, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['entry_bg'], fg=theme.get_colors()['fg'], relief="flat", highlightthickness=1, highlightbackground='gray')
search_entry.grid(row=4, column=0, sticky="ew", pady=5)
search_entry.insert(0, "Enter title or keyword")
search_entry.bind("<FocusIn>", lambda e: search_entry.delete(0, tk.END) if search_entry.get() == "Enter title or keyword" else None)
search_entry.bind("<Return>", lambda e: search_by_title())

search_button = tk.Button(main_frame, text="Search", command=search_by_title, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=search_icon, compound="left")
search_button.grid(row=5, column=0, pady=10)
search_button.bind("<Enter>", lambda e: search_button.configure(bg=theme.get_colors()['hover_bg']))
search_button.bind("<Leave>", lambda e: search_button.configure(bg=theme.get_colors()['button_bg']))

# Search by OCD/VP number section
ocd_vp_label = tk.Label(main_frame, text="🔢 Search by OCD/VP Number:", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
ocd_vp_label.grid(row=6, column=0, sticky="w", pady=(10, 5))

ocd_vp_entry = tk.Entry(main_frame, width=60, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['entry_bg'], fg=theme.get_colors()['fg'], relief="flat", highlightthickness=1, highlightbackground='gray')
ocd_vp_entry.grid(row=7, column=0, sticky="ew", pady=5)
ocd_vp_entry.insert(0, "Enter OCD/VP number")
ocd_vp_entry.bind("<FocusIn>", lambda e: ocd_vp_entry.delete(0, tk.END) if ocd_vp_entry.get() == "Enter OCD/VP number" else None)
ocd_vp_entry.bind("<Return>", lambda e: search_by_ocd_vp())

ocd_vp_button = tk.Button(main_frame, text="Search OCD/VP", command=search_by_ocd_vp, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=search_icon, compound="left")
ocd_vp_button.grid(row=8, column=0, pady=10)
ocd_vp_button.bind("<Enter>", lambda e: ocd_vp_button.configure(bg=theme.get_colors()['hover_bg']))
ocd_vp_button.bind("<Leave>", lambda e: ocd_vp_button.configure(bg=theme.get_colors()['button_bg']))

# Search by Video ID section
video_id_label = tk.Label(main_frame, text="🎥 Search by Video ID:", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
video_id_label.grid(row=9, column=0, sticky="w", pady=(10, 5))

video_id_entry = tk.Entry(main_frame, width=60, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['entry_bg'], fg=theme.get_colors()['fg'], relief="flat", highlightthickness=1, highlightbackground='gray')
video_id_entry.grid(row=10, column=0, sticky="ew", pady=5)
video_id_entry.insert(0, "Enter Video ID")
video_id_entry.bind("<FocusIn>", lambda e: video_id_entry.delete(0, tk.END) if video_id_entry.get() == "Enter Video ID" else None)
video_id_entry.bind("<Return>", lambda e: search_by_video_id())

video_id_button = tk.Button(main_frame, text="Search Video ID", command=search_by_video_id, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=search_icon, compound="left")
video_id_button.grid(row=11, column=0, pady=10)
video_id_button.bind("<Enter>", lambda e: video_id_button.configure(bg=theme.get_colors()['hover_bg']))
video_id_button.bind("<Leave>", lambda e: video_id_button.configure(bg=theme.get_colors()['button_bg']))

# Theme and settings buttons
button_frame = tk.Frame(main_frame, bg=theme.get_colors()['bg'])
button_frame.grid(row=12, column=0, sticky="e", pady=10)
theme_button = tk.Button(button_frame, text="🌙 Toggle Theme", command=theme.toggle, font=("Roboto", settings.get_font_size()-2), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5)
theme_button.pack(side="left", padx=5)
theme_button.bind("<Enter>", lambda e: theme_button.configure(bg=theme.get_colors()['hover_bg']))
theme_button.bind("<Leave>", lambda e: theme_button.configure(bg=theme.get_colors()['button_bg']))

settings_button = tk.Button(button_frame, text="⚙️ Settings", command=open_settings, font=("Roboto", settings.get_font_size()-2), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=settings_icon, compound="left")
settings_button.pack(side="left", padx=5)
settings_button.bind("<Enter>", lambda e: settings_button.configure(bg=theme.get_colors()['hover_bg']))
settings_button.bind("<Leave>", lambda e: settings_button.configure(bg=theme.get_colors()['button_bg']))

# Match Score section
score_label = tk.Label(main_frame, text="Best Match Score: 0%", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
score_label.grid(row=13, column=0, pady=5)

score_bar = ttk.Progressbar(main_frame, orient="horizontal", length=500, mode="determinate", maximum=100)
score_bar.grid(row=14, column=0, pady=5)

# Result and timer section
result_label = tk.Label(main_frame, text="YT Title and Duration will appear here.", wraplength=700, justify="center", font=("Roboto", settings.get_font_size()+2, 'bold'), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
result_label.grid(row=15, column=0, pady=10)

# Table view for results
style = ttk.Style()
style.configure("Custom.Treeview", rowheight=40)
style.configure("Custom.Treeview.Heading", font=("Roboto", settings.get_font_size(), 'bold'))
canvas = tk.Canvas(main_frame, bg=theme.get_colors()['frame_bg'], highlightthickness=1, highlightbackground='#cccccc')
scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
tree = ttk.Treeview(canvas, columns=("Filename", "Duration", "Drive Link", "YT Duration"), show="headings", style="Custom.Treeview")

tree.heading("Filename", text="Filename", command=lambda: treeview_sort_column(tree, "Filename", False))
tree.heading("Duration", text="Duration", command=lambda: treeview_sort_column(tree, "Duration", False))
tree.heading("Drive Link", text="Drive Link", command=lambda: treeview_sort_column(tree, "Drive Link", False))
tree.heading("YT Duration", text="YT Duration", command=lambda: treeview_sort_column(tree, "YT Duration", False))

tree.column("Filename", width=400)
tree.column("Duration", width=100)
tree.column("Drive Link", width=200)
tree.column("YT Duration", width=100)

tree.tag_configure('even', background=theme.get_colors()['table_bg'])
tree.tag_configure('odd', background=theme.get_colors()['table_alt'])
tree.tag_configure('error', foreground='red')
tree.tag_configure('green', foreground='green')
tree.tag_configure('#90EE90', foreground='#90EE90')
tree.tag_configure('red', foreground='red')

tree.bind("<Button-1>", on_match_select)
tree.bind("<Double-1>", on_drive_link_click)

canvas.create_window((0, 0), window=tree, anchor="nw")
canvas.configure(yscrollcommand=scrollbar.set)
canvas.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))

canvas.grid(row=16, column=0, sticky="nsew", pady=10)
scrollbar.grid(row=16, column=1, sticky="ns")

timer_label = tk.Label(main_frame, text="Task completed in: 0 seconds", font=("Roboto", settings.get_font_size()-2), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
timer_label.grid(row=17, column=0, pady=5)

# Apply initial theme
apply_theme()

root.mainloop()