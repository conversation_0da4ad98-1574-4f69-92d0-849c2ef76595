<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app_name }} - Secure Access</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            margin-bottom: 2rem;
        }

        .logo h1 {
            color: #4a5568;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
        }

        .logo p {
            color: #718096;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #4a5568;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8fafc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            border: 1px solid #feb2b2;
        }

        .security-info {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f0f4f8;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .security-info h3 {
            color: #4a5568;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .security-info p {
            color: #718096;
            font-size: 0.8rem;
            line-height: 1.5;
        }

        .features {
            margin-top: 1.5rem;
            text-align: left;
        }

        .features ul {
            list-style: none;
            color: #718096;
            font-size: 0.85rem;
        }

        .features li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .logo h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>{{ app_name }}</h1>
            <p>Professional Media Archive Search System</p>
        </div>

        {% if error %}
        <div class="error-message">
            {{ error }}
        </div>
        {% endif %}

        <form method="POST">
            <div class="form-group">
                <label for="password">Access Password</label>
                <input type="password" id="password" name="password" required 
                       placeholder="Enter your access password" autocomplete="current-password">
            </div>
            
            <button type="submit" class="login-btn">
                Secure Access
            </button>
        </form>

        <div class="security-info">
            <h3>🔒 Enterprise Security</h3>
            <p>This system uses advanced authentication to protect sensitive media archives. Multiple users can access simultaneously with the same credentials.</p>
            
            <div class="features">
                <ul>
                    <li>Session-based authentication</li>
                    <li>8-hour automatic timeout</li>
                    <li>Multi-user concurrent access</li>
                    <li>Secure password protection</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Auto-focus password field
        document.getElementById('password').focus();
        
        // Handle form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const btn = document.querySelector('.login-btn');
            btn.innerHTML = 'Authenticating...';
            btn.disabled = true;
        });
    </script>
</body>
</html>
