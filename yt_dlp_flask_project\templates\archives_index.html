<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app_name }} v{{ app_version }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo h1 {
            color: #333;
            font-size: 1.8rem;
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        .version {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .user-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        .refresh-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .refresh-btn {
            background: #28a745;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 1rem;
        }

        .refresh-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .refresh-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            margin-top: 1rem;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .cache-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .cache-stat {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .cache-stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #495057;
        }

        .cache-stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }


        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-title {
            text-align: center;
            margin-bottom: 2rem;
        }

        .search-title h2 {
            color: #333;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .search-title p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .performance-showcase {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            margin: 0.5rem 0;
            padding: 0.5rem 1rem;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.04) 0%, rgba(118, 75, 162, 0.04) 100%);
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
            max-width: 280px;
            margin-left: auto;
            margin-right: auto;
        }

        .performance-showcase::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .performance-metric {
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .metric-number {
            font-size: 1rem;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            margin-bottom: 0.2rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .metric-label {
            font-size: 0.6rem;
            font-weight: 500;
            color: #667eea;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .performance-divider {
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            z-index: 2;
        }

        .divider-line {
            width: 15px;
            height: 1px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .divider-icon {
            font-size: 0.8rem;
            color: #667eea;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.8; }
        }

        .search-form {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .search-input {
            flex: 1;
            padding: 1rem 1.5rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            background: #f8fafc;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .search-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .search-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .info-card {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .info-card h4 {
            color: #333;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .info-card p {
            color: #666;
            font-size: 0.8rem;
        }

        .results-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: none;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .results-title {
            color: #333;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .results-meta {
            color: #666;
            font-size: 0.9rem;
        }

        .result-item {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .result-item:hover {
            background: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .result-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .result-title {
            flex: 1;
            color: #333;
            font-weight: 600;
            font-size: 1rem;
            line-height: 1.4;
        }

        .result-score {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            white-space: nowrap;
        }

        .result-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
        }

        .meta-label {
            color: #666;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
        }

        .meta-value {
            color: #333;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .result-actions {
            display: flex;
            gap: 0.75rem;
        }

        .action-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #667eea;
            background: transparent;
            color: #667eea;
            border-radius: 8px;
            text-decoration: none;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }

        .action-btn.primary {
            background: #667eea;
            color: white;
        }

        .action-btn.primary:hover {
            background: #764ba2;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 1rem;
            border-radius: 12px;
            margin: 1rem 0;
            border: 1px solid #feb2b2;
        }

        .cache-status {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.8rem;
            color: #666;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .search-form {
                flex-direction: column;
            }
            
            .search-btn {
                width: 100%;
            }
            
            .header-content {
                padding: 0 1rem;
            }
            
            .logo h1 {
                font-size: 1.4rem;
            }
            
            .result-meta {
                grid-template-columns: 1fr;
            }
            
            .result-actions {
                flex-direction: column;
            }

            .performance-showcase {
                flex-direction: column;
                gap: 0.6rem;
                padding: 0.6rem;
                max-width: 220px;
            }

            .performance-divider {
                transform: rotate(90deg);
            }

            .metric-number {
                font-size: 0.9rem;
            }

            .metric-label {
                font-size: 0.55rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>{{ app_name }}</h1>
                <span class="version">v{{ app_version }}</span>
            </div>
            <div class="user-actions">
                <a href="/logout" class="logout-btn">Logout</a>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="search-section">
            <div class="search-title">
                <h2>Professional Media Archive Search</h2>
                <p>Advanced search capabilities across all media catalogs</p>


            </div>

            <form class="search-form" id="searchForm">
                <input type="text" class="search-input" id="searchInput"
                       placeholder="Enter video ID, OCD/VP number, YouTube URL, or search terms..."
                       autocomplete="off">
                <button type="submit" class="search-btn" id="searchBtn">Search</button>
            </form>

            <!-- Performance Showcase -->
            <div class="performance-showcase">
                <div class="performance-metric">
                    <div class="metric-number">21,000+</div>
                    <div class="metric-label">Media Stems</div>
                </div>
                <div class="performance-divider">
                    <div class="divider-line"></div>
                    <div class="divider-icon">⚡</div>
                    <div class="divider-line"></div>
                </div>
                <div class="performance-metric">
                    <div class="metric-number">&lt; 5s</div>
                    <div class="metric-label">Search Time</div>
                </div>
            </div>

            <div class="search-info">
                <div class="info-card">
                    <h4>🎯 Precise Search</h4>
                    <p>Video IDs, OCD/VP numbers, and URLs for exact matches</p>
                </div>
                <div class="info-card">
                    <h4>⚡ Ultra Fast</h4>
                    <p>Sub-2-second search across 21,000+ media files</p>
                </div>
                <div class="info-card">
                    <h4>🔗 Direct Access</h4>
                    <p>Instant Google Drive links for immediate access</p>
                </div>
                <div class="info-card">
                    <h4>📊 Multi-Source</h4>
                    <p>Searches across all 8 catalog sheets simultaneously</p>
                </div>
            </div>

            <!-- Cache Refresh Section -->
            <div class="refresh-section">
                <h3 style="margin-bottom: 1rem; color: #495057;">📂 Cache Management</h3>

                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                    <button id="refreshBtn" class="refresh-btn" onclick="refreshCache()">
                        🔄 Refresh Cache Now
                    </button>
                    <span id="refreshStatus" style="color: #6c757d;"></span>
                </div>

                <div id="progressContainer" class="progress-container">
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill"></div>
                    </div>
                    <div id="progressText" style="text-align: center; margin-top: 0.5rem; color: #6c757d;"></div>
                </div>

                <div class="cache-info">
                    <div class="cache-stat">
                        <div id="cacheSize" class="cache-stat-value">Loading...</div>
                        <div class="cache-stat-label">Total Records</div>
                    </div>
                    <div class="cache-stat">
                        <div id="lastUpdated" class="cache-stat-value">Loading...</div>
                        <div class="cache-stat-label">Last Updated</div>
                    </div>
                    <div class="cache-stat">
                        <div id="nextRefresh" class="cache-stat-value">Loading...</div>
                        <div class="cache-stat-label">Next Auto Refresh</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="results-section" id="resultsSection">
            <div class="results-header">
                <h3 class="results-title">Search Results</h3>
                <div class="results-meta" id="resultsMeta"></div>
            </div>
            <div id="resultsContainer"></div>
        </div>
    </div>

    <div class="cache-status" id="cacheStatus">
        <div>Cache Status: <span id="cacheInfo">Loading...</span></div>
    </div>

    <script>
        // Cache status update
        function updateCacheStatus() {
            fetch('/cache/status')
                .then(response => response.json())
                .then(data => {
                    const cacheInfo = document.getElementById('cacheInfo');
                    if (data.cache_loaded) {
                        cacheInfo.textContent = `${data.cache_size.toLocaleString()} records loaded`;
                        cacheInfo.style.color = '#38a169';
                    } else {
                        cacheInfo.textContent = 'Cache not loaded';
                        cacheInfo.style.color = '#e53e3e';
                    }

                    // Update cache management section
                    updateCacheManagement(data);
                })
                .catch(error => {
                    document.getElementById('cacheInfo').textContent = 'Status unavailable';
                });
        }

        // Update cache management section
        function updateCacheManagement(data) {
            document.getElementById('cacheSize').textContent = data.cache_size ? data.cache_size.toLocaleString() : 'N/A';

            if (data.last_updated) {
                const lastUpdated = new Date(data.last_updated);
                document.getElementById('lastUpdated').textContent = lastUpdated.toLocaleString();
            } else {
                document.getElementById('lastUpdated').textContent = 'N/A';
            }

            if (data.next_refresh) {
                const nextRefresh = new Date(data.next_refresh);
                document.getElementById('nextRefresh').textContent = nextRefresh.toLocaleString();
            } else {
                document.getElementById('nextRefresh').textContent = 'N/A';
            }
        }

        // Manual cache refresh
        function refreshCache() {
            const refreshBtn = document.getElementById('refreshBtn');
            const refreshStatus = document.getElementById('refreshStatus');
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            // Disable button and show progress
            refreshBtn.disabled = true;
            refreshBtn.textContent = '🔄 Refreshing...';
            refreshStatus.textContent = '';
            progressContainer.style.display = 'block';

            // Simulate progress
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += 10;
                progressFill.style.width = progress + '%';
                progressText.textContent = `Refreshing cache... ${progress}%`;

                if (progress >= 90) {
                    clearInterval(progressInterval);
                }
            }, 200);

            // Perform refresh
            fetch('/refresh-cache', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressFill.style.width = '100%';
                progressText.textContent = 'Refresh complete!';

                if (data.success) {
                    refreshStatus.textContent = `✅ Success! ${data.cache_size.toLocaleString()} records in ${data.elapsed_time}s`;
                    refreshStatus.style.color = '#28a745';
                    updateCacheStatus(); // Refresh the cache status
                } else {
                    refreshStatus.textContent = `❌ Failed: ${data.message}`;
                    refreshStatus.style.color = '#dc3545';
                }
            })
            .catch(error => {
                clearInterval(progressInterval);
                refreshStatus.textContent = `❌ Error: ${error.message}`;
                refreshStatus.style.color = '#dc3545';
            })
            .finally(() => {
                // Reset button and hide progress after delay
                setTimeout(() => {
                    refreshBtn.disabled = false;
                    refreshBtn.textContent = '🔄 Refresh Cache Now';
                    progressContainer.style.display = 'none';
                    progressFill.style.width = '0%';
                }, 2000);
            });
        }

        // Search functionality
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });

        function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) return;

            const searchBtn = document.getElementById('searchBtn');
            const resultsSection = document.getElementById('resultsSection');
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsMeta = document.getElementById('resultsMeta');

            // Show loading state
            searchBtn.disabled = true;
            searchBtn.textContent = 'Searching...';
            resultsSection.style.display = 'block';
            resultsContainer.innerHTML = '<div class="loading">🔍 Searching archives...</div>';

            // Perform search
            const formData = new FormData();
            formData.append('query', query);

            fetch('/search', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayResults(data);
                } else {
                    showError(data.error || 'Search failed');
                }
            })
            .catch(error => {
                showError('Network error: ' + error.message);
            })
            .finally(() => {
                searchBtn.disabled = false;
                searchBtn.textContent = 'Search';
            });
        }

        function displayResults(data) {
            const resultsContainer = document.getElementById('resultsContainer');
            const resultsMeta = document.getElementById('resultsMeta');

            resultsMeta.textContent = `${data.total_matches} results in ${data.elapsed_time}s`;

            if (data.results.length === 0) {
                resultsContainer.innerHTML = '<div class="loading">No results found. Try different search terms.</div>';
                return;
            }

            const resultsHTML = data.results.map(result => `
                <div class="result-item">
                    <div class="result-header">
                        <div class="result-title">${escapeHtml(result.filename)}</div>
                        <div class="result-score">${result.score}% match</div>
                    </div>
                    <div class="result-meta">
                        <div class="meta-item">
                            <div class="meta-label">Duration</div>
                            <div class="meta-value">${result.duration || 'N/A'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">OCD/VP</div>
                            <div class="meta-value">${result.ocd_vp || 'N/A'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Video ID</div>
                            <div class="meta-value">${result.video_id || 'N/A'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Source</div>
                            <div class="meta-value">${result.sheet_name}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">Search Type</div>
                            <div class="meta-value">${result.search_type}</div>
                        </div>
                    </div>
                    <div class="result-actions">
                        <a href="${result.drive_link}" target="_blank" class="action-btn primary">
                            📁 Open in Drive
                        </a>
                        ${result.video_id ? `<button onclick="copyToClipboard('${result.video_id}')" class="action-btn">📋 Copy Video ID</button>` : ''}
                        ${result.ocd_vp ? `<button onclick="copyToClipboard('${result.ocd_vp}')" class="action-btn">📋 Copy OCD/VP</button>` : ''}
                    </div>
                </div>
            `).join('');

            resultsContainer.innerHTML = resultsHTML;
        }

        function showError(message) {
            const resultsContainer = document.getElementById('resultsContainer');
            resultsContainer.innerHTML = `<div class="error">❌ ${escapeHtml(message)}</div>`;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show temporary feedback
                const originalText = event.target.textContent;
                event.target.textContent = '✓ Copied!';
                setTimeout(() => {
                    event.target.textContent = originalText;
                }, 1500);
            });
        }

        // Initialize
        updateCacheStatus();
        setInterval(updateCacheStatus, 30000); // Update every 30 seconds

        // Auto-focus search input
        document.getElementById('searchInput').focus();
    </script>
</body>
</html>
