#!/usr/bin/env python3
"""
Archives Stems Finder Pro - FAST VERSION (Cache Only)
Optimized for maximum search performance under 2 seconds
"""

import os
import time
import logging
import pandas as pd
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
import re
from flask import Flask, request, jsonify, render_template, session, redirect, url_for
# from fuzzywuzzy import fuzz  # Removed for speed
import json
import secrets

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lightning_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ArchivesConfig:
    """Configuration for Archives Stems Finder Pro"""
    APP_NAME = "Archives Stems Finder Pro"
    APP_VERSION = "2.0 FAST"
    SECRET_KEY = secrets.token_hex(32)
    ACCESS_PASSWORD = "Shiva@123"
    SESSION_TIMEOUT_HOURS = 8
    CACHE_FILE = 'archives_cache.csv'
    MAX_RESULTS = 10
    GOOGLE_DRIVE_FOLDER = "https://drive.google.com/drive/folders/1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG"

# Global variables
app = Flask(__name__)
app.secret_key = ArchivesConfig.SECRET_KEY
cached_df = None
cache_last_updated = None

def load_cache():
    """Load cache from file - OPTIMIZED FOR SPEED"""
    global cached_df, cache_last_updated
    
    if os.path.exists(ArchivesConfig.CACHE_FILE):
        try:
            start_time = time.time()
            cached_df = pd.read_csv(ArchivesConfig.CACHE_FILE)
            cache_last_updated = datetime.fromtimestamp(os.path.getmtime(ArchivesConfig.CACHE_FILE))
            elapsed = time.time() - start_time
            logging.info(f"⚡ FAST Cache loaded: {len(cached_df)} rows in {elapsed:.3f}s")
            return True
        except Exception as e:
            logging.error(f"Error loading cache: {str(e)}")
    
    return False

def ensure_cache_ready():
    """Ensure cache is ready - FAST VERSION"""
    global cached_df
    if cached_df is None:
        load_cache()

# Authentication functions
def check_password(password):
    return password == ArchivesConfig.ACCESS_PASSWORD

def is_authenticated():
    if 'authenticated' not in session:
        return False
    if 'login_time' in session:
        login_time = datetime.fromisoformat(session['login_time'])
        if datetime.now() - login_time > timedelta(hours=ArchivesConfig.SESSION_TIMEOUT_HOURS):
            session.clear()
            return False
    return session.get('authenticated', False)

def require_auth(f):
    def decorated_function(*args, **kwargs):
        if not is_authenticated():
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def extract_video_id_from_url(url):
    """Extract video ID from various social media URLs - OPTIMIZED"""
    import re
    
    # Instagram patterns
    instagram_patterns = [
        r'instagram\.com/p/([A-Za-z0-9_-]+)',
        r'instagram\.com/reel/([A-Za-z0-9_-]+)'
    ]
    
    for pattern in instagram_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    # YouTube patterns
    youtube_patterns = [
        r'youtube\.com/watch\?v=([A-Za-z0-9_-]+)',
        r'youtu\.be/([A-Za-z0-9_-]+)',
        r'youtube\.com/embed/([A-Za-z0-9_-]+)',
        r'youtube\.com/shorts/([A-Za-z0-9_-]+)'
    ]
    
    for pattern in youtube_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None

def detect_search_type(query):
    """Detect search type - OPTIMIZED"""
    original_query = query.strip()
    query = query.strip().upper()
    
    # Check for URLs first
    if any(domain in original_query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
        video_id = extract_video_id_from_url(original_query)
        if video_id:
            return 'video_id'
        return 'url'
    
    # Check for OCD/VP numbers
    if query.startswith('OCD-') or query.startswith('VP-') or (query.startswith(('OCD', 'VP')) and any(c.isdigit() for c in query)):
        return 'ocd_number'
    
    # Check for Video ID (11 characters)
    if len(query) == 11 and query.replace('_', '').replace('-', '').isalnum():
        return 'video_id'
    
    return 'title_search'

def search_by_video_id(video_id):
    """Search by video ID - ULTRA FAST"""
    global cached_df
    matches = []
    
    # Use vectorized operations for speed
    if 'video_ids' in cached_df.columns:
        mask = cached_df['video_ids'].astype(str).str.contains(video_id, na=False, regex=False)
        matching_rows = cached_df[mask]
        
        for _, row in matching_rows.iterrows():
            matches.append({
                'filename': row['filename'],
                'duration': row['duration'],
                'duration_seconds': row.get('duration_seconds', 0),
                'ocd_vp': row['ocd_vp'],
                'video_id': video_id,
                'sheet_name': row.get('sheet_name', 'Unknown'),
                'score': 100,
                'search_type': 'Video ID Match'
            })
    
    return matches

def search_by_ocd_vp(query):
    """Search by OCD/VP - ULTRA FAST"""
    global cached_df
    matches = []
    
    query_clean = query.upper().replace('-', '').replace(' ', '')
    
    # Use vectorized operations
    mask = (cached_df['ocd_vp'].astype(str).str.upper().str.contains(query, na=False, regex=False) |
            cached_df['video_id'].astype(str).str.upper().str.contains(query, na=False, regex=False))
    
    matching_rows = cached_df[mask]
    
    for _, row in matching_rows.iterrows():
        matches.append({
            'filename': row['filename'],
            'duration': row['duration'],
            'duration_seconds': row.get('duration_seconds', 0),
            'ocd_vp': row['ocd_vp'],
            'video_id': row['video_id'],
            'sheet_name': row.get('sheet_name', 'Unknown'),
            'score': 100,
            'search_type': 'OCD/VP Match'
        })
    
    return matches

def search_by_title(query):
    """Search by title - OPTIMIZED FOR SPEED"""
    global cached_df
    matches = []
    
    query_clean = query.lower().strip()
    
    # Use vectorized string operations for speed
    mask = cached_df['filename'].astype(str).str.lower().str.contains(query_clean, na=False, regex=False)
    matching_rows = cached_df[mask].head(ArchivesConfig.MAX_RESULTS)
    
    for _, row in matching_rows.iterrows():
        filename = str(row['filename']).lower()
        # Simple scoring based on exact match
        if query_clean in filename:
            score = 100
        else:
            score = 50

        matches.append({
            'filename': row['filename'],
            'duration': row['duration'],
            'duration_seconds': row.get('duration_seconds', 0),
            'ocd_vp': row['ocd_vp'],
            'video_id': row['video_id'],
            'sheet_name': row.get('sheet_name', 'Unknown'),
            'score': score,
            'search_type': 'Title Match'
        })
    
    # Sort by score
    matches.sort(key=lambda x: x['score'], reverse=True)
    return matches

def archives_search(query, max_results=10):
    """ULTRA FAST search function - optimized for sub-2-second performance"""
    global cached_df
    
    start_time = time.time()
    ensure_cache_ready()
    
    if cached_df is None or cached_df.empty:
        return []
    
    search_type = detect_search_type(query)
    logging.info(f"⚡ FAST search: '{query}' (type: {search_type})")
    
    results = []
    
    if search_type == 'video_id':
        if any(domain in query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
            extracted_video_id = extract_video_id_from_url(query)
            if extracted_video_id:
                results = search_by_video_id(extracted_video_id)
            else:
                results = search_by_title(query)
        else:
            results = search_by_video_id(query)
    elif search_type == 'ocd_number':
        results = search_by_ocd_vp(query)
    else:
        results = search_by_title(query)
    
    results = results[:max_results]
    elapsed = time.time() - start_time
    logging.info(f"🚀 FAST search completed in {elapsed:.3f}s - found {len(results)} matches")
    
    return results

# Flask routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        password = request.form.get('password', '')
        if check_password(password):
            session['authenticated'] = True
            session['login_time'] = datetime.now().isoformat()
            return redirect(url_for('index'))
        else:
            return render_template('login.html', error='Invalid password', app_name=ArchivesConfig.APP_NAME)
    return render_template('login.html', app_name=ArchivesConfig.APP_NAME)

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/')
@require_auth
def index():
    return render_template('archives_index.html', app_name=ArchivesConfig.APP_NAME, app_version=ArchivesConfig.APP_VERSION)

@app.route('/search', methods=['POST'])
@require_auth
def search():
    try:
        query = request.form.get('query', '').strip()
        if not query:
            return jsonify({'error': 'Query is required'}), 400
        
        start_time = time.time()
        results = archives_search(query, max_results=ArchivesConfig.MAX_RESULTS)
        
        formatted_results = []
        for result in results:
            ocd_vp = str(result.get('ocd_vp', '')).strip()
            video_id = str(result.get('video_id', '')).strip()
            
            if ocd_vp and ocd_vp not in ['nan', 'NaN', '']:
                drive_link = f"https://drive.google.com/drive/search?q={ocd_vp}"
            elif video_id and video_id not in ['nan', 'NaN', '']:
                drive_link = f"https://drive.google.com/drive/search?q={video_id}"
            else:
                drive_link = f"{ArchivesConfig.GOOGLE_DRIVE_FOLDER}?q={result['filename'].replace(' ', '+')}"
            
            def safe_str(value):
                if pd.isna(value) or str(value).lower() in ['nan', 'none', 'null']:
                    return ''
                return str(value)
            
            def safe_float(value):
                try:
                    if pd.isna(value) or str(value).lower() in ['nan', 'none', 'null']:
                        return 0.0
                    return float(value)
                except (ValueError, TypeError):
                    return 0.0
            
            formatted_results.append({
                'filename': safe_str(result['filename']),
                'duration': safe_str(result['duration']),
                'ocd_vp': safe_str(result['ocd_vp']),
                'video_id': safe_str(result['video_id']),
                'sheet_name': safe_str(result['sheet_name']),
                'score': round(safe_float(result['score']), 1),
                'search_type': safe_str(result['search_type']),
                'drive_link': drive_link
            })
        
        elapsed_time = time.time() - start_time
        
        return jsonify({
            'success': True,
            'query': query,
            'results': formatted_results,
            'total_matches': len(formatted_results),
            'elapsed_time': round(elapsed_time, 3),
            'search_type': detect_search_type(query)
        })
        
    except Exception as e:
        logging.error(f"Error in search: {str(e)}")
        return jsonify({'error': f'Search failed: {str(e)}'}), 500

@app.route('/cache/status')
@require_auth
def cache_status():
    global cached_df, cache_last_updated
    ensure_cache_ready()
    
    return jsonify({
        'cache_loaded': cached_df is not None,
        'cache_size': len(cached_df) if cached_df is not None else 0,
        'last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
        'cache_file_exists': os.path.exists(ArchivesConfig.CACHE_FILE)
    })

if __name__ == '__main__':
    logging.info(f"🚀 Starting {ArchivesConfig.APP_NAME} v{ArchivesConfig.APP_VERSION}")
    logging.info(f"Cache file: {ArchivesConfig.CACHE_FILE}")
    logging.info(f"Google Drive folder: {ArchivesConfig.GOOGLE_DRIVE_FOLDER}")
    
    # Load cache immediately for maximum speed
    ensure_cache_ready()
    
    # Start Flask app
    app.run(host='0.0.0.0', port=8080, debug=True)
