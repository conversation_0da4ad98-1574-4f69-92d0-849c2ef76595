#!/usr/bin/env python3
"""
Debug the query processing logic
"""

import re

def process_search_query(query):
    """
    Advanced query processing with multi-step algorithm:
    1. Remove special characters
    2. Replace spaces with hyphens
    3. Case-insensitive search
    4. Split query if no match
    5. Fuzzy search fallback
    """
    
    # Step 1: Remove all special characters, keep only alphanumeric and spaces
    cleaned_query = re.sub(r'[^\w\s]', '', query)
    
    # Step 2: Replace spaces with hyphens
    processed_query = cleaned_query.strip().replace(' ', '-')
    
    # Step 3: Create variations for better matching
    variations = [
        processed_query,  # Full processed query
        processed_query.lower(),  # Lowercase version
        processed_query.title(),  # Title case version
    ]
    
    # Step 4: If query has more than 2 words, create split variations
    words = cleaned_query.strip().split()
    if len(words) > 2:
        # First part: up to first 2-3 words
        first_part = '-'.join(words[:2])
        second_part = '-'.join(words[2:])
        
        # Also try 3-word split
        if len(words) > 3:
            first_part_3 = '-'.join(words[:3])
            second_part_3 = '-'.join(words[3:])
            variations.extend([first_part_3, second_part_3])
        
        variations.extend([first_part, second_part])
    
    # Step 5: Add individual words for fuzzy matching
    for word in words:
        if len(word) > 3:  # Only meaningful words
            variations.append(word)
    
    return {
        'original': query,
        'processed': processed_query,
        'variations': variations,
        'words': words
    }

def test_query_processing():
    """Test the query processing logic"""
    
    print("🔧 TESTING QUERY PROCESSING LOGIC")
    print("=" * 60)
    
    test_queries = [
        'A tip to prevent cancer?',
        'Why is God giving problems?',
        "Who will receive God's grace?",
        'Find Answers to Everything'
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        result = process_search_query(query)
        
        print(f"   Original: '{result['original']}'")
        print(f"   Processed: '{result['processed']}'")
        print(f"   Words: {result['words']}")
        print(f"   Variations: {result['variations']}")
        
        # Check if processing looks correct
        expected_patterns = {
            'A tip to prevent cancer?': 'A-tip-to-prevent-cancer',
            'Why is God giving problems?': 'Why-is-God-giving-problems',
            "Who will receive God's grace?": 'Who-will-receive-Gods-grace',
            'Find Answers to Everything': 'Find-Answers-to-Everything'
        }
        
        expected = expected_patterns.get(query)
        if expected:
            if result['processed'] == expected:
                print(f"   ✅ Processing correct: '{expected}'")
            else:
                print(f"   ❌ Processing incorrect: expected '{expected}', got '{result['processed']}'")

def test_filename_matching():
    """Test if our processed queries would match the target filenames"""
    
    print(f"\n🎯 TESTING FILENAME MATCHING")
    print("=" * 60)
    
    # Target filenames from the cache
    target_filenames = [
        'Insta-Reels_Can-We-Prevent-Cancer_14-Feb-2025_Tamil_01Min-53Secs_Stems',
        'Insta-Reels_Why-Is-God-Giving-Problems_15-Apr-2025_Tamil_55Secs_Stems',
        'Q-And-A_On-Receiving-Grace-Guru-Waiting-For-Someone-To-Receive-Him_23-Apr-2024_English_16Mins-08Secs_Stems',
        'Insta-Reels_You-Should-Not-Love-Shiva_08-Aug-2024_English_50Secs_Stems'
    ]
    
    test_queries = [
        'A tip to prevent cancer?',
        'Why is God giving problems?',
        "Who will receive God's grace?",
        'Find Answers to Everything'
    ]
    
    for i, query in enumerate(test_queries):
        print(f"\n🔍 Query: '{query}'")
        target = target_filenames[i]
        print(f"   Target: '{target}'")
        
        result = process_search_query(query)
        
        # Test if any variation would match the target filename
        found_match = False
        for variation in result['variations']:
            if variation.lower() in target.lower():
                print(f"   ✅ MATCH: '{variation}' found in target")
                found_match = True
                break
        
        if not found_match:
            print(f"   ❌ NO MATCH: None of the variations found in target")
            print(f"   💡 Variations tested: {result['variations'][:5]}...")
            
            # Check what parts of the target might match
            target_parts = target.replace('_', '-').replace('-', ' ').lower().split()
            query_words = [w.lower() for w in result['words']]
            
            common_words = set(target_parts) & set(query_words)
            if common_words:
                print(f"   💡 Common words found: {common_words}")
            else:
                print(f"   💡 No common words between query and target")

if __name__ == "__main__":
    print("🚀 QUERY PROCESSING DEBUG")
    print("Testing the advanced query processing logic")
    
    # Test query processing
    test_query_processing()
    
    # Test filename matching
    test_filename_matching()
    
    print("\n" + "=" * 60)
    print("🎯 QUERY PROCESSING DEBUG COMPLETE")
    print("=" * 60)
    
    print("✅ Query processing logic tested")
    print("💡 Check results above to identify matching issues")
    
    print("\n🚀 Query processing debug complete!")
