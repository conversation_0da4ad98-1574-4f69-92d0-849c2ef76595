#!/usr/bin/env python3
"""
Simple test to check if Flask works
"""

print("🔍 Testing Flask application...")

try:
    from flask import Flask
    print("✅ Flask imported")
    
    app = Flask(__name__)
    print("✅ Flask app created")
    
    @app.route('/')
    def hello():
        return "Hello World! Flask is working!"
    
    print("✅ Route defined")
    print("🚀 Starting Flask app on http://127.0.0.1:8080")
    
    app.run(host='0.0.0.0', port=8080, debug=True)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
