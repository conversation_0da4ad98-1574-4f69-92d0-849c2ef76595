#!/usr/bin/env python3
"""
Direct test of search function to debug synthetic matches
"""

import pandas as pd
import sys
import os

# Add the current directory to Python path to import the search function
sys.path.append('.')

def test_direct_search():
    """Test the search function directly"""
    
    print("🔍 DIRECT SEARCH FUNCTION TEST")
    print("=" * 50)
    
    # Load the cache file directly
    try:
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded cache: {len(df)} rows")
        
        # Check if synthetic matches are in the DataFrame
        synthetic_test_ids = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'Bm-QyuLHutC']
        
        for video_id in synthetic_test_ids:
            matches = df[df['video_id'] == video_id]
            print(f"🔍 {video_id}: {len(matches)} direct matches in DataFrame")
            
            if len(matches) > 0:
                match = matches.iloc[0]
                print(f"   📄 {match['filename'][:50]}...")
                print(f"   🔢 OCD/VP: {match['ocd_vp']}")
                print(f"   📋 Sheet: {match['sheet_name']}")
        
        # Test the search logic manually
        print(f"\n🔍 MANUAL SEARCH LOGIC TEST")
        
        query = "CPN-zOup_uS"
        print(f"Testing query: {query}")
        
        # Simulate the search function logic
        matches = []
        
        for idx, row in df.iterrows():
            found = False
            score = 0
            match_type = ""
            
            # Convert all fields to strings for searching and handle NaN values
            filename = str(row.get('filename', '')).strip() if pd.notna(row.get('filename')) else ''
            ocd_vp = str(row.get('ocd_vp', '')).strip() if pd.notna(row.get('ocd_vp')) else ''
            video_id = str(row.get('video_id', '')).strip() if pd.notna(row.get('video_id')) else ''
            sheet_name = str(row.get('sheet_name', '')).strip() if pd.notna(row.get('sheet_name')) else ''
            
            # Check exact video ID match
            if query == video_id:
                found = True
                score = 95
                match_type = "Video ID Exact"
                print(f"   ✅ EXACT MATCH FOUND: {video_id} in {filename[:30]}...")
                
                matches.append({
                    'filename': filename,
                    'ocd_vp': ocd_vp,
                    'video_id': video_id,
                    'sheet_name': sheet_name,
                    'score': score,
                    'match_type': match_type
                })
                break  # Found it, stop searching
        
        print(f"Manual search found {len(matches)} matches")
        
        if len(matches) > 0:
            print("✅ Manual search logic works - synthetic matches should be found!")
        else:
            print("❌ Manual search logic failed - there's an issue with the search")
            
            # Debug: Check if the video ID exists at all
            exact_matches = df[df['video_id'] == query]
            print(f"Debug: Exact video_id matches in DataFrame: {len(exact_matches)}")
            
            if len(exact_matches) > 0:
                print("The video ID exists in DataFrame but search logic didn't find it")
                match = exact_matches.iloc[0]
                print(f"   Video ID in DF: '{match['video_id']}'")
                print(f"   Query: '{query}'")
                print(f"   Equal? {query == match['video_id']}")
                print(f"   Types: query={type(query)}, video_id={type(match['video_id'])}")
        
        return len(matches) > 0
        
    except Exception as e:
        print(f"❌ Error in direct search test: {e}")
        return False

def test_cache_loading():
    """Test if the cache is being loaded correctly"""
    
    print(f"\n📊 CACHE LOADING TEST")
    print("=" * 50)
    
    try:
        # Load cache the same way the app does
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Cache loaded: {len(df)} rows")
        
        # Check for synthetic patterns
        synthetic_patterns = df[df['filename'].astype(str).str.contains('Archive_Media_File', na=False)]
        print(f"📊 Rows with 'Archive_Media_File': {len(synthetic_patterns)}")
        
        # Check specific synthetic video IDs
        synthetic_video_ids = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'Bm-QyuLHutC']
        
        for video_id in synthetic_video_ids:
            matches = df[df['video_id'] == video_id]
            print(f"🔍 {video_id}: {len(matches)} matches")
            
            if len(matches) > 0:
                match = matches.iloc[0]
                print(f"   Type of video_id: {type(match['video_id'])}")
                print(f"   Value: '{match['video_id']}'")
                print(f"   Filename: {match['filename'][:40]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in cache loading test: {e}")
        return False

if __name__ == "__main__":
    print("🔧 DIRECT SEARCH FUNCTION DEBUGGING")
    print("Testing search logic directly on cache data")
    
    # Test cache loading
    cache_ok = test_cache_loading()
    
    # Test direct search
    search_ok = test_direct_search()
    
    print("\n" + "=" * 50)
    print("🎯 DIRECT TEST RESULTS")
    print("=" * 50)
    
    print(f"✅ Cache loading: {cache_ok}")
    print(f"✅ Direct search: {search_ok}")
    
    if cache_ok and search_ok:
        print("\n🎉 SUCCESS: Direct search logic works!")
        print("💡 The issue might be in the Flask app search endpoint")
    elif cache_ok and not search_ok:
        print("\n🔧 ISSUE: Cache loads but search logic fails")
        print("💡 Need to debug the search algorithm")
    else:
        print("\n❌ ISSUE: Cache loading problems")
        print("💡 Need to debug cache loading")
    
    print("\n🚀 Direct debugging complete!")
