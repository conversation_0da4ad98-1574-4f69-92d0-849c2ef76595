#!/usr/bin/env python3
"""
Super Fast Stems Finder - CSV Based System
Lightning fast search in under 1 second!
"""

import os
import json
import time
import logging
import pandas as pd
import requests
from datetime import datetime
from urllib.parse import urlparse
import re
from flask import Flask, request, jsonify, render_template
import yt_dlp
from fuzzywuzzy import fuzz

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fast_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class FastConfig:
    """Configuration for super fast CSV-based system"""
    
    # Google Sheets CSV Export URLs (much faster than API)
    GOOGLE_SHEET_ID = "1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE"
    
    # CSV Export URLs for each sheet (instant download)
    SHEET_CSV_URLS = {
        "Edited Main Sheet": f"https://docs.google.com/spreadsheets/d/{GOOGLE_SHEET_ID}/export?format=csv&gid=0",
        "Social Media Catalog(SG)": f"https://docs.google.com/spreadsheets/d/{GOOGLE_SHEET_ID}/export?format=csv&gid=1234567890",
        "Social Media Catalog(IF)": f"https://docs.google.com/spreadsheets/d/{GOOGLE_SHEET_ID}/export?format=csv&gid=1234567891", 
        "Social Media Catalog(IG)": f"https://docs.google.com/spreadsheets/d/{GOOGLE_SHEET_ID}/export?format=csv&gid=1234567892",
        "Social Media Catalog(CP)": f"https://docs.google.com/spreadsheets/d/{GOOGLE_SHEET_ID}/export?format=csv&gid=1234567893",
        "Copy Social Media Catalog(SG)": f"https://docs.google.com/spreadsheets/d/{GOOGLE_SHEET_ID}/export?format=csv&gid=1234567894",
        "Copy Social Media Catalog(IF)": f"https://docs.google.com/spreadsheets/d/{GOOGLE_SHEET_ID}/export?format=csv&gid=1234567895",
        "Copy Social Media Catalog(IG)": f"https://docs.google.com/spreadsheets/d/{GOOGLE_SHEET_ID}/export?format=csv&gid=1234567896"
    }
    
    # Cache settings
    CACHE_FILE = 'fast_cache.csv'
    CACHE_DURATION_HOURS = 24
    
    # Search settings
    MAX_RESULTS = 10
    DURATION_TOLERANCE = 30  # seconds
    
    # Google Drive
    GOOGLE_DRIVE_FOLDER = "https://drive.google.com/drive/folders/1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG"

# Global variables
app = Flask(__name__)
cached_df = None
cache_last_updated = None

def download_csv_from_url(url, sheet_name):
    """Download CSV data from Google Sheets export URL"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # Parse CSV data
        from io import StringIO
        csv_data = StringIO(response.text)
        df = pd.read_csv(csv_data)
        
        # Add sheet name column
        df['sheet_name'] = sheet_name
        
        logging.info(f"Downloaded {len(df)} rows from {sheet_name}")
        return df
        
    except Exception as e:
        logging.error(f"Error downloading CSV from {sheet_name}: {str(e)}")
        return pd.DataFrame()

def build_fast_cache():
    """Build cache by downloading all CSV files - SUPER FAST!"""
    global cached_df, cache_last_updated
    
    start_time = time.time()
    logging.info("Building FAST cache from CSV exports...")
    
    all_dataframes = []
    
    # Download all sheets in parallel for maximum speed
    with ThreadPoolExecutor(max_workers=8) as executor:
        future_to_sheet = {
            executor.submit(download_csv_from_url, url, sheet_name): sheet_name
            for sheet_name, url in FastConfig.SHEET_CSV_URLS.items()
        }
        
        for future in future_to_sheet:
            sheet_name = future_to_sheet[future]
            try:
                df = future.result()
                if not df.empty:
                    all_dataframes.append(df)
                    logging.info(f"✓ {sheet_name}: {len(df)} rows")
            except Exception as e:
                logging.error(f"✗ {sheet_name}: {str(e)}")
    
    if all_dataframes:
        # Combine all dataframes
        cached_df = pd.concat(all_dataframes, ignore_index=True)
        
        # Clean and standardize data
        cached_df = clean_dataframe(cached_df)
        
        # Save to local cache
        cached_df.to_csv(FastConfig.CACHE_FILE, index=False)
        cache_last_updated = datetime.now()
        
        elapsed = time.time() - start_time
        logging.info(f"🚀 FAST cache built! {len(cached_df)} total rows in {elapsed:.2f}s")
        return True
    else:
        logging.error("Failed to build cache - no data downloaded")
        return False

def clean_dataframe(df):
    """Clean and standardize the dataframe"""
    try:
        # Ensure required columns exist
        required_columns = ['filename', 'duration', 'ocd_vp', 'video_id', 'sheet_name']
        for col in required_columns:
            if col not in df.columns:
                df[col] = ''
        
        # Clean filename
        df['filename'] = df['filename'].fillna('').astype(str)
        
        # Clean duration and convert to seconds
        df['duration_seconds'] = df['duration'].apply(parse_duration_to_seconds)
        
        # Clean OCD/VP numbers
        df['ocd_vp'] = df['ocd_vp'].fillna('').astype(str)
        
        # Clean video IDs
        df['video_id'] = df['video_id'].fillna('').astype(str)
        
        # Remove empty rows
        df = df[df['filename'].str.len() > 0]
        
        logging.info(f"Cleaned dataframe: {len(df)} valid rows")
        return df
        
    except Exception as e:
        logging.error(f"Error cleaning dataframe: {str(e)}")
        return df

def parse_duration_to_seconds(duration_str):
    """Parse duration string to seconds"""
    try:
        if pd.isna(duration_str) or duration_str == '':
            return 0
            
        duration_str = str(duration_str).strip()
        
        # Handle formats like "0:12:28", "12:28", "1:05"
        if ':' in duration_str:
            parts = duration_str.split(':')
            if len(parts) == 3:  # H:M:S
                return int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
            elif len(parts) == 2:  # M:S
                return int(parts[0]) * 60 + int(parts[1])
        
        return 0
    except:
        return 0

def load_cache():
    """Load cache from file if exists and valid"""
    global cached_df, cache_last_updated
    
    if os.path.exists(FastConfig.CACHE_FILE):
        try:
            # Check if cache is still valid
            file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(FastConfig.CACHE_FILE))
            if file_age.total_seconds() < FastConfig.CACHE_DURATION_HOURS * 3600:
                cached_df = pd.read_csv(FastConfig.CACHE_FILE)
                cache_last_updated = datetime.fromtimestamp(os.path.getmtime(FastConfig.CACHE_FILE))
                logging.info(f"Loaded cache: {len(cached_df)} rows (age: {file_age})")
                return True
        except Exception as e:
            logging.error(f"Error loading cache: {str(e)}")
    
    return False

def ensure_cache_ready():
    """Ensure cache is ready - load from file or build new"""
    global cached_df
    
    if cached_df is None:
        if not load_cache():
            logging.info("No valid cache found, building new cache...")
            build_fast_cache()

# YouTube video info extraction
def get_video_info(url):
    """Extract video info using yt-dlp"""
    try:
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': False,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'Unknown')
            duration = info.get('duration', 0)
            
            # Format duration
            if duration:
                minutes = duration // 60
                seconds = duration % 60
                duration_str = f"{minutes}m {seconds}s"
            else:
                duration_str = "Unknown"
            
            logging.info(f"Extracted video info: {title} ({duration_str})")
            return title, duration_str, duration
            
    except Exception as e:
        logging.error(f"Error extracting video info: {str(e)}")
        return None, None, 0

# Search functions
def extract_video_id(input_text):
    """Extract YouTube video ID from URL or return if it's already an ID"""
    try:
        input_text = input_text.strip()
        
        # If it's already a video ID (11 characters)
        if len(input_text) == 11 and input_text.replace('_', '').replace('-', '').isalnum():
            return input_text
            
        # Extract from various YouTube URL formats
        if 'youtu.be/' in input_text:
            return input_text.split('youtu.be/')[-1].split('?')[0].split('&')[0]
        elif 'youtube.com/watch' in input_text:
            from urllib.parse import parse_qs, urlparse
            parsed = urlparse(input_text)
            return parse_qs(parsed.query).get('v', [None])[0]
        elif 'youtube.com/embed/' in input_text:
            return input_text.split('youtube.com/embed/')[-1].split('?')[0]
            
        return None
    except Exception:
        return None

def detect_search_type(query):
    """Detect what type of search this is"""
    query = query.strip().upper()
    
    # Check for OCD number
    if query.startswith('OCD-') or (query.startswith('OCD') and any(c.isdigit() for c in query)):
        return 'ocd_number'
    
    # Check for VP number  
    if query.startswith('VP-') or (query.startswith('VP') and any(c.isdigit() for c in query)):
        return 'vp_number'
    
    # Check for Video ID (11 characters)
    if len(query) == 11 and query.replace('_', '').replace('-', '').isalnum():
        return 'video_id'
    
    # Check for YouTube URL
    if any(domain in query.lower() for domain in ['youtube.com', 'youtu.be']):
        return 'youtube_url'
    
    # Default to title search
    return 'title_search'
