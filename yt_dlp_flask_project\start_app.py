#!/usr/bin/env python3
"""
Simple launcher for the Flask application
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Importing Flask application...")
    from lightning_app import app, ArchivesConfig
    print("Import successful!")

    print(f"Starting {ArchivesConfig.APP_NAME} v{ArchivesConfig.APP_VERSION}")
    print(f"Cache file: {ArchivesConfig.CACHE_FILE}")
    print(f"Server will be available at:")
    print(f"   - http://127.0.0.1:8080")
    print(f"   - http://localhost:8080")
    print(f"Password: {ArchivesConfig.ACCESS_PASSWORD}")
    print("=" * 60)
    
    # Start the Flask application
    app.run(
        host='0.0.0.0',
        port=8080,
        debug=False,
        use_reloader=False,
        threaded=True
    )
    
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed:")
    print("  pip install flask pandas")

except Exception as e:
    print(f"Error starting application: {e}")
    import traceback
    traceback.print_exc()
