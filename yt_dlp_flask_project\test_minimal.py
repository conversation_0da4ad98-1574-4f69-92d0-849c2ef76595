#!/usr/bin/env python3
"""
Minimal test to check what's causing the issue
"""

print("🔍 Testing imports...")

try:
    import os
    print("✅ os imported")
    
    import pandas as pd
    print("✅ pandas imported")
    
    from flask import Flask
    print("✅ Flask imported")
    
    import logging
    print("✅ logging imported")
    
    # Test cache file
    cache_file = 'archives_cache.csv'
    if os.path.exists(cache_file):
        print(f"✅ Cache file exists: {cache_file}")
        try:
            df = pd.read_csv(cache_file)
            print(f"✅ Cache loaded: {len(df)} rows")
        except Exception as e:
            print(f"❌ Cache loading error: {e}")
    else:
        print(f"❌ Cache file not found: {cache_file}")
    
    # Test credentials
    creds_file = 'credentials.json'
    if os.path.exists(creds_file):
        print(f"✅ Credentials file exists: {creds_file}")
    else:
        print(f"❌ Credentials file not found: {creds_file}")
    
    print("🎉 All basic tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
