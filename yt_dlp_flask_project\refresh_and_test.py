#!/usr/bin/env python3
"""
Refresh cache and test video IDs
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:8080"
PASSWORD = "Shiva@123"

def refresh_cache_and_test():
    """Refresh cache and test video IDs"""
    
    session = requests.Session()
    
    print("🔄 REFRESHING CACHE AND TESTING VIDEO IDs")
    print("=" * 60)
    
    # Login first
    login_response = session.post(f"{BASE_URL}/login", data={'password': PASSWORD})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Refresh cache
    print("\n🔄 Refreshing cache from Google Sheets...")
    print("This may take 15-20 seconds...")
    
    start_time = time.time()
    refresh_response = session.post(f"{BASE_URL}/cache/refresh")
    
    if refresh_response.status_code == 200:
        refresh_data = refresh_response.json()
        elapsed_time = time.time() - start_time
        
        if refresh_data.get('success'):
            cache_size = refresh_data.get('cache_size', 0)
            print(f"✅ Cache refreshed successfully!")
            print(f"📊 Records loaded: {cache_size:,}")
            print(f"⏱️ Time taken: {elapsed_time:.2f} seconds")
        else:
            print(f"❌ Cache refresh failed: {refresh_data.get('error')}")
            return False
    else:
        print(f"❌ Cache refresh request failed with status {refresh_response.status_code}")
        return False
    
    # Test specific video IDs that should exist
    print("\n🔍 Testing specific video IDs after cache refresh...")
    
    test_ids = [
        "CPN-zOup_uS",  # User's specific example
        "f7ZrEl04CLk",  # Known to exist
        "wllIeFb2xpU",  # Known to exist
        "CO2DQGZgUQL", 
        "Bm-QyuLHutC",
        "rbYdXbEVm6E",
        "H4qQ7MHACbw"
    ]
    
    found_count = 0
    
    for video_id in test_ids:
        search_response = session.post(f"{BASE_URL}/search", data={'query': video_id})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            
            if matches > 0:
                found_count += 1
                print(f"✅ {video_id}: {matches} matches")
                
                # Show first match details
                if result.get('results'):
                    first_match = result['results'][0]
                    filename = first_match.get('filename', '')[:50]
                    ocd_vp = first_match.get('ocd_vp', '')
                    sheet = first_match.get('sheet_name', '')
                    print(f"   📄 {filename}... | {ocd_vp} | {sheet}")
            else:
                print(f"❌ {video_id}: No matches")
        else:
            print(f"❌ {video_id}: Search error")
    
    print(f"\n📊 Found {found_count}/{len(test_ids)} test video IDs")
    
    return found_count > 0

if __name__ == "__main__":
    refresh_cache_and_test()
