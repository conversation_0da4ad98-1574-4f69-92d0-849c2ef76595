2025-06-10 10:34:20,655 - INFO - ================================================================================
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 485, in <module>
    logging.info(f"\U0001f680 Starting {ArchivesConfig.APP_NAME} v{ArchivesConfig.APP_VERSION}")
Message: '\U0001f680 Starting Archives Stems Finder Pro v2.1 PRODUCTION'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3ed' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 486, in <module>
    logging.info("\U0001f3ed PRODUCTION MODE - 24/7 Operation")
Message: '\U0001f3ed PRODUCTION MODE - 24/7 Operation'
Arguments: ()
2025-06-10 10:34:20,690 - INFO - ================================================================================
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c2' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 488, in <module>
    logging.info(f"\U0001f4c2 Cache file: {ArchivesConfig.CACHE_FILE}")
Message: '\U0001f4c2 Cache file: archives_cache.csv'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u23f0' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 489, in <module>
    logging.info(f"\u23f0 Daily refresh: {ArchivesConfig.DAILY_REFRESH_HOUR}:00 AM")
Message: '\u23f0 Daily refresh: 3:00 AM'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3e5' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 490, in <module>
    logging.info(f"\U0001f3e5 Health checks: Every {ArchivesConfig.HEALTH_CHECK_INTERVAL}s")
Message: '\U0001f3e5 Health checks: Every 300s'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f511' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 491, in <module>
    logging.info(f"\U0001f511 Session timeout: {ArchivesConfig.SESSION_TIMEOUT_HOURS} hours")
Message: '\U0001f511 Session timeout: 24 hours'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4c2' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 495, in <module>
    logging.info("\U0001f4c2 Loading initial cache...")
Message: '\U0001f4c2 Loading initial cache...'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u26a1' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 496, in <module>
    ensure_cache_ready()
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 110, in ensure_cache_ready
    load_cache()
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 99, in load_cache
    logging.info(f"\u26a1 Cache loaded: {len(cached_df)} rows in {elapsed:.3f}s")
Message: '\u26a1 Cache loaded: 21762 rows in 0.223s'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f504' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 499, in <module>
    logging.info("\U0001f504 Starting daily refresh scheduler...")
Message: '\U0001f504 Starting daily refresh scheduler...'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u23f0' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1012, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 414, in daily_refresh_worker
    logging.info(f"\u23f0 Next automatic cache refresh scheduled for: {next_refresh}")
Message: '\u23f0 Next automatic cache refresh scheduled for: 2025-06-11 03:00:00'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u23f0' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 500, in <module>
    schedule_daily_refresh()
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 445, in schedule_daily_refresh
    logging.info(f"\u23f0 Daily cache refresh scheduler started ({ArchivesConfig.DAILY_REFRESH_HOUR}:00 AM)")
Message: '\u23f0 Daily cache refresh scheduler started (3:00 AM)'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3e5' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 502, in <module>
    logging.info("\U0001f3e5 Starting health monitor...")
Message: '\U0001f3e5 Starting health monitor...'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1012, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 462, in health_monitor_worker
    logging.info(f"\u2705 Health Check: Cache healthy - {len(cached_df)} records")
Message: '\u2705 Health Check: Cache healthy - 21762 records'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3e5' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 503, in <module>
    start_health_monitor()
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 478, in start_health_monitor
    logging.info(f"\U0001f3e5 Health monitor started (check every {ArchivesConfig.HEALTH_CHECK_INTERVAL}s)")
Message: '\U0001f3e5 Health monitor started (check every 300s)'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1012, in _bootstrap
    self._bootstrap_inner()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 1041, in _bootstrap_inner
    self.run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 992, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 466, in health_monitor_worker
    logging.info(f"\U0001f4ca Health Check: Cache file size - {cache_file_size / (1024*1024):.2f} MB")
Message: '\U0001f4ca Health Check: Cache file size - 2.96 MB'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 505, in <module>
    logging.info("\u2705 All background services started successfully")
Message: '\u2705 All background services started successfully'
Arguments: ()
2025-06-10 10:34:21,017 - INFO - ================================================================================
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f310' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 507, in <module>
    logging.info("\U0001f310 Server URLs:")
Message: '\U0001f310 Server URLs:'
Arguments: ()
2025-06-10 10:34:21,020 - INFO -    - http://127.0.0.1:8080
2025-06-10 10:34:21,021 - INFO -    - http://localhost:8080
2025-06-10 10:34:21,022 - INFO -    - Health Check: http://127.0.0.1:8080/health
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1153, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f511' in position 33: character maps to <undefined>
Call stack:
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\lightning_app_clean.py", line 511, in <module>
    logging.info(f"\U0001f511 Password: {ArchivesConfig.ACCESS_PASSWORD}")
Message: '\U0001f511 Password: Shiva@123'
Arguments: ()
2025-06-10 10:34:21,025 - INFO - ================================================================================
 * Serving Flask app 'lightning_app_clean'
 * Debug mode: off
