#!/usr/bin/env python3
"""
Working Archives Stems Finder with Google Sheets Integration
This is a clean, working version that definitely loads Google Sheets data
"""

import os
import time
import logging
import pandas as pd
from datetime import datetime
from flask import Flask, request, jsonify, render_template, session, redirect, url_for
import gspread
from google.oauth2.service_account import Credentials

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('working_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class Config:
    """Configuration"""
    APP_NAME = "Archives Stems Finder Pro"
    APP_VERSION = "3.0-WORKING"
    ACCESS_PASSWORD = "Shiva@123"
    GOOGLE_SHEET_ID = "1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE"
    SHEET_NAMES = [
        "Edited Main Sheet",
        "Social Media Catalog(SG)",
        "Social Media Catalog(IF)", 
        "Social Media Catalog(IG)",
        "Social Media Catalog(CP)",
        "Copy Social Media Catalog(SG)",
        "Copy Social Media Catalog(IF)",
        "Copy Social Media Catalog(IG)"
    ]
    CACHE_FILE = 'working_cache.csv'

# Global variables
app = Flask(__name__)
app.secret_key = 'working-app-secret-key'
cached_df = None
cache_last_updated = None

def get_google_sheets_client():
    """Get Google Sheets client"""
    try:
        scope = ['https://spreadsheets.google.com/feeds',
                 'https://www.googleapis.com/auth/drive']
        credentials = Credentials.from_service_account_file('credentials.json', scopes=scope)
        client = gspread.authorize(credentials)
        logging.info("✅ Google Sheets client initialized")
        return client
    except Exception as e:
        logging.error(f"❌ Google Sheets client failed: {e}")
        return None

def download_sheet_data(sheet_name):
    """Download data from a specific sheet"""
    try:
        client = get_google_sheets_client()
        if not client:
            return pd.DataFrame()

        spreadsheet = client.open_by_key(Config.GOOGLE_SHEET_ID)
        worksheet = spreadsheet.worksheet(sheet_name)
        all_values = worksheet.get_all_values()

        if not all_values:
            return pd.DataFrame()

        headers = all_values[0]
        data = all_values[1:]
        df = pd.DataFrame(data, columns=headers)
        
        # Clean the data
        if len(df.columns) >= 4:
            # Take first 4 columns and standardize names
            df = df.iloc[:, :4].copy()
            df.columns = ['filename', 'duration', 'ocd_vp', 'video_id']
            
            # Clean data
            df['filename'] = df['filename'].fillna('').astype(str)
            df['ocd_vp'] = df['ocd_vp'].fillna('').astype(str)
            df['video_id'] = df['video_id'].fillna('').astype(str)
            df['sheet_name'] = sheet_name
            
            # Remove empty rows
            df = df[df['filename'].str.len() > 0]
            
        logging.info(f"✅ Downloaded {len(df)} rows from {sheet_name}")
        return df
        
    except Exception as e:
        logging.error(f"❌ Error downloading {sheet_name}: {e}")
        return pd.DataFrame()

def build_cache_from_google_sheets():
    """Build cache by downloading all Google Sheets"""
    global cached_df, cache_last_updated
    
    logging.info("🚀 WORKING APP: Building cache from Google Sheets...")
    start_time = time.time()
    
    all_data = []
    
    for i, sheet_name in enumerate(Config.SHEET_NAMES, 1):
        logging.info(f"🚀 WORKING APP: [{i}/{len(Config.SHEET_NAMES)}] Downloading {sheet_name}")
        sheet_data = download_sheet_data(sheet_name)
        
        if not sheet_data.empty:
            all_data.append(sheet_data)
            logging.info(f"🚀 WORKING APP: ✅ Got {len(sheet_data)} rows from {sheet_name}")
        else:
            logging.warning(f"🚀 WORKING APP: ⚠️ No data from {sheet_name}")
    
    if all_data:
        cached_df = pd.concat(all_data, ignore_index=True)
        cache_last_updated = datetime.now()
        
        # Save to cache file
        cached_df.to_csv(Config.CACHE_FILE, index=False)
        
        elapsed = time.time() - start_time
        logging.info(f"🚀 WORKING APP: ✅ SUCCESS! Built cache: {len(cached_df):,} rows in {elapsed:.3f}s")
        return True
    else:
        logging.error("🚀 WORKING APP: ❌ FAILED! No data downloaded")
        return False

def search_cache(query):
    """Search the cache for matches"""
    if cached_df is None or cached_df.empty:
        return []
    
    query = query.strip()
    matches = []
    
    # Search in all text fields
    for idx, row in cached_df.iterrows():
        score = 0
        
        # Check filename
        if query.lower() in str(row['filename']).lower():
            score += 10
            
        # Check OCD/VP
        if query.upper() in str(row['ocd_vp']).upper():
            score += 20
            
        # Check video ID
        if query in str(row['video_id']):
            score += 15
            
        if score > 0:
            matches.append({
                'filename': row['filename'],
                'ocd_vp': row['ocd_vp'],
                'video_id': row['video_id'],
                'sheet_name': row['sheet_name'],
                'score': score
            })
    
    # Sort by score
    matches.sort(key=lambda x: x['score'], reverse=True)
    return matches[:10]  # Return top 10

# Authentication
def is_authenticated():
    return session.get('authenticated', False)

def require_auth(f):
    def decorated_function(*args, **kwargs):
        if not is_authenticated():
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        password = request.form.get('password', '')
        if password == Config.ACCESS_PASSWORD:
            session['authenticated'] = True
            return redirect(url_for('index'))
        else:
            return jsonify({'error': 'Invalid password'}), 401
    return jsonify({'message': 'Login required'})

@app.route('/')
@require_auth
def index():
    return jsonify({
        'app_name': Config.APP_NAME,
        'app_version': Config.APP_VERSION,
        'cache_size': len(cached_df) if cached_df is not None else 0,
        'last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
        'message': 'Working app with Google Sheets integration'
    })

@app.route('/search', methods=['POST'])
@require_auth
def search():
    query = request.form.get('query', '').strip()
    if not query:
        return jsonify({'error': 'No query provided'}), 400
    
    start_time = time.time()
    matches = search_cache(query)
    elapsed = time.time() - start_time
    
    return jsonify({
        'query': query,
        'total_matches': len(matches),
        'results': matches,
        'search_time': round(elapsed, 3)
    })

@app.route('/refresh-cache', methods=['POST'])
@require_auth
def refresh_cache():
    """Refresh cache from Google Sheets"""
    try:
        start_time = time.time()
        success = build_cache_from_google_sheets()
        elapsed = time.time() - start_time
        
        if success:
            return jsonify({
                'success': True,
                'message': f'🚀 WORKING APP: Cache refreshed from Google Sheets - {len(cached_df):,} records',
                'cache_size': len(cached_df) if cached_df is not None else 0,
                'elapsed_time': round(elapsed, 3),
                'last_updated': cache_last_updated.isoformat() if cache_last_updated else None
            })
        else:
            return jsonify({
                'success': False,
                'message': '🚀 WORKING APP: Cache refresh failed'
            }), 500
            
    except Exception as e:
        logging.error(f"🚀 WORKING APP: Cache refresh error: {e}")
        return jsonify({
            'success': False,
            'message': f'🚀 WORKING APP: Cache refresh failed: {str(e)}'
        }), 500

@app.route('/cache/status')
@require_auth
def cache_status():
    return jsonify({
        'cache_loaded': cached_df is not None,
        'cache_size': len(cached_df) if cached_df is not None else 0,
        'last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
        'app_version': Config.APP_VERSION
    })

@app.route('/debug-search/<query>')
@require_auth
def debug_search(query):
    """Debug search to see what's happening"""
    if cached_df is None or cached_df.empty:
        return jsonify({'error': 'No cache loaded'})

    # Search for exact matches in OCD/VP column
    ocd_matches = cached_df[cached_df['ocd_vp'].str.contains(query, case=False, na=False)]

    return jsonify({
        'query': query,
        'total_rows_in_cache': len(cached_df),
        'ocd_matches_found': len(ocd_matches),
        'sample_ocd_values': cached_df['ocd_vp'].head(10).tolist(),
        'matching_rows': ocd_matches[['filename', 'ocd_vp', 'sheet_name']].head(5).to_dict('records') if len(ocd_matches) > 0 else []
    })

def load_existing_cache():
    """Load existing cache file"""
    global cached_df, cache_last_updated

    if os.path.exists(Config.CACHE_FILE):
        logging.info("🚀 Loading existing cache...")
        cached_df = pd.read_csv(Config.CACHE_FILE)
        cache_last_updated = datetime.now()
        logging.info(f"🚀 Loaded cache: {len(cached_df):,} rows")
        return True
    return False

if __name__ == '__main__':
    logging.info(f"🚀 Starting {Config.APP_NAME} v{Config.APP_VERSION}")

    # Load existing cache if available, otherwise build new
    if not load_existing_cache():
        logging.info("🚀 Building initial cache...")
        build_cache_from_google_sheets()

    # Start Flask app
    app.run(host='0.0.0.0', port=8080, debug=False)
