@echo off
echo ========================================
echo Archives Stems Finder Pro - Startup
echo ========================================

REM Check if environment variables are set
if "%GOOGLE_CLIENT_ID%"=="" (
    echo ERROR: GOOGLE_CLIENT_ID environment variable not set
    echo Please set the required environment variables:
    echo set GOOGLE_CLIENT_ID=your_client_id_here
    echo set GOOGLE_CLIENT_SECRET=your_client_secret_here
    echo set SECRET_KEY=your_secret_key_here
    pause
    exit /b 1
)

if "%GOOGLE_CLIENT_SECRET%"=="" (
    echo ERROR: GOOGLE_CLIENT_SECRET environment variable not set
    echo Please set the required environment variables:
    echo set GOOGLE_CLIENT_ID=your_client_id_here
    echo set GOOGLE_CLIENT_SECRET=your_client_secret_here
    echo set SECRET_KEY=your_secret_key_here
    pause
    exit /b 1
)

echo Environment variables configured
echo Starting Flask application...
echo ========================================

python app.py

pause
