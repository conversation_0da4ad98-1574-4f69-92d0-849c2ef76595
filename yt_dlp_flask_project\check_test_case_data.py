#!/usr/bin/env python3
"""
Check if the test case target filenames exist in the cache
"""

import pandas as pd

def check_test_case_data():
    """Check if the target filenames for test cases exist in cache"""
    
    print("🔍 CHECKING TEST CASE TARGET FILENAMES IN CACHE")
    print("=" * 70)
    
    try:
        # Load cache file
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded cache: {len(df):,} rows")
        
        # Target filenames from test cases
        target_filenames = [
            "Insta-Reels_Can-We-Prevent-Cancer_14-Feb-2025_Tamil_01Min-53Secs_Stems",
            "Insta-Reels_Why-Is-God-Giving-Problems_15-Apr-2025_Tamil_55Secs_Stems",
            "Q-And-A_On-Receiving-Grace-Guru-Waiting-For-Someone-To-Receive-Him_23-Apr-2024_English_16Mins-08Secs_Stems",
            "Insta-Reels_You-Should-Not-Love-Shiva_08-Aug-2024_English_50Secs_Stems"
        ]
        
        print(f"\n🎯 SEARCHING FOR TARGET FILENAMES:")
        
        for i, target in enumerate(target_filenames, 1):
            print(f"\n{i}. Target: {target}")
            
            # Check exact match
            exact_matches = df[df['filename'] == target]
            print(f"   Exact matches: {len(exact_matches)}")
            
            # Check partial match
            partial_matches = df[df['filename'].astype(str).str.contains(target[:30], na=False, case=False)]
            print(f"   Partial matches (first 30 chars): {len(partial_matches)}")
            
            # Check for key words
            key_words = target.split('_')[:3]  # First 3 parts
            for word in key_words:
                if len(word) > 3:
                    word_matches = df[df['filename'].astype(str).str.contains(word, na=False, case=False)]
                    print(f"   Matches for '{word}': {len(word_matches)}")
                    
                    if len(word_matches) > 0:
                        print(f"      Sample: {word_matches.iloc[0]['filename'][:50]}...")
        
        # Check for similar patterns
        print(f"\n📊 CHECKING FOR SIMILAR PATTERNS:")
        
        # Check for Insta-Reels
        insta_reels = df[df['filename'].astype(str).str.contains('Insta-Reels', na=False, case=False)]
        print(f"   Files with 'Insta-Reels': {len(insta_reels)}")
        if len(insta_reels) > 0:
            print(f"      Sample: {insta_reels.iloc[0]['filename']}")
        
        # Check for Q-And-A
        qna = df[df['filename'].astype(str).str.contains('Q-And-A', na=False, case=False)]
        print(f"   Files with 'Q-And-A': {len(qna)}")
        if len(qna) > 0:
            print(f"      Sample: {qna.iloc[0]['filename']}")
        
        # Check for cancer-related content
        cancer = df[df['filename'].astype(str).str.contains('cancer', na=False, case=False)]
        print(f"   Files with 'cancer': {len(cancer)}")
        if len(cancer) > 0:
            for _, match in cancer.head(3).iterrows():
                print(f"      {match['filename']}")
        
        # Check for grace-related content
        grace = df[df['filename'].astype(str).str.contains('grace', na=False, case=False)]
        print(f"   Files with 'grace': {len(grace)}")
        if len(grace) > 0:
            for _, match in grace.head(3).iterrows():
                print(f"      {match['filename']}")
        
        # Check for problem-related content
        problems = df[df['filename'].astype(str).str.contains('problem', na=False, case=False)]
        print(f"   Files with 'problem': {len(problems)}")
        if len(problems) > 0:
            for _, match in problems.head(3).iterrows():
                print(f"      {match['filename']}")
        
        # Check for Shiva-related content
        shiva = df[df['filename'].astype(str).str.contains('shiva', na=False, case=False)]
        print(f"   Files with 'shiva': {len(shiva)}")
        if len(shiva) > 0:
            for _, match in shiva.head(3).iterrows():
                print(f"      {match['filename']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking cache: {e}")
        return False

def suggest_alternative_matches():
    """Suggest alternative matches from existing data"""
    
    print(f"\n💡 SUGGESTING ALTERNATIVE MATCHES FROM EXISTING DATA:")
    print("=" * 70)
    
    try:
        df = pd.read_csv('archives_cache.csv')
        
        # Find files that could be used as alternatives for each test case
        alternatives = {
            "Cancer prevention": df[df['filename'].astype(str).str.contains('health|cancer|prevent|disease', na=False, case=False)],
            "God/problems": df[df['filename'].astype(str).str.contains('god|divine|problem|suffering|why', na=False, case=False)],
            "Grace/receiving": df[df['filename'].astype(str).str.contains('grace|receive|blessing|guru', na=False, case=False)],
            "Answers/everything": df[df['filename'].astype(str).str.contains('answer|everything|question|truth', na=False, case=False)]
        }
        
        for category, matches in alternatives.items():
            print(f"\n📋 {category} ({len(matches)} matches):")
            for _, match in matches.head(3).iterrows():
                print(f"   📄 {match['filename']}")
                print(f"   📋 Sheet: {match['sheet_name']}")
                print(f"   🔢 OCD/VP: {match['ocd_vp']}")
                print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error suggesting alternatives: {e}")
        return False

if __name__ == "__main__":
    print("🚀 CHECKING TEST CASE DATA IN CACHE")
    print("Verifying if target filenames exist in current cache data")
    
    # Check test case data
    data_ok = check_test_case_data()
    
    # Suggest alternatives
    alternatives_ok = suggest_alternative_matches()
    
    print("\n" + "=" * 70)
    print("🎯 TEST CASE DATA CHECK RESULTS")
    print("=" * 70)
    
    if data_ok and alternatives_ok:
        print("✅ Data check completed successfully")
        print("💡 The target filenames from test cases don't exist in current cache")
        print("🔧 Need to either:")
        print("   1. Add synthetic matches for these specific filenames")
        print("   2. Use alternative existing filenames for testing")
        print("   3. Update test cases to match existing data")
    else:
        print("❌ Data check had issues")
        print("🔧 Need to investigate cache file structure")
    
    print("\n🚀 Test case data check complete!")
