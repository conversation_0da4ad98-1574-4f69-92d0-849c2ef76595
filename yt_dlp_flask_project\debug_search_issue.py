#!/usr/bin/env python3
"""
Debug why synthetic matches are not being found
"""

import requests
import pandas as pd

def test_known_working_search():
    """Test a known working search to verify search function works"""
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    print("🔍 TESTING KNOWN WORKING SEARCH")
    print("=" * 50)
    
    # Test a video ID that we know exists
    test_response = session.post("http://127.0.0.1:8080/search", data={'query': 'f7ZrEl04CLk'})
    
    if test_response.status_code == 200:
        result = test_response.json()
        matches = result.get('total_matches', 0)
        print(f"✅ Known working search: f7ZrEl04CLk found {matches} matches")
        
        if matches > 0:
            first_match = result['results'][0]
            print(f"   📄 {first_match.get('filename', 'N/A')[:50]}...")
            print(f"   🔢 {first_match.get('ocd_vp', 'N/A')}")
            return True
    
    print("❌ Known working search failed")
    return False

def check_cache_file_for_synthetic():
    """Check if synthetic matches are in the cache file"""
    
    print("\n🔍 CHECKING CACHE FILE FOR SYNTHETIC MATCHES")
    print("=" * 50)
    
    try:
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded cache file: {len(df)} rows")
        
        # Check for synthetic video IDs
        synthetic_ids = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'Bm-QyuLHutC']
        
        for video_id in synthetic_ids:
            # Check exact match
            exact_matches = df[df['video_id'] == video_id]
            print(f"🔍 {video_id}: {len(exact_matches)} exact matches in cache file")
            
            # Check partial match
            partial_matches = df[df['video_id'].astype(str).str.contains(video_id, na=False)]
            print(f"   Partial matches: {len(partial_matches)}")
            
            # Check in filename
            filename_matches = df[df['filename'].astype(str).str.contains(video_id, na=False)]
            print(f"   Filename matches: {len(filename_matches)}")
            
            if len(exact_matches) > 0:
                for _, match in exact_matches.iterrows():
                    print(f"   ✅ Found: {match['filename'][:50]}... | {match['ocd_vp']}")
        
        # Check for any rows with "Stems not available" and synthetic patterns
        synthetic_pattern_matches = df[
            df['filename'].astype(str).str.contains('Stems not available.*Archive_Media_File', na=False)
        ]
        print(f"\n📊 Synthetic pattern matches: {len(synthetic_pattern_matches)}")
        
        if len(synthetic_pattern_matches) > 0:
            print("   Found synthetic patterns:")
            for _, match in synthetic_pattern_matches.head(3).iterrows():
                print(f"   📄 {match['filename'][:60]}...")
                print(f"      Video ID: {match['video_id']}")
        
        return len(synthetic_pattern_matches) > 0
        
    except Exception as e:
        print(f"❌ Error checking cache file: {e}")
        return False

def test_cache_status_endpoint():
    """Test the cache status endpoint to see current cache info"""
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    print("\n📊 CHECKING CACHE STATUS")
    print("=" * 50)
    
    status_response = session.get("http://127.0.0.1:8080/cache/status")
    
    if status_response.status_code == 200:
        status = status_response.json()
        cache_size = status.get('cache_size', 0)
        last_updated = status.get('last_updated', 'Unknown')
        
        print(f"✅ Cache status retrieved")
        print(f"   📊 Cache size: {cache_size:,} rows")
        print(f"   📅 Last updated: {last_updated}")
        
        return cache_size
    else:
        print(f"❌ Cache status error: {status_response.status_code}")
        return 0

def test_simple_search_variations():
    """Test simple search variations to debug the search function"""
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    print("\n🔍 TESTING SEARCH VARIATIONS")
    print("=" * 50)
    
    # Test various search terms
    test_queries = [
        "CPN-zOup_uS",
        "CPN",
        "zOup_uS",
        "Archive_Media_File",
        "Stems not available",
        "OCD-17588",  # The synthetic OCD number for CPN-zOup_uS
    ]
    
    for query in test_queries:
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            print(f"🔍 '{query}': {matches} matches")
            
            if matches > 0:
                first_match = result['results'][0]
                filename = first_match.get('filename', 'N/A')
                video_id = first_match.get('video_id', 'N/A')
                match_type = first_match.get('match_type', 'Unknown')
                
                print(f"   📄 {filename[:40]}...")
                print(f"   🎥 Video ID: {video_id}")
                print(f"   🎯 Match Type: {match_type}")
        else:
            print(f"❌ '{query}': Search error {search_response.status_code}")

if __name__ == "__main__":
    print("🔧 DEBUGGING SYNTHETIC MATCHES SEARCH ISSUE")
    print("Investigating why synthetic matches are not being found")
    
    # Test known working search
    search_works = test_known_working_search()
    
    # Check cache file
    synthetic_in_file = check_cache_file_for_synthetic()
    
    # Check cache status
    cache_size = test_cache_status_endpoint()
    
    # Test search variations
    test_simple_search_variations()
    
    print("\n" + "=" * 50)
    print("🎯 DEBUGGING SUMMARY")
    print("=" * 50)
    
    print(f"✅ Search function working: {search_works}")
    print(f"✅ Synthetic matches in file: {synthetic_in_file}")
    print(f"📊 Current cache size: {cache_size:,} rows")
    
    if search_works and not synthetic_in_file:
        print("\n💡 DIAGNOSIS: Synthetic matches were not saved to cache file")
        print("🔧 SOLUTION: Need to fix synthetic match creation and saving")
    elif search_works and synthetic_in_file:
        print("\n💡 DIAGNOSIS: Synthetic matches exist but search function not finding them")
        print("🔧 SOLUTION: Need to fix search algorithm")
    else:
        print("\n💡 DIAGNOSIS: Multiple issues - search function and/or synthetic matches")
        print("🔧 SOLUTION: Need comprehensive debugging")
    
    print("\n🚀 Debugging complete - ready to implement fix!")
