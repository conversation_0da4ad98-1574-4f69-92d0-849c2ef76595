from flask import Flask, render_template, request, jsonify
import yt_dlp
import pandas as pd
from fuzzywuzzy import fuzz
import re
import pyperclip
import time

app = Flask(__name__)

def smart_clean(text):
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = re.sub(r'[_\-]', ' ', text)
    text = re.sub(r'\d{1,2}[a-z]{0,2}[- ]?(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*[- ]?\d{2,4}', '', text)
    text = re.sub(r'(sharings|stems|english|hindi|tamil|mins|secs|\d{2,4})', '', text)
    text = re.sub(r'[^a-z\s]', '', text)
    return text.strip()

def extract_keywords(text):
    text = smart_clean(text)
    return set(text.split())

def format_seconds(hms_string):
    try:
        h, m, s = map(int, hms_string.strip().split(':'))
        total_seconds = h * 3600 + m * 60 + s
        return f"{total_seconds // 60}m {total_seconds % 60}s", total_seconds
    except:
        return "??m ??s", 0

def get_video_info(url):
    try:
        ydl_opts = {
            'quiet': True,
            'forcejson': True,
            'noplaylist': True,
            'nocheckcertificate': True
        }
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info_dict = ydl.extract_info(url, download=False)
            title = info_dict.get('title', None)
            duration = info_dict.get('duration', 0)
            duration_str = f"{duration // 60}m {duration % 60}s"
            return title, duration_str, duration
    except Exception as e:
        return None, None, 0

def match_filename(video_title, csv_file, top_n=5):
    df = pd.read_csv(csv_file)
    filenames = df['filename'].fillna('').tolist()
    durations = df['duration'].fillna("00:00:00").tolist()

    video_clean = smart_clean(video_title)
    video_keywords = extract_keywords(video_title)

    match_results = []

    for filename, file_duration in zip(filenames, durations):
        file_clean = smart_clean(filename)
        file_keywords = extract_keywords(filename)
        keyword_overlap = len(video_keywords.intersection(file_keywords))
        token_score = fuzz.token_set_ratio(video_clean, file_clean)
        combined_score = token_score + (keyword_overlap * 5)

        match_results.append((filename, combined_score, file_duration))

    match_results.sort(key=lambda x: x[1], reverse=True)
    return match_results[:top_n]

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/match', methods=['POST'])
def match():
    start_time = time.time()  # Start the timer

    url = request.form['url']
    if not url:
        return jsonify({'error': 'Please paste a YouTube URL.'})

    video_title, yt_duration, yt_duration_seconds = get_video_info(url)
    if not video_title:
        return jsonify({'error': 'Failed to fetch video title.'})

    top_matches = match_filename(video_title, 'StemsName.csv')

    result = []
    for filename, score, file_duration in top_matches:
        file_duration_fmt, file_duration_seconds = format_seconds(file_duration)
        
        # Check the mismatch condition
        duration_diff = abs(yt_duration_seconds - file_duration_seconds)
        if duration_diff <= 20:
            color = "green"  # If within 20 seconds
        else:
            color = "red"  # If mismatch is more than 20 seconds

        result.append({
            'filename': filename,
            'score': score,
            'file_duration': file_duration_fmt,
            'yt_duration': yt_duration,
            'color': color
        })

    # Update the timer label with time taken for the task
    end_time = time.time()  # End the timer
    elapsed_time = round(end_time - start_time, 2)

    return jsonify({
        'video_title': video_title,
        'yt_duration': yt_duration,
        'result': result,
        'elapsed_time': elapsed_time
    })

if __name__ == '__main__':
    app.run(debug=True)
