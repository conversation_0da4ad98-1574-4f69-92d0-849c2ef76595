#!/usr/bin/env python3
"""
Test Flask search endpoint to debug the issue
"""

import requests
import json

def test_flask_search_endpoint():
    """Test the Flask search endpoint with detailed debugging"""
    
    print("🔍 TESTING FLASK SEARCH ENDPOINT")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test cache status first
    status_response = session.get("http://127.0.0.1:8080/cache/status")
    if status_response.status_code == 200:
        status = status_response.json()
        print(f"📊 Cache status: {status.get('cache_size', 0):,} rows")
    
    # Test known working search
    print("\n🔍 Testing known working search (f7ZrEl04CLk):")
    known_response = session.post("http://127.0.0.1:8080/search", data={'query': 'f7ZrEl04CLk'})
    
    if known_response.status_code == 200:
        result = known_response.json()
        matches = result.get('total_matches', 0)
        print(f"   ✅ Known search: {matches} matches")
        
        if matches > 0:
            first_match = result['results'][0]
            print(f"   📄 {first_match.get('filename', 'N/A')[:40]}...")
    else:
        print(f"   ❌ Known search failed: {known_response.status_code}")
        return False
    
    # Test synthetic matches
    print("\n🔍 Testing synthetic matches:")
    synthetic_tests = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'Bm-QyuLHutC']
    
    for video_id in synthetic_tests:
        print(f"\n   Testing: {video_id}")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': video_id})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"   Result: {matches} matches in {search_time:.3f}s")
            
            if matches > 0:
                first_match = result['results'][0]
                print(f"   ✅ Found: {first_match.get('filename', 'N/A')[:40]}...")
                print(f"   🔢 OCD/VP: {first_match.get('ocd_vp', 'N/A')}")
                print(f"   🎥 Video ID: {first_match.get('video_id', 'N/A')}")
                print(f"   🎯 Match Type: {first_match.get('match_type', 'Unknown')}")
            else:
                print(f"   ❌ No matches found")
        else:
            print(f"   ❌ Search error: {search_response.status_code}")
            print(f"   Response: {search_response.text[:200]}...")
    
    # Test cache refresh to see if that helps
    print(f"\n🔄 Testing cache refresh:")
    refresh_response = session.post("http://127.0.0.1:8080/cache/refresh")
    
    if refresh_response.status_code == 200:
        refresh_result = refresh_response.json()
        if refresh_result.get('success'):
            new_cache_size = refresh_result.get('cache_size', 0)
            print(f"   ✅ Cache refreshed: {new_cache_size:,} rows")
            
            # Test synthetic search again after refresh
            print(f"\n🔍 Testing CPN-zOup_uS after cache refresh:")
            post_refresh_response = session.post("http://127.0.0.1:8080/search", data={'query': 'CPN-zOup_uS'})
            
            if post_refresh_response.status_code == 200:
                result = post_refresh_response.json()
                matches = result.get('total_matches', 0)
                print(f"   Post-refresh result: {matches} matches")
                
                if matches > 0:
                    print("   🎉 SUCCESS: Found after cache refresh!")
                    return True
                else:
                    print("   ❌ Still not found after cache refresh")
            else:
                print(f"   ❌ Search error after refresh: {post_refresh_response.status_code}")
        else:
            print(f"   ❌ Cache refresh failed: {refresh_result.get('error', 'Unknown error')}")
    else:
        print(f"   ❌ Cache refresh request failed: {refresh_response.status_code}")
    
    return False

if __name__ == "__main__":
    print("🔧 FLASK SEARCH ENDPOINT DEBUGGING")
    print("Testing the Flask search endpoint to find the issue")
    
    success = test_flask_search_endpoint()
    
    print("\n" + "=" * 50)
    print("🎯 FLASK ENDPOINT TEST RESULTS")
    print("=" * 50)
    
    if success:
        print("🎉 SUCCESS: Flask search endpoint working!")
        print("✅ Synthetic matches found via Flask API!")
    else:
        print("🔧 ISSUE: Flask search endpoint not finding synthetic matches")
        print("💡 The in-memory cache might not match the file cache")
        print("🔧 SOLUTION: Need to force reload the cache in Flask app")
    
    print("\n🚀 Flask endpoint debugging complete!")
