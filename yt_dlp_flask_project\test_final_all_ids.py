#!/usr/bin/env python3
"""
FINAL TEST: Test all missing IDs with the final working app
"""

import requests
import time

def test_final_all_ids():
    """Test all missing IDs"""
    base_url = "http://127.0.0.1:8080"
    
    # ALL missing IDs to test
    missing_ids = [
        "OCD-16673", "OCD-16668", "OCD-16698", "OCD-15138", "OCD-16697", 
        "OCD-16677", "OCD-16693", "VP-9992", "VP-8882", "VP-5552", 
        "VP-3332", "VP-1112", "VP-0002", "VP-42222", "C9SQ3zxyYO9", 
        "DKrifQ0T_8Z", "A75b1NKWCC4"
    ]
    
    session = requests.Session()
    
    try:
        # Login
        print("🔐 FINAL TEST: Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': '<PERSON>@123'})
        print(f"   Login status: {login_response.status_code}")
        
        # Check app status
        print("\n📊 FINAL TEST: Checking app status...")
        status_response = session.get(f"{base_url}/cache/status")
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"   Cache loaded: {status_data.get('cache_loaded', False)}")
            print(f"   Cache size: {status_data.get('cache_size', 0):,} records")
            print(f"   App version: {status_data.get('app_version', 'N/A')}")
            print(f"   Cache file: {status_data.get('cache_file', 'N/A')}")
        
        # Test ALL missing IDs
        print(f"\n🔍 FINAL TEST: Testing ALL {len(missing_ids)} missing IDs...")
        found_count = 0
        missing_count = 0
        found_ids = []
        still_missing_ids = []
        
        for i, test_id in enumerate(missing_ids, 1):
            print(f"\n   [{i:2d}/{len(missing_ids)}] Testing: {test_id}")
            
            start_time = time.time()
            search_response = session.post(f"{base_url}/search", data={'query': test_id})
            elapsed_time = time.time() - start_time
            
            if search_response.status_code == 200:
                result_data = search_response.json()
                total_matches = result_data.get('total_matches', 0)
                
                if total_matches > 0:
                    print(f"      ✅ FOUND: {total_matches} matches in {elapsed_time:.3f}s")
                    found_count += 1
                    found_ids.append(test_id)
                    
                    # Show first result
                    if result_data.get('results'):
                        first_result = result_data['results'][0]
                        print(f"         📄 File: {first_result.get('filename', 'N/A')[:50]}...")
                        print(f"         🆔 OCD/VP: {first_result.get('ocd_vp', 'N/A')}")
                        print(f"         📋 Sheet: {first_result.get('sheet_name', 'N/A')}")
                        print(f"         🎯 Score: {first_result.get('score', 0)}")
                else:
                    print(f"      ❌ NOT FOUND in {elapsed_time:.3f}s")
                    missing_count += 1
                    still_missing_ids.append(test_id)
            else:
                print(f"      ❌ Search error: {search_response.status_code}")
                missing_count += 1
                still_missing_ids.append(test_id)
        
        # FINAL SUMMARY
        print(f"\n" + "="*80)
        print(f"🎯 FINAL RESULTS SUMMARY:")
        print(f"="*80)
        print(f"✅ FOUND: {found_count}/{len(missing_ids)} IDs ({(found_count/len(missing_ids)*100):.1f}%)")
        print(f"❌ MISSING: {missing_count}/{len(missing_ids)} IDs ({(missing_count/len(missing_ids)*100):.1f}%)")
        
        if found_ids:
            print(f"\n✅ SUCCESSFULLY FOUND IDs:")
            for found_id in found_ids:
                print(f"   - {found_id}")
        
        if still_missing_ids:
            print(f"\n❌ STILL MISSING IDs:")
            for missing_id in still_missing_ids:
                print(f"   - {missing_id}")
        
        # Final verdict
        if found_count == len(missing_ids):
            print(f"\n🎉 PERFECT SUCCESS: ALL {len(missing_ids)} IDs FOUND!")
            print(f"🎉 Cache refresh is working 100% correctly!")
        elif found_count >= len(missing_ids) * 0.8:  # 80% or more
            print(f"\n🎊 EXCELLENT SUCCESS: {found_count}/{len(missing_ids)} IDs found!")
            print(f"🎊 Cache refresh is working very well!")
        elif found_count > 0:
            print(f"\n✅ PARTIAL SUCCESS: {found_count}/{len(missing_ids)} IDs found!")
            print(f"✅ Cache refresh is working but some IDs may not exist in Google Sheets!")
        else:
            print(f"\n❌ ISSUE: No IDs found - there may still be a problem!")
            
        print(f"\n" + "="*80)
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_final_all_ids()
