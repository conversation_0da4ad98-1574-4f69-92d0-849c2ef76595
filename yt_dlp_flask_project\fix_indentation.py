#!/usr/bin/env python3
"""
Fix indentation issues in lightning_app_clean.py
"""

def fix_indentation():
    """Fix indentation issues"""
    
    # Read the file
    with open('lightning_app_clean.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Fix indentation
    fixed_lines = []
    for line in lines:
        # Fix lines that start with single space to 4 spaces
        if line.startswith(' ') and not line.startswith('    ') and not line.startswith('  '):
            # Count leading spaces
            stripped = line.lstrip(' ')
            if stripped and not stripped.startswith('#'):
                # Replace single space with 4 spaces for function/class content
                fixed_lines.append('    ' + stripped)
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    # Write back to file
    with open('lightning_app_clean.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("Indentation fixed successfully!")

if __name__ == '__main__':
    fix_indentation()
