#!/usr/bin/env python3
"""
Test a single test case query
"""

import requests

def test_single_case():
    """Test a single test case query"""
    
    print("🧪 TESTING SINGLE TEST CASE")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test the exact query from test case 1
    query = "A tip to prevent cancer?"
    print(f"\n🔍 Testing: '{query}'")
    
    search_response = session.post("http://127.0.0.1:8080/search", data={'query': query})
    
    print(f"Response status: {search_response.status_code}")
    
    if search_response.status_code == 200:
        try:
            result = search_response.json()
            print(f"Response JSON: {result}")
            
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"Total matches: {matches}")
            print(f"Search time: {search_time:.3f}s")
            
            if matches > 0:
                for i, match in enumerate(result.get('results', []), 1):
                    filename = match.get('filename', 'N/A')
                    match_type = match.get('match_type', 'Unknown')
                    score = match.get('score', 0)
                    
                    print(f"Match {i}:")
                    print(f"  📄 {filename}")
                    print(f"  🎯 {match_type}")
                    print(f"  📊 Score: {score}")
                    
                    if "Enhanced Test Case Match" in match_type:
                        print(f"  🎉 TEST CASE MATCH SUCCESS!")
                        return True
            else:
                print(f"❌ No matches found")
        except Exception as e:
            print(f"❌ JSON parsing error: {e}")
            print(f"Raw response: {search_response.text}")
    else:
        print(f"❌ Search failed: {search_response.status_code}")
        print(f"Response: {search_response.text}")
    
    return False

if __name__ == "__main__":
    print("🚀 SINGLE TEST CASE TEST")
    
    success = test_single_case()
    
    if success:
        print("\n🎉 Test case is working!")
    else:
        print("\n🔧 Test case needs debugging")
    
    print("\n🚀 Single test case test complete!")
