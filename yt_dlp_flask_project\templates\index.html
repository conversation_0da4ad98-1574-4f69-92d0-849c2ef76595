<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Stems Finder - Advanced Video Content Discovery</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .main-container {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: var(--shadow-heavy);
            margin: 2rem auto;
            max-width: 1400px;
            overflow: hidden;
        }

        .hero-section {
            background: var(--dark-gradient);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,1000 1000,0 1000,1000"/></svg>');
            pointer-events: none;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .feature-badges {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .feature-badge {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50px;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .search-section {
            padding: 3rem 2rem;
        }

        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .search-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-light);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .search-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .search-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .search-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .search-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.2rem;
            color: white;
        }

        .youtube-icon { background: var(--secondary-gradient); }
        .search-icon-blue { background: var(--success-gradient); }
        .code-icon { background: var(--dark-gradient); }
        .drive-icon { background: linear-gradient(135deg, #34a853 0%, #4285f4 100%); }

        .search-card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .search-card-desc {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }

        .btn-search {
            background: var(--primary-gradient);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .stats-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            display: block;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .hero-title { font-size: 2.5rem; }
            .hero-subtitle { font-size: 1.1rem; }
            .search-grid { grid-template-columns: 1fr; }
            .main-container { margin: 1rem; }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">
                    <i class="fas fa-search me-3"></i>Stems Finder Pro
                </h1>
                <p class="hero-subtitle">
                    Advanced Video Content Discovery Platform with AI-Powered Matching
                </p>
                <div class="feature-badges">
                    <span class="feature-badge"><i class="fab fa-youtube me-2"></i>YouTube Integration</span>
                    <span class="feature-badge"><i class="fas fa-database me-2"></i>16,000+ Stems</span>
                    <span class="feature-badge"><i class="fab fa-google-drive me-2"></i>Google Drive</span>
                    <span class="feature-badge"><i class="fas fa-bolt me-2"></i>Lightning Fast</span>
                </div>
            </div>
        </div>

        <div class="search-section">
            <!-- Universal Search Grid -->
            <div class="search-grid">
                <!-- YouTube URL Search -->
                <div class="search-card">
                    <div class="search-card-header">
                        <div class="search-icon youtube-icon">
                            <i class="fab fa-youtube"></i>
                        </div>
                        <div>
                            <h3 class="search-card-title">YouTube URL Search</h3>
                            <p class="search-card-desc">Paste any YouTube URL for instant stem matching</p>
                        </div>
                    </div>
                    <form id="urlForm">
                        <input type="url" class="form-control" id="url" name="url"
                               placeholder="https://www.youtube.com/watch?v=..." required>
                        <button type="submit" class="btn-search">
                            <i class="fas fa-search me-2"></i>Search by URL
                        </button>
                    </form>
                </div>

                <!-- Universal Search -->
                <div class="search-card">
                    <div class="search-card-header">
                        <div class="search-icon search-icon-blue">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div>
                            <h3 class="search-card-title">Smart Universal Search</h3>
                            <p class="search-card-desc">Search by title, Video ID, OCD/VP numbers</p>
                        </div>
                    </div>
                    <form id="titleForm">
                        <input type="text" class="form-control" id="title" name="title"
                               placeholder="Video title, C8_3zchPsAY, VP-16338, OCD-12623..." required>
                        <button type="submit" class="btn-search">
                            <i class="fas fa-search me-2"></i>Smart Search
                        </button>
                    </form>
                </div>

                <!-- Video ID Search -->
                <div class="search-card">
                    <div class="search-card-header">
                        <div class="search-icon code-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <div>
                            <h3 class="search-card-title">Video ID Search</h3>
                            <p class="search-card-desc">Direct search using YouTube Video IDs</p>
                        </div>
                    </div>
                    <form id="videoIdForm">
                        <input type="text" class="form-control" id="videoId" name="videoId"
                               placeholder="C8_3zchPsAY, C8_cunFPMVT..." required>
                        <button type="submit" class="btn-search">
                            <i class="fas fa-search me-2"></i>Search by ID
                        </button>
                    </form>
                </div>

                <!-- OCD/VP Number Search -->
                <div class="search-card">
                    <div class="search-card-header">
                        <div class="search-icon drive-icon">
                            <i class="fab fa-google-drive"></i>
                        </div>
                        <div>
                            <h3 class="search-card-title">OCD/VP Search</h3>
                            <p class="search-card-desc">Find stems using OCD or VP numbers</p>
                        </div>
                    </div>
                    <form id="ocdVpForm">
                        <input type="text" class="form-control" id="ocdVp" name="ocdVp"
                               placeholder="OCD-12623, VP-16338..." required>
                        <button type="submit" class="btn-search">
                            <i class="fas fa-search me-2"></i>Find Stems
                        </button>
                    </form>
                </div>
            </div>

            <!-- Loading Spinner -->
            <div id="loading" class="loading">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">Searching through Google Sheets...</p>
            </div>

            <!-- Stats Section -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number" id="cacheSize">Loading...</span>
                        <span class="stat-label">Total Stems</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="sheetsCount">8</span>
                        <span class="stat-label">Google Sheets</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="cacheStatus">Checking...</span>
                        <span class="stat-label">Cache Status</span>
                    </div>
                    <div class="stat-item">
                        <button id="refreshCacheBtn" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-sync-alt me-1"></i>Quick Update
                        </button>
                        <button id="fullRefreshBtn" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-database me-1"></i>Full Rebuild
                        </button>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div id="result"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show/hide loading spinner
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').innerHTML = '';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // Universal search function
        async function performSearch(query, searchType) {
            showLoading();

            try {
                const endpoint = searchType === 'url' ? '/match_by_url' : '/match_by_title';
                const paramName = searchType === 'url' ? 'url' : 'title';

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({ [paramName]: query })
                });

                const data = await response.json();
                hideLoading();
                displayResults(data, searchType);
            } catch (error) {
                hideLoading();
                displayError('Network error occurred. Please try again.');
            }
        }

        // Handle URL search form
        document.getElementById('urlForm').onsubmit = async (e) => {
            e.preventDefault();
            const url = document.getElementById('url').value;
            await performSearch(url, 'url');
        };

        // Handle Universal search form
        document.getElementById('titleForm').onsubmit = async (e) => {
            e.preventDefault();
            const title = document.getElementById('title').value;
            await performSearch(title, 'universal');
        };

        // Handle Video ID search form
        document.getElementById('videoIdForm').onsubmit = async (e) => {
            e.preventDefault();
            const videoId = document.getElementById('videoId').value;
            await performSearch(videoId, 'video_id');
        };

        // Handle OCD/VP search form
        document.getElementById('ocdVpForm').onsubmit = async (e) => {
            e.preventDefault();
            const ocdVp = document.getElementById('ocdVp').value;
            await performSearch(ocdVp, 'ocd_vp');
        };

        // Display error message
        function displayError(message) {
            document.getElementById('result').innerHTML = `
                <div class="alert alert-danger rounded-4 border-0" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }

        // Display results with enhanced styling
        function displayResults(data, searchType) {
            if (data.error) {
                displayError(data.error);
                return;
            }

            const searchTypeLabels = {
                'url': 'YouTube URL',
                'universal': 'Smart Search',
                'video_id': 'Video ID',
                'ocd_vp': 'OCD/VP Number'
            };

            let resultHTML = `
                <div class="alert alert-primary rounded-4 border-0" role="alert">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-chart-line me-3 fs-4"></i>
                        <div>
                            <h5 class="mb-1">Search Results</h5>
                            <small class="opacity-75">
                                ${searchTypeLabels[searchType] || searchType} •
                                ${data.total_matches || 0} matches •
                                ${data.elapsed_time}s
                            </small>
                        </div>
                    </div>
                    ${data.video_title ? `<p class="mb-0"><strong>Video:</strong> ${data.video_title}</p>` : ''}
                    ${data.yt_duration ? `<p class="mb-0"><strong>Duration:</strong> ${data.yt_duration}</p>` : ''}
                </div>
            `;

            if (!data.result || data.result.length === 0) {
                resultHTML += `
                    <div class="alert alert-warning rounded-4 border-0" role="alert">
                        <i class="fas fa-search me-2"></i>
                        No matching stems found. Try different keywords or check the spelling.
                    </div>
                `;
            } else {
                resultHTML += '<div class="row g-4">';

                data.result.forEach((item, index) => {
                    const colorClass = item.color === 'green' ? 'success' :
                                     item.color === 'red' ? 'danger' : 'primary';

                    resultHTML += `
                        <div class="col-lg-6 mb-3">
                            <div class="card h-100 border-0 shadow-sm rounded-4">
                                <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                                    <span class="badge bg-${colorClass} px-3 py-2 rounded-pill">
                                        Score: ${Math.round(item.score)}
                                    </span>
                                    <small class="text-muted fw-medium">${item.sheet_name}</small>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title mb-3" title="${item.filename}">
                                        <i class="fas fa-file-video me-2 text-primary"></i>
                                        ${item.filename.length > 50 ? item.filename.substring(0, 50) + '...' : item.filename}
                                    </h6>
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-clock me-2 text-muted"></i>
                                        <span class="text-muted">${item.file_duration}</span>
                                        ${item.yt_duration && item.yt_duration !== item.file_duration ?
                                            ` <span class="mx-2">•</span> <span class="text-muted">YT: ${item.yt_duration}</span>` : ''}
                                    </div>
                                    ${item.search_type ? `
                                        <div class="mb-3">
                                            <span class="badge bg-light text-dark rounded-pill">
                                                ${item.search_type}
                                            </span>
                                        </div>
                                    ` : ''}
                                    ${item.drive_link ? `
                                        <a href="${item.drive_link}" target="_blank"
                                           class="btn btn-outline-primary btn-sm rounded-pill">
                                            <i class="fab fa-google-drive me-2"></i>Open in Drive
                                        </a>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                });

                resultHTML += '</div>';
            }

            document.getElementById('result').innerHTML = resultHTML;
        }

        // Copy filename to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                // Show toast or notification
                console.log('Copied to clipboard:', text);
            });
        }

        // Enhanced cache management functions
        async function loadCacheStatus() {
            try {
                const response = await fetch('/cache/status');
                const data = await response.json();

                // Update cache size
                document.getElementById('cacheSize').textContent =
                    data.cache_size ? data.cache_size.toLocaleString() : 'Loading...';

                // Update cache status
                const statusElement = document.getElementById('cacheStatus');
                const isValid = data.is_valid;
                statusElement.textContent = isValid ? 'Active' : 'Expired';
                statusElement.className = `stat-number ${isValid ? 'text-success' : 'text-warning'}`;

            } catch (error) {
                document.getElementById('cacheStatus').textContent = 'Error';
                document.getElementById('cacheStatus').className = 'stat-number text-danger';
            }
        }

        async function refreshCache() {
            const refreshBtn = document.getElementById('refreshCacheBtn');
            const originalText = refreshBtn.innerHTML;

            try {
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
                refreshBtn.disabled = true;

                const response = await fetch('/cache/refresh', { method: 'POST' });
                const data = await response.json();

                if (data.status === 'success') {
                    // Update cache size immediately
                    document.getElementById('cacheSize').textContent =
                        data.cache_size.toLocaleString();
                    document.getElementById('cacheStatus').textContent = 'Active';
                    document.getElementById('cacheStatus').className = 'stat-number text-success';

                    // Show success notification
                    showNotification('Cache refreshed successfully!', 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                showNotification(`Cache refresh failed: ${error.message}`, 'error');
            } finally {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            }
        }

        // Show notification
        function showNotification(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-${icon} me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Full cache refresh
        async function fullRefreshCache() {
            const fullRefreshBtn = document.getElementById('fullRefreshBtn');
            const originalText = fullRefreshBtn.innerHTML;

            try {
                fullRefreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Rebuilding...';
                fullRefreshBtn.disabled = true;

                const response = await fetch('/cache/refresh/full', { method: 'POST' });
                const data = await response.json();

                if (data.status === 'success') {
                    // Update cache size immediately
                    document.getElementById('cacheSize').textContent =
                        data.cache_size.toLocaleString();
                    document.getElementById('cacheStatus').textContent = 'Active';
                    document.getElementById('cacheStatus').className = 'stat-number text-success';

                    // Show success notification
                    showNotification('Full cache rebuild completed successfully!', 'success');
                } else {
                    throw new Error(data.message);
                }
            } catch (error) {
                showNotification(`Full cache rebuild failed: ${error.message}`, 'error');
            } finally {
                fullRefreshBtn.innerHTML = originalText;
                fullRefreshBtn.disabled = false;
            }
        }

        // Event listeners
        document.getElementById('refreshCacheBtn').addEventListener('click', refreshCache);
        document.getElementById('fullRefreshBtn').addEventListener('click', fullRefreshCache);

        // Load cache status on page load
        document.addEventListener('DOMContentLoaded', loadCacheStatus);

        // Auto-refresh cache status every 60 seconds
        setInterval(loadCacheStatus, 60000);
    </script>
</body>
</html>
