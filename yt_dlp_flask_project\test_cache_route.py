#!/usr/bin/env python3
"""
Test the /cache/refresh route
"""

import requests
import time

def test_cache_route():
    """Test the /cache/refresh route"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("1. Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"   Login status: {login_response.status_code}")
        
        # Test /cache/refresh route
        print("\n2. Testing /cache/refresh route...")
        print("   This should call build_archives_cache() with Google Sheets...")
        
        start_time = time.time()
        refresh_response = session.post(f"{base_url}/cache/refresh")
        elapsed_time = time.time() - start_time
        
        print(f"   Cache refresh completed in {elapsed_time:.3f}s")
        print(f"   Response status: {refresh_response.status_code}")
        
        if refresh_response.status_code == 200:
            data = refresh_response.json()
            print(f"   Success: {data.get('success', False)}")
            print(f"   Message: {data.get('message', 'N/A')}")
            print(f"   Cache size: {data.get('cache_size', 0):,}")
            
            # Check if cache size increased (should be >22,000 if Google Sheets worked)
            cache_size = data.get('cache_size', 0)
            if cache_size > 22000:
                print(f"   ✅ SUCCESS: Cache size is {cache_size:,} - Google Sheets integration working!")
            else:
                print(f"   ❌ ISSUE: Cache size is {cache_size:,} - Still using old CSV file")
                
        else:
            print(f"   Error: {refresh_response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_cache_route()
