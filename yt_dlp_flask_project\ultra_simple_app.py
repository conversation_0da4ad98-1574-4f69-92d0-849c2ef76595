#!/usr/bin/env python3
"""
Ultra simple Flask app - just to test basic functionality
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test App</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 50px; text-align: center; }
            .container { max-width: 600px; margin: 0 auto; padding: 30px; background: #f8f9fa; border-radius: 10px; }
            h1 { color: #007bff; }
            .success { color: #28a745; font-size: 18px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 Flask App is Working!</h1>
            <div class="success">✅ Connection successful</div>
            <p>The Flask application is running correctly and responding to requests.</p>
            <p><strong>Next step:</strong> We can now add the search functionality.</p>
        </div>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 Starting Ultra Simple Test App")
    print("🌐 Open your browser and go to: http://127.0.0.1:8083")
    
    app.run(host='127.0.0.1', port=8083, debug=False)
