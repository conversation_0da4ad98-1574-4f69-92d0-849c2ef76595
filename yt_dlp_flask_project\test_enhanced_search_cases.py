#!/usr/bin/env python3
"""
Test script for enhanced search functionality with specific test cases
"""

import requests
import json
import time

def test_enhanced_search_cases():
    """Test the four specific enhanced search test cases"""
    
    print("🔍 TESTING ENHANCED SEARCH FUNCTIONALITY")
    print("=" * 80)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Define the four test cases
    test_cases = [
        {
            'query': "A tip to prevent cancer?",
            'expected_filename': "Insta-Reels_Can-We-Prevent-Cancer_14-Feb-2025_Tamil_01Min-53Secs_Stems",
            'description': "Cancer prevention tips",
            'test_id': 1
        },
        {
            'query': "Why is God giving problems?",
            'expected_filename': "Insta-Reels_Why-Is-God-Giving-Problems_15-Apr-2025_Tamil_55Secs_Stems",
            'description': "Why God gives problems",
            'test_id': 2
        },
        {
            'query': "Who will receive God's grace?",
            'expected_filename': "Q-And-A_On-Receiving-Grace-<PERSON>-Waiting-For-Someone-To-Receive-Him_23-Apr-2024_English_16Mins-08Secs_Stems",
            'description': "Receiving God's grace",
            'test_id': 3
        },
        {
            'query': "Find Answers to Everything",
            'expected_filename': "Insta-Reels_You-Should-Not-Love-Shiva_08-Aug-2024_English_50Secs_Stems",
            'description': "Finding answers to everything",
            'test_id': 4
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    detailed_results = []
    
    print(f"\n🎯 RUNNING {total_tests} ENHANCED SEARCH TEST CASES:")
    print("=" * 80)
    
    for test_case in test_cases:
        query = test_case['query']
        expected_filename = test_case['expected_filename']
        description = test_case['description']
        test_id = test_case['test_id']
        
        print(f"\n📋 TEST CASE {test_id}: {description}")
        print(f"   🔍 Query: \"{query}\"")
        print(f"   📄 Expected: {expected_filename}")
        
        # Perform search
        start_time = time.time()
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        search_time = time.time() - start_time
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            
            print(f"   ⏱️  Search completed in {search_time:.3f}s")
            print(f"   📊 Total matches found: {matches}")
            
            if matches > 0:
                # Check if expected filename is in results
                found_expected = False
                best_match = None
                
                for i, match in enumerate(result.get('results', []), 1):
                    filename = match.get('filename', '')
                    match_type = match.get('match_type', 'Unknown')
                    score = match.get('score', 0)
                    sheet_name = match.get('sheet_name', 'Unknown')
                    
                    print(f"      Match {i}:")
                    print(f"      📄 Filename: {filename}")
                    print(f"      🎯 Match Type: {match_type}")
                    print(f"      📊 Score: {score}")
                    print(f"      📋 Sheet: {sheet_name}")
                    
                    # Check if this matches the expected filename
                    if expected_filename.lower() in filename.lower() or filename.lower() in expected_filename.lower():
                        found_expected = True
                        best_match = match
                        print(f"      ✅ EXACT MATCH FOUND!")
                    elif any(word in filename.lower() for word in expected_filename.lower().split('_') if len(word) > 3):
                        if not found_expected:  # Only set if we haven't found exact match
                            found_expected = True
                            best_match = match
                            print(f"      ✅ PARTIAL MATCH FOUND!")
                    
                    print()
                
                if found_expected:
                    passed_tests += 1
                    print(f"   🎉 TEST CASE {test_id}: PASSED")
                    
                    detailed_results.append({
                        'test_id': test_id,
                        'query': query,
                        'expected': expected_filename,
                        'found': best_match['filename'],
                        'match_type': best_match['match_type'],
                        'score': best_match['score'],
                        'sheet': best_match['sheet_name'],
                        'search_time': search_time,
                        'status': 'PASSED'
                    })
                else:
                    print(f"   ❌ TEST CASE {test_id}: FAILED - Expected filename not found")
                    
                    detailed_results.append({
                        'test_id': test_id,
                        'query': query,
                        'expected': expected_filename,
                        'found': 'Not Found',
                        'match_type': 'N/A',
                        'score': 0,
                        'sheet': 'N/A',
                        'search_time': search_time,
                        'status': 'FAILED'
                    })
            else:
                print(f"   ❌ TEST CASE {test_id}: FAILED - No matches found")
                
                detailed_results.append({
                    'test_id': test_id,
                    'query': query,
                    'expected': expected_filename,
                    'found': 'No Matches',
                    'match_type': 'N/A',
                    'score': 0,
                    'sheet': 'N/A',
                    'search_time': search_time,
                    'status': 'FAILED'
                })
        else:
            print(f"   ❌ TEST CASE {test_id}: ERROR - Search request failed ({search_response.status_code})")
            
            detailed_results.append({
                'test_id': test_id,
                'query': query,
                'expected': expected_filename,
                'found': 'Search Error',
                'match_type': 'N/A',
                'score': 0,
                'sheet': 'N/A',
                'search_time': search_time,
                'status': 'ERROR'
            })
    
    # Calculate success rate
    success_rate = (passed_tests / total_tests) * 100
    
    print("\n" + "=" * 80)
    print("📊 ENHANCED SEARCH TEST RESULTS SUMMARY")
    print("=" * 80)
    
    print(f"✅ Passed Tests: {passed_tests}/{total_tests}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    # Show detailed results
    print(f"\n📋 DETAILED TEST RESULTS:")
    for result in detailed_results:
        status_icon = "✅" if result['status'] == 'PASSED' else "❌"
        print(f"\n{status_icon} Test {result['test_id']}: {result['status']}")
        print(f"   🔍 Query: \"{result['query']}\"")
        print(f"   📄 Expected: {result['expected'][:60]}...")
        print(f"   📄 Found: {result['found'][:60]}...")
        print(f"   🎯 Match Type: {result['match_type']}")
        print(f"   📊 Score: {result['score']}")
        print(f"   ⏱️  Time: {result['search_time']:.3f}s")
    
    # Performance metrics
    avg_search_time = sum(r['search_time'] for r in detailed_results) / len(detailed_results)
    print(f"\n⚡ PERFORMANCE METRICS:")
    print(f"   📈 Average search time: {avg_search_time:.3f}s")
    print(f"   📈 Total test cases: {total_tests}")
    
    # Final assessment
    print(f"\n🎯 FINAL ASSESSMENT:")
    if success_rate == 100.0:
        print("🎉 PERFECT! All enhanced search test cases passed!")
        print("✅ Intelligent keyword mapping is working flawlessly!")
        print("✅ Fuzzy matching algorithm is performing excellently!")
        print("🚀 Enhanced search functionality ready for production!")
    elif success_rate >= 75.0:
        print("🎯 EXCELLENT! Most enhanced search test cases passed!")
        print("✅ Intelligent keyword mapping is working well!")
        print("🔧 Minor adjustments needed for perfect 100%")
    elif success_rate >= 50.0:
        print("🔧 GOOD PROGRESS! Enhanced search is partially working!")
        print("💡 Need to improve keyword mapping and fuzzy matching")
    else:
        print("❌ NEEDS IMPROVEMENT! Enhanced search requires debugging")
        print("🔧 Keyword mapping and fuzzy matching need major fixes")
    
    return success_rate == 100.0, detailed_results

if __name__ == "__main__":
    print("🚀 ENHANCED SEARCH FUNCTIONALITY TEST")
    print("Testing intelligent keyword mapping and fuzzy matching")
    print("Goal: Achieve 100% pass rate for all 4 test cases")
    
    # Run enhanced search tests
    all_passed, results = test_enhanced_search_cases()
    
    print("\n" + "=" * 80)
    print("🎯 ENHANCED SEARCH TEST COMPLETE")
    print("=" * 80)
    
    if all_passed:
        print("🎉 MISSION ACCOMPLISHED!")
        print("✅ All enhanced search test cases passed!")
        print("🚀 Archives Stems Finder Pro enhanced search is perfect!")
    else:
        failed_count = len([r for r in results if r['status'] != 'PASSED'])
        print(f"🔧 NEEDS IMPROVEMENT: {failed_count} test cases failed")
        print("💡 Continue debugging enhanced search functionality")
    
    print("\n🚀 Enhanced search test complete!")
