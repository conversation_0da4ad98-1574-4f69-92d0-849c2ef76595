#!/usr/bin/env python3
"""
Check cache file directly to see if video IDs exist
"""

import pandas as pd

def check_cache_for_video_ids():
    """Check if the user's video IDs actually exist in the cache"""
    
    print("🔍 CHECKING CACHE FILE DIRECTLY FOR USER'S VIDEO IDs")
    print("=" * 70)
    
    try:
        # Load cache file
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded cache: {len(df):,} rows")
        
        # Check columns
        print(f"📊 Columns: {list(df.columns)}")
        
        # User's specific video IDs
        user_video_ids = [
            'CPN-zOup_uS',  # Should be in Copy Social Media Catalog(IG) row 2906
            'H4qQ7MHACbw',  # Should be in Social Media Catalog(IF) row 1851
            'CO2DQGZgUQL',  # Should be in Social Media Catalog(IG) row 3759
            'COzgvYcgBAx',  # Should be in Social Media Catalog(IG)
            'ChTnwpkCMhg'   # Should be in Copy Social Media Catalog(SG) row 3830
        ]
        
        print(f"\n🔍 SEARCHING FOR USER'S VIDEO IDs:")
        
        for video_id in user_video_ids:
            print(f"\n📹 Searching for: {video_id}")
            
            # Check exact match in video_id column
            exact_matches = df[df['video_id'] == video_id]
            print(f"   Exact video_id matches: {len(exact_matches)}")
            
            # Check partial match in video_id column
            partial_matches = df[df['video_id'].astype(str).str.contains(video_id, na=False)]
            print(f"   Partial video_id matches: {len(partial_matches)}")
            
            # Check in filename
            filename_matches = df[df['filename'].astype(str).str.contains(video_id, na=False)]
            print(f"   Filename matches: {len(filename_matches)}")
            
            # Check in all text
            all_text_matches = df[
                df['filename'].astype(str).str.contains(video_id, na=False) |
                df['ocd_vp'].astype(str).str.contains(video_id, na=False) |
                df['video_id'].astype(str).str.contains(video_id, na=False)
            ]
            print(f"   All text matches: {len(all_text_matches)}")
            
            # Show details if found
            if len(exact_matches) > 0:
                for _, match in exact_matches.iterrows():
                    print(f"   ✅ EXACT MATCH:")
                    print(f"      📄 Filename: {match['filename']}")
                    print(f"      🔢 OCD/VP: {match['ocd_vp']}")
                    print(f"      🎥 Video ID: {match['video_id']}")
                    print(f"      📋 Sheet: {match['sheet_name']}")
            elif len(partial_matches) > 0:
                for _, match in partial_matches.head(1).iterrows():
                    print(f"   ⚠️ PARTIAL MATCH:")
                    print(f"      📄 Filename: {match['filename']}")
                    print(f"      🔢 OCD/VP: {match['ocd_vp']}")
                    print(f"      🎥 Video ID: {match['video_id']}")
                    print(f"      📋 Sheet: {match['sheet_name']}")
            elif len(filename_matches) > 0:
                for _, match in filename_matches.head(1).iterrows():
                    print(f"   📄 FILENAME MATCH:")
                    print(f"      📄 Filename: {match['filename']}")
                    print(f"      🔢 OCD/VP: {match['ocd_vp']}")
                    print(f"      🎥 Video ID: {match['video_id']}")
                    print(f"      📋 Sheet: {match['sheet_name']}")
            else:
                print(f"   ❌ NOT FOUND in cache")
        
        # Check sheets distribution
        print(f"\n📊 SHEETS IN CACHE:")
        sheet_counts = df['sheet_name'].value_counts()
        for sheet, count in sheet_counts.items():
            print(f"   📋 {sheet}: {count:,} rows")
        
        # Check for synthetic matches
        print(f"\n🔧 CHECKING FOR SYNTHETIC MATCHES:")
        synthetic_matches = df[df['filename'].astype(str).str.contains('Archive_Media_File', na=False)]
        print(f"   Synthetic pattern matches: {len(synthetic_matches)}")
        
        if len(synthetic_matches) > 0:
            print(f"   Sample synthetic matches:")
            for _, match in synthetic_matches.head(3).iterrows():
                print(f"      📄 {match['filename'][:60]}...")
                print(f"      🎥 Video ID: {match['video_id']}")
                print(f"      📋 Sheet: {match['sheet_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking cache: {e}")
        return False

def check_specific_rows():
    """Check specific rows mentioned by user"""
    
    print(f"\n🎯 CHECKING SPECIFIC ROWS MENTIONED BY USER:")
    print("=" * 70)
    
    try:
        df = pd.read_csv('archives_cache.csv')
        
        # Check specific rows (note: pandas is 0-indexed, so row 2906 is index 2905)
        specific_checks = [
            {'video_id': 'CPN-zOup_uS', 'sheet': 'Copy Social Media Catalog(IG)', 'row': 2906},
            {'video_id': 'H4qQ7MHACbw', 'sheet': 'Social Media Catalog(IF)', 'row': 1851},
            {'video_id': 'CO2DQGZgUQL', 'sheet': 'Social Media Catalog(IG)', 'row': 3759},
            {'video_id': 'ChTnwpkCMhg', 'sheet': 'Copy Social Media Catalog(SG)', 'row': 3830}
        ]
        
        for check in specific_checks:
            video_id = check['video_id']
            expected_sheet = check['sheet']
            expected_row = check['row']
            
            print(f"\n📍 Checking {video_id} in {expected_sheet} around row {expected_row}:")
            
            # Filter by sheet
            sheet_data = df[df['sheet_name'] == expected_sheet]
            print(f"   📋 {expected_sheet}: {len(sheet_data)} rows")
            
            if len(sheet_data) > 0:
                # Check around the expected row (±10 rows)
                start_idx = max(0, expected_row - 10)
                end_idx = min(len(sheet_data), expected_row + 10)
                
                nearby_rows = sheet_data.iloc[start_idx:end_idx]
                
                # Search for video ID in nearby rows
                found_in_nearby = nearby_rows[
                    nearby_rows['video_id'].astype(str).str.contains(video_id, na=False) |
                    nearby_rows['filename'].astype(str).str.contains(video_id, na=False)
                ]
                
                if len(found_in_nearby) > 0:
                    print(f"   ✅ FOUND in nearby rows!")
                    for _, match in found_in_nearby.iterrows():
                        print(f"      📄 Filename: {match['filename']}")
                        print(f"      🎥 Video ID: {match['video_id']}")
                        print(f"      🔢 OCD/VP: {match['ocd_vp']}")
                else:
                    print(f"   ❌ NOT FOUND in nearby rows")
                    
                    # Show sample data from that area
                    print(f"   📝 Sample data around row {expected_row}:")
                    for i, (_, row) in enumerate(nearby_rows.head(3).iterrows()):
                        print(f"      {i+1}. {row['filename'][:40]}... | {row['video_id']} | {row['ocd_vp']}")
            else:
                print(f"   ❌ Sheet '{expected_sheet}' not found in cache")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking specific rows: {e}")
        return False

if __name__ == "__main__":
    print("🚀 DIRECT CACHE FILE ANALYSIS")
    print("Checking if user's video IDs actually exist in the cache file")
    
    # Check cache for video IDs
    cache_ok = check_cache_for_video_ids()
    
    # Check specific rows
    rows_ok = check_specific_rows()
    
    print("\n" + "=" * 70)
    print("🎯 DIRECT CACHE ANALYSIS RESULTS")
    print("=" * 70)
    
    if cache_ok and rows_ok:
        print("✅ Cache analysis completed successfully")
        print("💡 This will help identify why the search function isn't finding the video IDs")
    else:
        print("❌ Cache analysis had issues")
        print("🔧 Need to investigate cache file structure")
    
    print("\n🚀 Direct cache analysis complete!")
