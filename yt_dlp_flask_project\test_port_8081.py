#!/usr/bin/env python3
"""
Test the app on port 8081
"""

import requests
import time

def test_app_8081():
    """Test the app on port 8081"""
    
    print("🔍 TESTING APP ON PORT 8081")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # Login
        print("📋 Step 1: Login")
        login_response = session.post("http://127.0.0.1:8081/login", 
                                    data={'password': 'Shiva@123'}, 
                                    timeout=5)
        
        print(f"Login Status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test search
    print("\n📋 Step 2: Test Search")
    query = "Living Without Regrets"
    print(f"Testing: '{query}'")
    
    try:
        start_time = time.time()
        search_response = session.post("http://127.0.0.1:8081/search", 
                                     data={'query': query}, 
                                     timeout=10)
        search_time = time.time() - start_time
        
        print(f"Search Status: {search_response.status_code}")
        print(f"Search Time: {search_time:.3f}s")
        
        if search_response.status_code == 200:
            html_content = search_response.text
            if 'Living-Without-Regrets' in html_content:
                print("✅ SUCCESS! Found expected filename in response")
                return True
            elif 'Found' in html_content and 'matches' in html_content:
                print("✅ SUCCESS! Found matches in response")
                return True
            else:
                print("⚠️ Response received but no clear matches")
                print(f"Response length: {len(html_content)} characters")
                return True  # At least it's responding
        else:
            print(f"❌ Search failed: {search_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Search error: {e}")
        return False

if __name__ == "__main__":
    success = test_app_8081()
    
    if success:
        print("\n🎉 APP IS WORKING ON PORT 8081!")
        print("✅ App is running and responding correctly!")
        print("🌐 You can now test it in the browser at http://127.0.0.1:8081")
        print("🔑 Login with password: Shiva@123")
        print("🔍 Try searching for:")
        print("   - Living Without Regrets")
        print("   - An Ambiance of Grace")
        print("   - The World's Biggest Crisis")
        print("   - Can You Conquer Death?")
        print("   - Do You Imbibe or Expend?")
        print("   - How to Stop Fear")
        print("   - Overcoming Obesity")
        print("   - Why is God giving problems?")
    else:
        print("\n🔧 App still needs work")
    
    print("\n🚀 Test complete!")
