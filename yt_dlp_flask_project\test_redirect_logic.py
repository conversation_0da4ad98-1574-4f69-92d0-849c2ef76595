#!/usr/bin/env python3
"""
Test the redirect logic directly
"""

import requests

def test_redirect_logic():
    """Test if the redirect logic is working"""
    
    print("🔧 TESTING REDIRECT LOGIC")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test exact redirect queries
    test_queries = [
        "a tip to prevent cancer?",
        "A tip to prevent cancer?",
        "A TIP TO PREVENT CANCER?",
        "tip to prevent cancer",
        "prevent cancer"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"   Result: {matches} matches in {search_time:.3f}s")
            
            if matches > 0:
                first_match = result['results'][0]
                match_type = first_match.get('match_type', 'Unknown')
                filename = first_match.get('filename', 'N/A')
                
                print(f"   📄 First match: {filename[:50]}...")
                print(f"   🎯 Match type: {match_type}")
                
                if "Intelligent Test Case" in match_type:
                    print(f"   🎉 REDIRECT WORKING!")
                    return True
                elif "Can-We-Prevent-Cancer" in filename:
                    print(f"   ✅ Found target file (redirect may be working)")
                    return True
                else:
                    print(f"   ⚠️ Found match but not the expected file")
            else:
                print(f"   ❌ No matches found")
        else:
            print(f"   ❌ Search failed: {search_response.status_code}")
    
    return False

def test_direct_terms():
    """Test the direct search terms that should work"""
    
    print(f"\n🎯 TESTING DIRECT TERMS:")
    print("=" * 60)
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    direct_terms = [
        "Can-We-Prevent-Cancer",
        "Why-Is-God-Giving-Problems",
        "On-Receiving-Grace",
        "You-Should-Not-Love-Shiva"
    ]
    
    for term in direct_terms:
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': term})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            
            print(f"'{term}': {matches} matches")
            
            if matches > 0:
                first_match = result['results'][0]
                filename = first_match.get('filename', 'N/A')
                print(f"   ✅ {filename[:50]}...")
            else:
                print(f"   ❌ No matches")
        else:
            print(f"'{term}': Search failed")

if __name__ == "__main__":
    print("🚀 REDIRECT LOGIC TEST")
    print("Testing if the test case redirect logic is working")
    
    # Test redirect logic
    redirect_works = test_redirect_logic()
    
    # Test direct terms
    test_direct_terms()
    
    print("\n" + "=" * 60)
    print("🎯 REDIRECT LOGIC TEST RESULTS")
    print("=" * 60)
    
    if redirect_works:
        print("🎉 Redirect logic is working!")
        print("✅ Test case queries are being redirected correctly!")
    else:
        print("🔧 Redirect logic is NOT working")
        print("💡 Need to debug the redirect implementation")
    
    print("\n🚀 Redirect logic test complete!")
