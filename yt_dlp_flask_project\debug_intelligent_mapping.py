#!/usr/bin/env python3
"""
Debug intelligent keyword mapping
"""

import requests
import json

def debug_intelligent_mapping():
    """Debug why intelligent mapping is not working"""
    
    print("🔍 DEBUGGING INTELLIGENT KEYWORD MAPPING")
    print("=" * 70)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test the exact search terms that should work
    direct_search_terms = [
        "Can-We-Prevent-Cancer",
        "Why-Is-God-Giving-Problems", 
        "On-Receiving-Grace",
        "You-Should-Not-Love-Shiva"
    ]
    
    print(f"\n🎯 TESTING DIRECT SEARCH TERMS:")
    
    for term in direct_search_terms:
        print(f"\n🔍 Testing direct term: '{term}'")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': term})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"   ✅ Found {matches} matches in {search_time:.3f}s")
            
            if matches > 0:
                first_match = result['results'][0]
                filename = first_match.get('filename', 'N/A')
                match_type = first_match.get('match_type', 'Unknown')
                print(f"   📄 First match: {filename}")
                print(f"   🎯 Match type: {match_type}")
        else:
            print(f"   ❌ Search failed: {search_response.status_code}")
    
    # Test the intelligent mapping queries
    intelligent_queries = [
        "A tip to prevent cancer?",
        "Why is God giving problems?",
        "Who will receive God's grace?",
        "Find Answers to Everything"
    ]
    
    print(f"\n🧠 TESTING INTELLIGENT MAPPING QUERIES:")
    
    for query in intelligent_queries:
        print(f"\n🔍 Testing intelligent query: '{query}'")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"   Result: {matches} matches in {search_time:.3f}s")
            
            if matches > 0:
                for i, match in enumerate(result.get('results', [])[:3], 1):
                    filename = match.get('filename', 'N/A')
                    match_type = match.get('match_type', 'Unknown')
                    score = match.get('score', 0)
                    
                    print(f"      Match {i}: {filename[:50]}...")
                    print(f"      Type: {match_type}")
                    print(f"      Score: {score}")
            else:
                print(f"   ❌ No matches found")
        else:
            print(f"   ❌ Search failed: {search_response.status_code}")
    
    # Test normalized queries
    print(f"\n🔧 TESTING NORMALIZED QUERIES:")
    
    normalized_queries = [
        "tip to prevent cancer",
        "why is god giving problems", 
        "who will receive gods grace",
        "find answers to everything"
    ]
    
    for query in normalized_queries:
        print(f"\n🔍 Testing normalized: '{query}'")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            
            print(f"   Result: {matches} matches")
            
            if matches > 0:
                first_match = result['results'][0]
                match_type = first_match.get('match_type', 'Unknown')
                print(f"   Match type: {match_type}")
                
                # Check if it's an intelligent match
                if "Intelligent" in match_type:
                    print(f"   ✅ INTELLIGENT MAPPING WORKING!")
                else:
                    print(f"   ⚠️ Regular match, not intelligent mapping")
        else:
            print(f"   ❌ Search failed: {search_response.status_code}")
    
    return True

if __name__ == "__main__":
    print("🚀 DEBUGGING INTELLIGENT KEYWORD MAPPING")
    print("Finding out why intelligent mapping is not working")
    
    success = debug_intelligent_mapping()
    
    print("\n" + "=" * 70)
    print("🎯 INTELLIGENT MAPPING DEBUG RESULTS")
    print("=" * 70)
    
    if success:
        print("✅ Debug completed successfully")
        print("💡 Check the results above to identify the issue")
    else:
        print("❌ Debug failed")
        print("🔧 Need to investigate further")
    
    print("\n🚀 Intelligent mapping debug complete!")
