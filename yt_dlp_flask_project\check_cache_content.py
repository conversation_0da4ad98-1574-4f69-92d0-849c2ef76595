#!/usr/bin/env python3
"""
Check what's actually in the current cache
"""

import pandas as pd

def check_cache_content():
    """Check the current cache content and structure"""
    
    print("🔍 Checking Current Cache Content")
    print("=" * 60)
    
    try:
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Cache loaded: {len(df):,} rows")
        
        # Check columns
        print(f"\n📋 Columns: {list(df.columns)}")
        
        # Check data types
        print(f"\n📊 Data Types:")
        for col in df.columns:
            print(f"   {col}: {df[col].dtype}")
        
        # Sample data
        print(f"\n📄 Sample Data (first 5 rows):")
        print(df.head().to_string())
        
        # Check for OCD numbers
        print(f"\n🔍 OCD/VP Number Analysis:")
        if 'ocd_vp' in df.columns:
            ocd_count = df['ocd_vp'].notna().sum()
            print(f"   Non-null OCD/VP entries: {ocd_count:,}")
            
            # Sample OCD numbers
            sample_ocds = df[df['ocd_vp'].notna()]['ocd_vp'].head(10).tolist()
            print(f"   Sample OCD/VP numbers: {sample_ocds}")
            
            # Check for specific OCD numbers
            test_ocds = ['OCD-15170', 'OCD-16908', 'OCD-17266']
            for ocd in test_ocds:
                matches = df[df['ocd_vp'].astype(str).str.contains(ocd, case=False, na=False)]
                print(f"   {ocd}: {len(matches)} matches")
        
        # Check for video IDs
        print(f"\n🎥 Video ID Analysis:")
        if 'video_id' in df.columns:
            video_count = df['video_id'].notna().sum()
            print(f"   Non-null Video ID entries: {video_count:,}")
            
            # Sample video IDs
            sample_videos = df[df['video_id'].notna()]['video_id'].head(10).tolist()
            print(f"   Sample Video IDs: {sample_videos}")
            
            # Check for specific video IDs
            test_videos = ['oWBTMp35RfA', 'dDLvjAk50gc', 'H4qQ7MHACbw']
            for video in test_videos:
                matches = df[df['video_id'].astype(str).str.contains(video, case=False, na=False)]
                print(f"   {video}: {len(matches)} matches")
        
        # Check sheet distribution
        print(f"\n📊 Sheet Distribution:")
        if 'sheet_name' in df.columns:
            sheet_counts = df['sheet_name'].value_counts()
            for sheet, count in sheet_counts.items():
                print(f"   {sheet}: {count:,} rows")
        
        # Check date range in filenames
        print(f"\n📅 Date Analysis (from filenames):")
        if 'filename' in df.columns:
            # Look for 2024 and 2025 dates
            files_2024 = df[df['filename'].str.contains('2024', na=False)]
            files_2025 = df[df['filename'].str.contains('2025', na=False)]
            print(f"   Files with '2024': {len(files_2024):,}")
            print(f"   Files with '2025': {len(files_2025):,}")
            
            # Sample recent files
            recent_files = df[df['filename'].str.contains('2025', na=False)]['filename'].head(5).tolist()
            print(f"   Sample 2025 files:")
            for file in recent_files:
                print(f"      - {file}")
        
    except Exception as e:
        print(f"❌ Error checking cache: {e}")

if __name__ == "__main__":
    check_cache_content()
