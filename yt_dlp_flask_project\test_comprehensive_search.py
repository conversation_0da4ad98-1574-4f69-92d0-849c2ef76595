#!/usr/bin/env python3
"""
Test comprehensive search across ALL 8 sheets based on user's analysis
"""

import requests
import json

def test_user_examples():
    """Test the specific examples provided by the user"""
    
    print("🔍 TESTING USER'S SPECIFIC EXAMPLES")
    print("=" * 70)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test cases based on user's analysis
    test_cases = [
        {
            'video_id': 'CPN-zOup_uS',
            'expected_sheet': 'Copy Social Media Catalog(IG)',
            'expected_row': 2906,
            'expected_content': 'Daily-Mystic-Quote with Z9906',
            'description': 'Not in Social Media Catalog(IG) row 3744, BUT in Copy Social Media Catalog(IG) row 2906'
        },
        {
            'video_id': 'H4qQ7MHACbw',
            'expected_sheet': 'Social Media Catalog(IF)',
            'expected_row': 1851,
            'expected_content': 'Available in IF sheet',
            'description': 'Available in Social Media Catalog(IF) row 1851'
        },
        {
            'video_id': 'CO2DQGZgUQL',
            'expected_sheet': 'Social Media Catalog(IG)',
            'expected_row': 3759,
            'expected_content': 'Available in IG sheet',
            'description': 'Available in Social Media Catalog(IG) row 3759'
        },
        {
            'video_id': 'COzgvYcgBAx',
            'expected_sheet': 'Social Media Catalog(IG)',
            'expected_row': 0,
            'expected_content': 'Available in IG sheet',
            'description': 'Available in Social Media Catalog(IG)'
        },
        {
            'video_id': 'ChTnwpkCMhg',
            'expected_sheet': 'Copy Social Media Catalog(SG)',
            'expected_row': 3830,
            'expected_content': 'Not in Social Media Catalog(SG) but in Copy',
            'description': 'Not in Social Media Catalog(SG), BUT in Copy Social Media Catalog(SG) row 3830'
        }
    ]
    
    found_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        video_id = test_case['video_id']
        expected_sheet = test_case['expected_sheet']
        expected_row = test_case['expected_row']
        description = test_case['description']
        
        print(f"\n{i}. Testing: {video_id}")
        print(f"   📋 Expected: {description}")
        print(f"   📊 Should be in: {expected_sheet} (row {expected_row})")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': video_id})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"   🔍 Search result: {matches} matches in {search_time:.3f}s")
            
            if matches > 0:
                found_count += 1
                print(f"   ✅ FOUND!")
                
                # Analyze the matches
                for j, match in enumerate(result.get('results', [])[:3], 1):
                    filename = match.get('filename', 'N/A')
                    ocd_vp = match.get('ocd_vp', 'N/A')
                    video_id_found = match.get('video_id', 'N/A')
                    sheet_name = match.get('sheet_name', 'N/A')
                    match_type = match.get('match_type', 'Unknown')
                    score = match.get('score', 0)
                    row_index = match.get('row_index', 0)
                    
                    print(f"      Match {j}:")
                    print(f"      📄 Filename: {filename[:60]}...")
                    print(f"      🔢 OCD/VP: {ocd_vp}")
                    print(f"      🎥 Video ID: {video_id_found}")
                    print(f"      📋 Sheet: {sheet_name}")
                    print(f"      🎯 Match Type: {match_type}")
                    print(f"      📊 Score: {score}")
                    print(f"      📍 Row: {row_index}")
                    
                    # Check if it matches expected location
                    if sheet_name == expected_sheet:
                        print(f"      🎉 PERFECT: Found in expected sheet!")
                        if expected_row > 0 and row_index == expected_row:
                            print(f"      🎯 EXACT: Found at expected row {expected_row}!")
                    
                    # Special checks for specific video IDs
                    if video_id == "CPN-zOup_uS":
                        if "Daily-Mystic-Quote" in filename or "Z9906" in filename or "Z9906" in ocd_vp:
                            print(f"      🌟 EXCELLENT: Found the Daily-Mystic-Quote with Z9906!")
                    
                    print()
            else:
                print(f"   ❌ NOT FOUND: No matches")
        else:
            print(f"   ❌ SEARCH ERROR: Status {search_response.status_code}")
    
    success_rate = (found_count / len(test_cases)) * 100
    
    print(f"\n📊 USER EXAMPLES TEST RESULTS:")
    print(f"✅ Found: {found_count}/{len(test_cases)} ({success_rate:.1f}%)")
    
    return found_count == len(test_cases)

def test_comprehensive_search_all_missing():
    """Test all missing video IDs to verify comprehensive search"""
    
    print(f"\n🔍 TESTING ALL MISSING VIDEO IDs WITH COMPREHENSIVE SEARCH")
    print("=" * 70)
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    # All missing video IDs from previous tests
    missing_video_ids = [
        "CPN-zOup_uS", "CO2DQGZgUQL", "CO0NM2AIvRx", "COzgvYcgBAx", "COw40WfBzWZ",
        "COxhRudAD4k", "COuaS9mBRjR", "COvKclMopkO", "Bm-QyuLHutC", "Bm7zt7_nWN4",
        "BkxrLGZHN1r", "Bi9PLrYH3Ef", "rbYdXbEVm6E", "ChTnwpkCMhg", "KX_pnMG-4RE",
        "IH23o77OZXw", "FBYoZ-FgC84", "g7SvHaSzz9A", "5Rr13rAlifM", "-tLR-BztVKI",
        "8oSuwfAfWh4", "W2qSmUq3YQk", "yBJqCW6bxsY", "H4qQ7MHACbw"
    ]
    
    found_count = 0
    sheets_found = set()
    
    for i, video_id in enumerate(missing_video_ids, 1):
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': video_id})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            if matches > 0:
                found_count += 1
                first_match = result['results'][0]
                sheet_name = first_match.get('sheet_name', 'Unknown')
                match_type = first_match.get('match_type', 'Unknown')
                sheets_found.add(sheet_name)
                
                status = "✅"
                print(f"{i:2d}. {status} {video_id}: {matches} matches in {search_time:.3f}s ({sheet_name}) - {match_type}")
            else:
                status = "❌"
                print(f"{i:2d}. {status} {video_id}: No matches in {search_time:.3f}s")
        else:
            print(f"{i:2d}. ❌ {video_id}: Search error")
    
    success_rate = (found_count / len(missing_video_ids)) * 100
    
    print(f"\n📊 COMPREHENSIVE SEARCH RESULTS:")
    print(f"✅ Found: {found_count}/{len(missing_video_ids)} ({success_rate:.1f}%)")
    print(f"📋 Sheets searched: {sorted(sheets_found)}")
    print(f"📊 Total sheets found in: {len(sheets_found)}")
    
    if success_rate == 100.0:
        print("🎉 PERFECT: 100% SUCCESS RATE ACHIEVED!")
        print("✅ Comprehensive search across ALL 8 sheets is working!")
    elif success_rate >= 90.0:
        print("🎯 EXCELLENT: Near-perfect success rate!")
        print("🔧 Minor adjustments needed for perfect 100%")
    else:
        print("🔧 NEEDS IMPROVEMENT: Comprehensive search needs debugging")
    
    return success_rate

if __name__ == "__main__":
    print("🚀 TESTING COMPREHENSIVE SEARCH ACROSS ALL 8 SHEETS")
    print("Based on user's analysis of specific video ID locations")
    
    # Test user's specific examples
    user_examples_success = test_user_examples()
    
    # Test all missing video IDs
    overall_success_rate = test_comprehensive_search_all_missing()
    
    print("\n" + "=" * 70)
    print("🎯 COMPREHENSIVE SEARCH TEST RESULTS")
    print("=" * 70)
    
    if user_examples_success and overall_success_rate == 100.0:
        print("🎉 MISSION ACCOMPLISHED!")
        print("✅ 100% SUCCESS RATE ACHIEVED!")
        print("✅ Comprehensive search across ALL 8 sheets working perfectly!")
        print("🚀 Archives Stems Finder Pro now finds EVERY video ID!")
    elif user_examples_success and overall_success_rate >= 90.0:
        print("🎯 EXCELLENT PROGRESS!")
        print("✅ User examples working perfectly!")
        print(f"✅ {overall_success_rate:.1f}% overall success rate!")
        print("🔧 Minor fine-tuning needed for perfect 100%")
    else:
        print("🔧 NEEDS DEBUGGING")
        print("⚠️ Comprehensive search not working as expected")
        print("💡 Need to investigate search algorithm across all sheets")
    
    print(f"\n📈 User Examples: {'✅' if user_examples_success else '❌'}")
    print(f"📈 Overall Success Rate: {overall_success_rate:.1f}%")
    print("\n🚀 Comprehensive search test complete!")
