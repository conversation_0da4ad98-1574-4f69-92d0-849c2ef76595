#!/usr/bin/env python3
"""
Direct performance test using the cache file
"""

import pandas as pd
import time
import re
from urllib.parse import urlparse, parse_qs

# Test queries from user
test_queries = [
    "https://youtu.be/Nr_ldMy87lI",
    "https://youtu.be/SMQ-rJlOpSU", 
    "https://youtu.be/-W52myd9nHE",
    "https://youtu.be/SZLK7WYQE8E",
    "dZj8o9hMUX8",
    "Kgowgm1KeZ4",
    "3amX-jVo4-U",
    "OCD-9585",
    "https://youtu.be/AHS1c_vqjxI",
    "https://youtu.be/BtPrmLLHtKY",
    "https://youtu.be/TMHhylNs-3Q",
    "https://youtu.be/rbYdXbEVm6E",
    "QEkHcPt-Vpw",
    "2UHwPxgmfg8",
    "e2UN3d60IQM",
    "Nkh5y4R_RD0",
    "Z3633_Talks-Uploaded-In-Sadhguru-YouTube-Channel-On-Sep-2016-Eng-4Mins-1Mov-HD-With-Logo",
    "Q-And-A_What-Are-The-Limits-Of-My-Mind_07-Sep-2016_English_09Mins-54Secs_MOV",
    "Promo_What-Is-Inner-Engineering_02-Sep-2016_English_07Mins-35Secs_MOV",
    "BmDc0eglgIN",
    "BlcwSDZnvRC",
    "BlaKLssDhRm",
    "BkxrLGZHN1r",
    "Bi9PLrYH3Ef",
    "https://www.instagram.com/p/BtAa0Mihu85/",
    "https://www.instagram.com/reel/BtAa0Mihu85",
    "https://www.instagram.com/p/Bs5j5Owh_aM/",
    "https://www.instagram.com/p/BsvPnhAhB37/",
    "https://www.instagram.com/reel/BsvPnhAhB37",
    "TMHhylNs-3Q",
    "rbYdXbEVm6E",
    "https://youtu.be/5WGshwt_RKw",
    "https://youtu.be/grTMDBwVOPo",
    "https://youtu.be/Vu45G9lMQa0",
    "https://www.instagram.com/reel/DAtKuIAvcs1",
    "https://www.instagram.com/reel/DAsxYHXMGCx",
    "OCD-7152",
    "OCD-8906",
    "OCD-8532",
    "https://youtu.be/4LTe2vsmD_8",
    "https://youtu.be/apK41d5HWfc"
]

def extract_video_id_from_url(url):
    """Extract video ID from various social media URLs"""
    # Instagram patterns
    instagram_patterns = [
        r'instagram\.com/p/([A-Za-z0-9_-]+)',
        r'instagram\.com/reel/([A-Za-z0-9_-]+)'
    ]
    
    for pattern in instagram_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    # YouTube patterns
    youtube_patterns = [
        r'youtube\.com/watch\?v=([A-Za-z0-9_-]+)',
        r'youtu\.be/([A-Za-z0-9_-]+)',
        r'youtube\.com/embed/([A-Za-z0-9_-]+)',
        r'youtube\.com/shorts/([A-Za-z0-9_-]+)'
    ]
    
    for pattern in youtube_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None

def detect_search_type(query):
    """Detect search type"""
    original_query = query.strip()
    query = query.strip().upper()
    
    # Check for URLs first
    if any(domain in original_query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
        video_id = extract_video_id_from_url(original_query)
        if video_id:
            return 'video_id'
        return 'url'
    
    # Check for OCD/VP numbers
    if query.startswith('OCD-') or query.startswith('VP-') or (query.startswith(('OCD', 'VP')) and any(c.isdigit() for c in query)):
        return 'ocd_number'
    
    # Check for Video ID (11 characters)
    if len(query) == 11 and query.replace('_', '').replace('-', '').isalnum():
        return 'video_id'
    
    return 'title_search'

def search_cache(df, query):
    """Search the cache dataframe"""
    search_type = detect_search_type(query)
    
    if search_type == 'video_id':
        if any(domain in query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
            extracted_video_id = extract_video_id_from_url(query)
            if extracted_video_id:
                query = extracted_video_id
        
        # Search in video_id and video_ids columns
        mask = (df['video_id'].astype(str).str.contains(query, na=False, regex=False) |
                df['video_ids'].astype(str).str.contains(query, na=False, regex=False))
        
    elif search_type == 'ocd_number':
        # Search in ocd_vp column
        mask = df['ocd_vp'].astype(str).str.upper().str.contains(query.upper(), na=False, regex=False)
        
    else:
        # Search in filename
        mask = df['filename'].astype(str).str.lower().str.contains(query.lower(), na=False, regex=False)
    
    results = df[mask]
    return len(results)

def test_performance():
    """Test search performance directly on cache"""
    print("🚀 DIRECT CACHE PERFORMANCE TEST")
    print("=" * 80)
    
    # Load cache
    cache_file = 'archives_cache.csv'
    try:
        start_time = time.time()
        df = pd.read_csv(cache_file)
        load_time = time.time() - start_time
        print(f"📂 Cache loaded: {len(df)} rows in {load_time:.3f}s")
    except Exception as e:
        print(f"❌ Error loading cache: {e}")
        return
    
    print(f"🔍 Testing {len(test_queries)} queries...")
    print("=" * 80)
    
    total_time = 0
    fast_searches = 0
    
    for i, query in enumerate(test_queries, 1):
        try:
            start_time = time.time()
            matches = search_cache(df, query)
            elapsed_time = time.time() - start_time
            total_time += elapsed_time
            
            # Performance indicator
            if elapsed_time <= 2.0:
                perf_icon = "🚀"  # Fast
                fast_searches += 1
            elif elapsed_time <= 5.0:
                perf_icon = "⚡"  # Good
            else:
                perf_icon = "🐌"  # Slow
            
            print(f"{perf_icon} Query {i:2d}: {elapsed_time:.3f}s | {matches:2d} matches | {query[:50]}")
            
        except Exception as e:
            print(f"💥 Query {i:2d}: Error - {e} | {query[:50]}")
    
    # Summary
    print("=" * 80)
    avg_time = total_time / len(test_queries) if test_queries else 0
    print(f"📊 PERFORMANCE SUMMARY:")
    print(f"   Total queries: {len(test_queries)}")
    print(f"   Fast searches (≤2s): {fast_searches}")
    print(f"   Average time: {avg_time:.3f}s")
    print(f"   Total time: {total_time:.3f}s")
    
    if avg_time <= 2.0:
        print(f"🎉 EXCELLENT! Average search time is under 2 seconds!")
    elif avg_time <= 5.0:
        print(f"✅ GOOD! Average search time is under 5 seconds")
    else:
        print(f"⚠️  SLOW! Average search time is over 5 seconds - needs optimization")
    
    # Performance percentage
    fast_percentage = (fast_searches / len(test_queries)) * 100
    print(f"🎯 Fast search rate: {fast_percentage:.1f}% (target: 100%)")

if __name__ == "__main__":
    test_performance()
