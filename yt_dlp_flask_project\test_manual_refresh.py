#!/usr/bin/env python3
"""
Test manual cache refresh functionality
"""

import requests
import time

def test_manual_refresh():
    """Test the manual cache refresh endpoint"""
    
    print("🔄 Testing Manual Cache Refresh")
    print("=" * 50)
    
    try:
        # Test the cache status endpoint first
        print("📊 Checking cache status...")
        status_response = requests.get("http://127.0.0.1:5000/cache/status", timeout=10)
        
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"   Cache loaded: {status_data.get('cache_loaded')}")
            print(f"   Cache size: {status_data.get('cache_size')}")
            print(f"   Last updated: {status_data.get('last_updated')}")
        else:
            print(f"   ❌ Status check failed: {status_response.status_code}")
            if status_response.status_code == 302:
                print("   ⚠️ Redirected to login (authentication required)")
                return
        
        print("\n🔄 Testing manual refresh...")
        print("   Note: This requires authentication. Please login in browser first.")
        
        # Test manual refresh endpoint
        refresh_response = requests.post("http://127.0.0.1:5000/cache/refresh", timeout=30)
        
        print(f"   Response status: {refresh_response.status_code}")
        
        if refresh_response.status_code == 200:
            refresh_data = refresh_response.json()
            if refresh_data.get('success'):
                print(f"   ✅ SUCCESS: {refresh_data.get('message')}")
                print(f"   Cache size: {refresh_data.get('cache_size')}")
            else:
                print(f"   ❌ FAILED: {refresh_data.get('error')}")
        elif refresh_response.status_code == 302:
            print("   ⚠️ Redirected to login (authentication required)")
            print("   Please login in browser first, then try manual refresh from UI")
        elif refresh_response.status_code == 400:
            try:
                error_data = refresh_response.json()
                print(f"   ❌ BAD REQUEST: {error_data.get('error')}")
            except:
                print(f"   ❌ BAD REQUEST: {refresh_response.text}")
        else:
            print(f"   ❌ HTTP Error: {refresh_response.status_code}")
            try:
                error_data = refresh_response.json()
                print(f"   Error: {error_data.get('error')}")
            except:
                print(f"   Response: {refresh_response.text[:200]}...")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    print(f"\n📊 Manual Refresh Test Complete")
    print("=" * 50)

if __name__ == "__main__":
    test_manual_refresh()
