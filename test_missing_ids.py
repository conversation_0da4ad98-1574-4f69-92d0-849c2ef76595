#!/usr/bin/env python3
"""
Test for missing IDs in the cache
"""

import requests
import time

def test_missing_ids():
    """Test the missing IDs"""
    base_url = "http://127.0.0.1:8080"
    
    # List of missing IDs to test
    missing_ids = [
        "OCD-16673", "OCD-16668", "OCD-16698", "OCD-15138", "OCD-16697", 
        "OCD-16677", "OCD-16693", "VP-9992", "VP-8882", "VP-5552", 
        "VP-3332", "VP-1112", "VP-0002", "VP-42222", "C9SQ3zxyYO9", 
        "DKrifQ0T_8Z", "A75b1NKWCC4"
    ]
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        if login_response.status_code in [200, 302]:
            print("Login successful")
        else:
            print(f"Login failed: {login_response.status_code}")
            return
        
        # Check cache status
        print("\nChecking cache status...")
        status_response = session.get(f"{base_url}/cache/status")
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"Cache loaded: {status_data.get('cache_loaded', False)}")
            print(f"Cache size: {status_data.get('cache_size', 0):,} records")
            print(f"Last updated: {status_data.get('last_updated', 'N/A')}")

            if status_data.get('cache_size', 0) == 0:
                print("CRITICAL: Cache is empty! This explains why IDs are missing.")
                return
        else:
            print(f"Cache status check failed: {status_response.status_code}")
        
        # Test each missing ID
        print(f"\nTesting {len(missing_ids)} missing IDs...")
        found_count = 0
        missing_count = 0

        for i, test_id in enumerate(missing_ids, 1):
            print(f"\n[{i}/{len(missing_ids)}] Testing: {test_id}")

            start_time = time.time()
            search_response = session.post(f"{base_url}/search", data={'query': test_id})
            elapsed_time = time.time() - start_time

            if search_response.status_code == 200:
                result_data = search_response.json()
                total_matches = result_data.get('total_matches', 0)

                if total_matches > 0:
                    print(f"  FOUND: {total_matches} matches in {elapsed_time:.3f}s")
                    found_count += 1
                    # Show first result
                    if result_data.get('results'):
                        first_result = result_data['results'][0]
                        print(f"     File: {first_result.get('filename', 'N/A')[:60]}...")
                        print(f"     OCD/VP: {first_result.get('ocd_vp', 'N/A')}")
                else:
                    print(f"  NOT FOUND in {elapsed_time:.3f}s")
                    missing_count += 1
            else:
                print(f"  Search error: {search_response.status_code}")
                missing_count += 1
        
        # Summary
        print(f"\nSUMMARY:")
        print(f"Found: {found_count}/{len(missing_ids)} IDs")
        print(f"Missing: {missing_count}/{len(missing_ids)} IDs")
        print(f"Success Rate: {(found_count/len(missing_ids)*100):.1f}%")

        if missing_count > 0:
            print(f"\nISSUE: {missing_count} IDs are still missing from cache!")
            print("This indicates the cache is not loading the latest data from Google Sheets.")
        else:
            print(f"\nSUCCESS: All IDs found in cache!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_missing_ids()
