#!/usr/bin/env python3
"""
Test specific search functionality via web interface
"""

import requests
import time

def test_specific_search():
    """Test the specific URL search"""
    base_url = "http://127.0.0.1:8080"
    test_url = "https://youtu.be/rbYdXbEVm6E"
    
    # Create session
    session = requests.Session()
    
    print("Testing specific URL search...")
    print(f"URL: {test_url}")
    print("=" * 50)
    
    try:
        # Check if server is running
        health_check = session.get(f"{base_url}/", timeout=5)
        print(f"Server status: {health_check.status_code}")
        
        # Login
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        if login_response.status_code == 302:
            print("Login successful")
        else:
            print(f"Login failed: {login_response.status_code}")
            return
        
        # Test the specific search
        start_time = time.time()
        search_response = session.post(f"{base_url}/search", data={'query': test_url})
        elapsed_time = time.time() - start_time
        
        if search_response.status_code == 200:
            result_data = search_response.json()
            
            print(f"Search completed in {elapsed_time:.3f}s")
            print(f"Success: {result_data.get('success', False)}")
            print(f"Total matches: {result_data.get('total_matches', 0)}")
            print(f"Search type: {result_data.get('search_type', 'Unknown')}")
            
            results = result_data.get('results', [])
            if results:
                print("\nFirst result:")
                first_result = results[0]
                print(f"  Filename: {first_result.get('filename', 'N/A')}")
                print(f"  Duration: {first_result.get('duration', 'N/A')}")
                print(f"  OCD/VP: {first_result.get('ocd_vp', 'N/A')}")
                print(f"  Video ID: {first_result.get('video_id', 'N/A')}")
                print(f"  Drive Link: {first_result.get('drive_link', 'N/A')}")
                print(f"  Score: {first_result.get('score', 'N/A')}")
                
                print("\nSUCCESS! Search is working perfectly!")
            else:
                print("\nNo results found")
        else:
            print(f"Search failed: {search_response.status_code}")
            print(f"Response: {search_response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_specific_search()
