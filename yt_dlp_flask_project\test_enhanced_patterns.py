#!/usr/bin/env python3
"""
Test enhanced pattern matching for specific user examples
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:8080"
PASSWORD = "Shiva@123"

def test_enhanced_patterns():
    """Test enhanced pattern matching"""
    
    session = requests.Session()
    
    print("🎯 TESTING ENHANCED PATTERN MATCHING")
    print("=" * 60)
    
    # Login first
    login_response = session.post(f"{BASE_URL}/login", data={'password': PASSWORD})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test specific patterns that should work now
    test_cases = [
        # User's specific example
        ("CPN-zOup_uS", "Should find Daily-Mystic-Quote with Z9906"),
        ("Z9906", "Should find the Daily-Mystic-Quote file directly"),
        
        # Pattern variations that exist in data
        ("CpNLoKJpHLV", "Direct match for CpN pattern"),
        ("Co2HadEujB3", "Direct match for Co2 pattern"),
        ("Co0We6qvCpM", "Direct match for Co0 pattern"),
        
        # Test some of the user's other patterns
        ("CO2DQGZgUQL", "Should match Co2 pattern"),
        ("CO0NM2AIvRx", "Should match Co0 pattern"),
        ("COzgvYcgBAx", "Should match Coz pattern"),
        
        # Test filename patterns
        ("Daily-Mystic-Quote", "Should find many Daily Mystic Quote files"),
        ("Stems not available", "Should find the specific file mentioned"),
    ]
    
    found_count = 0
    total_tests = len(test_cases)
    
    for i, (query, description) in enumerate(test_cases, 1):
        print(f"\n{i:2d}. Testing: '{query}'")
        print(f"    Expected: {description}")
        
        search_response = session.post(f"{BASE_URL}/search", data={'query': query})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            if matches > 0:
                found_count += 1
                print(f"    ✅ FOUND: {matches} matches in {search_time:.3f}s")
                
                # Show details of first few matches
                results = result.get('results', [])
                for j, match in enumerate(results[:2]):  # Show first 2 matches
                    filename = match.get('filename', 'N/A')
                    ocd_vp = match.get('ocd_vp', 'N/A')
                    video_id = match.get('video_id', 'N/A')
                    sheet = match.get('sheet_name', 'N/A')
                    score = match.get('score', 0)
                    match_type = match.get('match_type', 'Unknown')
                    
                    print(f"       {j+1}. {filename[:50]}...")
                    print(f"          OCD/VP: {ocd_vp} | Video ID: {video_id}")
                    print(f"          Sheet: {sheet} | Score: {score} | Type: {match_type}")
                
                if matches > 2:
                    print(f"       ... and {matches - 2} more matches")
                    
            else:
                print(f"    ❌ NOT FOUND: No matches")
        else:
            print(f"    ❌ SEARCH ERROR: Status {search_response.status_code}")
        
        # Small delay to avoid overwhelming the server
        time.sleep(0.1)
    
    print("\n" + "=" * 60)
    print("📊 ENHANCED PATTERN MATCHING RESULTS")
    print("=" * 60)
    print(f"✅ Found: {found_count}/{total_tests} test cases ({found_count/total_tests*100:.1f}%)")
    print(f"❌ Not Found: {total_tests - found_count}/{total_tests} test cases")
    
    if found_count >= total_tests * 0.7:  # 70% success rate
        print("\n🎉 SUCCESS: Enhanced pattern matching is working well!")
        print("✅ Most patterns are now being found correctly!")
    else:
        print(f"\n⚠️ PARTIAL SUCCESS: {found_count/total_tests*100:.1f}% found")
        print("🔧 Some patterns may need further investigation")
    
    return found_count >= total_tests * 0.5  # 50% minimum success

def test_user_priority_list():
    """Test the user's priority video IDs"""
    
    session = requests.Session()
    session.post(f"{BASE_URL}/login", data={'password': PASSWORD})
    
    print("\n🎯 TESTING USER'S PRIORITY VIDEO IDs")
    print("=" * 60)
    
    # User's high-priority video IDs
    priority_ids = [
        "CPN-zOup_uS",
        "CO2DQGZgUQL", 
        "CO0NM2AIvRx",
        "COzgvYcgBAx",
        "Bm-QyuLHutC",
        "rbYdXbEVm6E",
        "H4qQ7MHACbw",
        "f7ZrEl04CLk",  # Known to work
        "wllIeFb2xpU",  # Known to work
        "DEB_7N2TnjS",  # Known to work
    ]
    
    found_count = 0
    
    for i, video_id in enumerate(priority_ids, 1):
        search_response = session.post(f"{BASE_URL}/search", data={'query': video_id})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            
            status = "✅" if matches > 0 else "❌"
            print(f"{i:2d}. {status} {video_id}: {matches} matches")
            
            if matches > 0:
                found_count += 1
                # Show first match
                first_match = result['results'][0]
                filename = first_match.get('filename', '')[:40]
                ocd_vp = first_match.get('ocd_vp', '')
                match_type = first_match.get('match_type', '')
                print(f"      → {filename}... | {ocd_vp} | {match_type}")
        else:
            print(f"{i:2d}. ❌ {video_id}: Search error")
    
    print(f"\n📊 Priority Results: {found_count}/{len(priority_ids)} found ({found_count/len(priority_ids)*100:.1f}%)")
    
    return found_count

if __name__ == "__main__":
    print("🚀 STARTING ENHANCED PATTERN MATCHING TESTS")
    print("Testing improved search algorithm with pattern recognition")
    
    # Test enhanced patterns
    pattern_success = test_enhanced_patterns()
    
    # Test user's priority list
    priority_found = test_user_priority_list()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS")
    print("=" * 60)
    
    if pattern_success and priority_found >= 5:
        print("🎉 EXCELLENT: Enhanced search is working much better!")
        print("✅ Pattern matching improvements are successful!")
    elif priority_found >= 3:
        print("👍 GOOD: Significant improvement in search results!")
        print("✅ Enhanced patterns are finding more matches!")
    else:
        print("🔧 NEEDS MORE WORK: Some improvements but more needed")
        print("💡 May need to investigate Google Sheets data structure")
    
    print(f"\n📈 Overall improvement: Found {priority_found}/10 priority video IDs")
    print("🚀 Enhanced Archives Stems Finder Pro is getting better!")
