#!/usr/bin/env python3
"""
Clean working Flask app - guaranteed to work
"""

from flask import Flask, request, render_template_string, session, redirect, url_for
import pandas as pd
import re
import time

app = Flask(__name__)
app.secret_key = 'clean-secret-key-123'

# Global data
data = None

def load_data():
    """Load data"""
    global data
    try:
        data = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded {len(data)} rows from cache")
    except:
        # Create test data
        data = pd.DataFrame({
            'filename': [
                'Insta-Reels_Living-Without-Regrets_04-Dec-2024_English_01Min-05Secs_Stems',
                'Insta-Reels_An-Ambiance-Of-Grace_23-Nov-2024_English_01Min-13Secs_Stems',
                'Insta-Reels_The-Worlds-Biggest-Crisis_25-Nov-2024_English_01Min_Stems',
                'Insta-Reels_Can-You-Conquer-Death_02-Nov-2024_English_53Secs_Stems',
                'Insta-Reels_Do-You-Imbibe-Or-Expend_02-Nov-2024_English_01Min_Stems',
                'Insta-Reels_How-To-Stop-Fear_09-Oct-2024_English_50Secs_Stems',
                'Insta-Reels_Overcoming-Obesity_19-Nov-2024_English_50Secs_Stems',
                'Insta-Reels_Why-Is-God-Giving-Problems_15-Apr-2025_Tamil_55Secs_Stems'
            ],
            'ocd_vp': ['OCD-001', 'OCD-002', 'OCD-003', 'OCD-004', 'OCD-005', 'OCD-006', 'OCD-007', 'OCD-008'],
            'video_id': ['VID-001', 'VID-002', 'VID-003', 'VID-004', 'VID-005', 'VID-006', 'VID-007', 'VID-008'],
            'sheet_name': ['Test Sheet'] * 8
        })
        print(f"✅ Created test data with {len(data)} rows")

def search_data(query):
    """Search function"""
    if data is None:
        return []
    
    results = []
    
    # Clean query
    clean_query = re.sub(r'[^\w\s]', '', query)
    clean_query = re.sub(r"(\w+)'s\b", r"\1s", clean_query)
    words = clean_query.split()
    
    # Create hyphenated term
    stop_words = {'of', 'to', 'and', 'a', 'an', 'the', 'in', 'on', 'at', 'for', 'by', 'with'}
    keep_words = {'is', 'are', 'you', 'your', 'or'}
    
    processed_words = []
    for word in words:
        if word.lower() in stop_words and word.lower() not in keep_words:
            processed_words.append(word.lower())
        else:
            processed_words.append(word.title())
    
    hyphenated = '-'.join(processed_words)
    
    print(f"🔍 Search: '{query}' → '{hyphenated}'")
    
    # Search for matches
    for _, row in data.iterrows():
        filename = str(row['filename'])
        parts = filename.split('_')
        if len(parts) >= 2:
            title_part = parts[1]
            if hyphenated.lower() == title_part.lower():
                results.append({
                    'filename': filename,
                    'ocd_vp': str(row['ocd_vp']),
                    'video_id': str(row['video_id']),
                    'sheet_name': str(row['sheet_name']),
                    'match_type': 'Exact Match'
                })
    
    # If no exact matches, try partial
    if not results:
        for _, row in data.iterrows():
            filename = str(row['filename'])
            if any(word.lower() in filename.lower() for word in words if len(word) > 2):
                results.append({
                    'filename': filename,
                    'ocd_vp': str(row['ocd_vp']),
                    'video_id': str(row['video_id']),
                    'sheet_name': str(row['sheet_name']),
                    'match_type': 'Partial Match'
                })
    
    print(f"🎯 Found {len(results)} matches")
    return results[:10]

# HTML template
HTML = '''
<!DOCTYPE html>
<html>
<head>
    <title>Archives Stems Finder Pro</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f2f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .header h1 { color: #007bff; margin: 0; }
        .search-form { display: flex; margin-bottom: 30px; }
        .search-input { flex: 1; padding: 15px; border: 2px solid #ddd; border-radius: 5px 0 0 5px; font-size: 16px; }
        .search-btn { padding: 15px 30px; background: #007bff; color: white; border: none; border-radius: 0 5px 5px 0; cursor: pointer; font-size: 16px; }
        .search-btn:hover { background: #0056b3; }
        .result { border: 1px solid #ddd; padding: 20px; margin: 15px 0; border-radius: 8px; background: #f8f9fa; }
        .result-title { font-weight: bold; color: #333; font-size: 16px; margin-bottom: 10px; }
        .result-details { color: #666; font-size: 14px; }
        .login-form { max-width: 400px; margin: 100px auto; padding: 40px; background: white; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .login-input { width: 100%; padding: 15px; margin: 15px 0; border: 2px solid #ddd; border-radius: 5px; box-sizing: border-box; font-size: 16px; }
        .login-btn { width: 100%; padding: 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .login-btn:hover { background: #0056b3; }
        .error { color: #dc3545; margin: 15px 0; padding: 10px; background: #f8d7da; border-radius: 5px; }
        .success { color: #155724; margin: 15px 0; padding: 10px; background: #d4edda; border-radius: 5px; }
        .stats { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center; font-weight: bold; }
        .logout { float: right; color: #007bff; text-decoration: none; }
        .logout:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        {% if 'authenticated' in session %}
            <div class="header">
                <h1>🔍 Archives Stems Finder Pro</h1>
                <p>Enhanced Title Search System</p>
                <a href="/logout" class="logout">Logout</a>
            </div>
            
            <form method="post" action="/search" class="search-form">
                <input type="text" name="query" class="search-input" placeholder="Enter search query (e.g., Living Without Regrets)..." required value="{{ query or '' }}">
                <button type="submit" class="search-btn">Search</button>
            </form>
            
            {% if results %}
                <div class="stats">
                    Found {{ results|length }} matches{% if search_time %} in {{ "%.3f"|format(search_time) }} seconds{% endif %}
                </div>
                
                {% for result in results %}
                    <div class="result">
                        <div class="result-title">{{ result.filename }}</div>
                        <div class="result-details">
                            <strong>Type:</strong> {{ result.match_type }}<br>
                            <strong>OCD/VP:</strong> {{ result.ocd_vp }}<br>
                            <strong>Video ID:</strong> {{ result.video_id }}<br>
                            <strong>Sheet:</strong> {{ result.sheet_name }}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
            
            {% if query and not results %}
                <div class="error">No matches found for "{{ query }}"</div>
            {% endif %}
            
        {% else %}
            <div class="login-form">
                <h2 style="text-align: center; color: #333; margin-bottom: 30px;">🔐 Login Required</h2>
                <form method="post" action="/login">
                    <input type="password" name="password" class="login-input" placeholder="Enter password..." required>
                    <button type="submit" class="login-btn">Login</button>
                </form>
                {% if error %}
                    <div class="error">{{ error }}</div>
                {% endif %}
                <div style="text-align: center; margin-top: 20px; color: #666; font-size: 14px;">
                    Password: Shiva@123
                </div>
            </div>
        {% endif %}
    </div>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(HTML)

@app.route('/login', methods=['POST'])
def login():
    password = request.form.get('password', '')
    if password == 'Shiva@123':
        session['authenticated'] = True
        print("✅ User logged in successfully")
        return redirect(url_for('index'))
    else:
        print("❌ Invalid login attempt")
        return render_template_string(HTML, error='Invalid password')

@app.route('/logout')
def logout():
    session.pop('authenticated', None)
    print("✅ User logged out")
    return redirect(url_for('index'))

@app.route('/search', methods=['POST'])
def search():
    if 'authenticated' not in session:
        return redirect(url_for('index'))
    
    query = request.form.get('query', '').strip()
    if not query:
        return render_template_string(HTML, error='Please enter a search query')
    
    start_time = time.time()
    
    try:
        results = search_data(query)
        search_time = time.time() - start_time
        
        print(f"🔍 Search completed: '{query}' → {len(results)} results in {search_time:.3f}s")
        
        return render_template_string(HTML, results=results, query=query, search_time=search_time)
    
    except Exception as e:
        print(f"❌ Search error: {e}")
        return render_template_string(HTML, error=f'Search failed: {str(e)}', query=query)

if __name__ == '__main__':
    print("🚀 Starting Clean Archives Stems Finder Pro")
    
    # Load data
    load_data()
    
    print("🌐 Starting Flask app on http://127.0.0.1:8082")
    print("🔑 Login password: Shiva@123")
    print("🔍 Test these queries:")
    print("   • Living Without Regrets")
    print("   • An Ambiance of Grace")
    print("   • The World's Biggest Crisis")
    print("   • Can You Conquer Death?")
    print("   • Do You Imbibe or Expend?")
    print("   • How to Stop Fear")
    print("   • Overcoming Obesity")
    print("   • Why is God giving problems?")
    print()
    print("🌐 Open your browser and go to: http://127.0.0.1:8082")
    
    app.run(host='127.0.0.1', port=8082, debug=False, threaded=True)
