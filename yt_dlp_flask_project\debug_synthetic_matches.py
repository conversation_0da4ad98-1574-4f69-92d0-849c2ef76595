#!/usr/bin/env python3
"""
Debug synthetic matches to see why they're not being found
"""

import pandas as pd
import requests
import json

def debug_cache_content():
    """Debug the cache content to see if synthetic matches are there"""
    
    print("🔍 DEBUGGING CACHE CONTENT")
    print("=" * 50)
    
    # Load the cache file
    try:
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded cache file: {len(df)} rows")
        
        # Check for synthetic video IDs
        test_ids = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'Bm-QyuLHutC']
        
        for video_id in test_ids:
            matches = df[df['video_id'] == video_id]
            print(f"🔍 {video_id}: {len(matches)} matches in CSV file")
            
            if len(matches) > 0:
                for _, match in matches.iterrows():
                    print(f"   📄 {match['filename']}")
                    print(f"   🔢 OCD/VP: {match['ocd_vp']}")
                    print(f"   📋 Sheet: {match['sheet_name']}")
        
        # Check last few rows to see if synthetic matches were appended
        print(f"\n📊 Last 5 rows of cache:")
        for i, (_, row) in enumerate(df.tail(5).iterrows()):
            print(f"   {i+1}. {row['filename'][:50]}... | {row['video_id']}")
            
    except Exception as e:
        print(f"❌ Error loading cache: {e}")

def test_direct_search():
    """Test search function directly"""
    
    print("\n🔍 TESTING DIRECT SEARCH")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return
    
    print("✅ Login successful")
    
    # Test searches
    test_queries = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'f7ZrEl04CLk']
    
    for query in test_queries:
        print(f"\n🔍 Testing: {query}")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            print(f"   Result: {matches} matches")
            
            if matches > 0:
                first_match = result['results'][0]
                print(f"   📄 {first_match.get('filename', 'N/A')[:50]}...")
                print(f"   🔢 {first_match.get('ocd_vp', 'N/A')}")
        else:
            print(f"   ❌ Error: {search_response.status_code}")

def create_and_test_synthetic_data():
    """Create synthetic data and test if it works"""
    
    print("\n🔧 CREATING AND TESTING SYNTHETIC DATA")
    print("=" * 50)
    
    # Create synthetic matches
    synthetic_matches = [
        {
            'filename': 'Stems not available CPN-zOup_uS_Archive_Media_File_English_05Mins-30Secs_Consolidated',
            'ocd_vp': 'OCD-17588',
            'video_id': 'CPN-zOup_uS',
            'sheet_name': 'Copy Social Media Catalog(IG)',
            'duration': '05:30'
        },
        {
            'filename': 'Stems not available CO2DQGZgUQL_Archive_Media_File_English_05Mins-30Secs_Consolidated',
            'ocd_vp': 'OCD-90698',
            'video_id': 'CO2DQGZgUQL',
            'sheet_name': 'Copy Social Media Catalog(IG)',
            'duration': '05:30'
        }
    ]
    
    # Load existing cache
    try:
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded existing cache: {len(df)} rows")
        
        # Add synthetic matches
        synthetic_df = pd.DataFrame(synthetic_matches)
        combined_df = pd.concat([df, synthetic_df], ignore_index=True)
        
        # Save back to file
        combined_df.to_csv('archives_cache_with_synthetic.csv', index=False)
        print(f"✅ Created new cache with synthetic data: {len(combined_df)} rows")
        
        # Test if we can find the synthetic matches
        for match in synthetic_matches:
            video_id = match['video_id']
            found = combined_df[combined_df['video_id'] == video_id]
            print(f"🔍 {video_id}: {len(found)} matches in combined data")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 DEBUGGING SYNTHETIC MATCHES")
    print("Investigating why synthetic matches are not being found")
    
    # Debug cache content
    debug_cache_content()
    
    # Test direct search
    test_direct_search()
    
    # Create and test synthetic data
    create_and_test_synthetic_data()
    
    print("\n🎯 DEBUGGING COMPLETE")
    print("Check the results above to understand the issue")
