#!/usr/bin/env python3
"""
Quick test for the specific URL
"""

import requests
import time

def quick_test():
    """Quick test of the specific URL"""
    base_url = "http://127.0.0.1:8080"
    test_url = "https://youtu.be/rbYdXbEVm6E"
    
    session = requests.Session()
    
    try:
        # Login
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login: {login_response.status_code}")
        
        # Test search
        start_time = time.time()
        search_response = session.post(f"{base_url}/search", data={'query': test_url})
        elapsed_time = time.time() - start_time
        
        print(f"Search: {search_response.status_code}")
        print(f"Time: {elapsed_time:.3f}s")
        
        if search_response.status_code == 200:
            result = search_response.json()
            print(f"Matches: {result.get('total_matches', 0)}")
            if result.get('results'):
                print(f"First result: {result['results'][0]['filename'][:50]}...")
            print("SUCCESS!")
        else:
            print(f"Error: {search_response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    quick_test()
