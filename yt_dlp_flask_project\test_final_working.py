#!/usr/bin/env python3
"""
Test the final working app
"""

import requests
import time

def test_final_working_app():
    """Test the final working app"""
    
    print("🔍 TESTING FINAL WORKING APP")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # Login
        print("📋 Step 1: Login")
        login_response = session.post("http://127.0.0.1:8080/login", 
                                    data={'password': 'Shiva@123'}, 
                                    timeout=10)
        
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test specific queries
    test_queries = [
        'Living Without Regrets',
        'An Ambiance of Grace',
        'The World\'s Biggest Crisis',
        'Can You Conquer Death?',
        'Do You Imbibe or Expend?',
        'How to Stop Fear',
        'Overcoming Obesity',
        'Why is <PERSON> giving problems?'
    ]
    
    successful_queries = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 TEST {i}: '{query}'")
        
        try:
            start_time = time.time()
            search_response = session.post("http://127.0.0.1:8080/search", 
                                         data={'query': query}, 
                                         timeout=15)
            search_time = time.time() - start_time
            
            print(f"   Response Status: {search_response.status_code}")
            print(f"   Search Time: {search_time:.3f}s")
            
            if search_response.status_code == 200:
                result = search_response.json()
                matches = result.get('total_matches', 0)
                
                print(f"   Total Matches: {matches}")
                
                if matches > 0:
                    successful_queries += 1
                    print(f"   ✅ SUCCESS!")
                    
                    # Show first match
                    first_match = result.get('results', [{}])[0]
                    filename = first_match.get('filename', 'N/A')
                    match_type = first_match.get('match_type', 'Unknown')
                    
                    print(f"   📄 First result: {filename[:60]}...")
                    print(f"   🎯 Match type: {match_type}")
                else:
                    print(f"   ❌ No matches found")
            else:
                print(f"   ❌ Search failed: {search_response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Calculate success rate
    success_rate = (successful_queries / len(test_queries)) * 100
    
    print(f"\n📊 FINAL RESULTS")
    print("=" * 60)
    print(f"✅ Successful queries: {successful_queries}/{len(test_queries)}")
    print(f"📈 Success rate: {success_rate:.1f}%")
    
    if success_rate >= 75.0:
        print("\n🎉 EXCELLENT! App is working well!")
        return True
    else:
        print("\n🔧 Needs improvement")
        return False

if __name__ == "__main__":
    success = test_final_working_app()
    
    if success:
        print("\n🎉 FINAL WORKING APP IS SUCCESSFUL!")
        print("✅ Ready for production use!")
    else:
        print("\n🔧 App needs more work")
    
    print("\n🚀 Test complete!")
