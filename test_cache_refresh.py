#!/usr/bin/env python3
"""
Test cache refresh functionality
"""

import requests
import time
import json

def test_cache_refresh():
    """Test the cache refresh functionality"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("1. Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        if login_response.status_code == 302:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return
        
        # Check cache status before refresh
        print("\n2. Checking cache status before refresh...")
        status_response = session.get(f"{base_url}/cache/status")
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"✅ Cache size: {status_data.get('cache_size', 0)}")
            print(f"✅ Last updated: {status_data.get('last_updated', 'N/A')}")
            print(f"✅ Next refresh: {status_data.get('next_refresh', 'N/A')}")
        
        # Test manual cache refresh
        print("\n3. Testing manual cache refresh...")
        start_time = time.time()
        refresh_response = session.post(f"{base_url}/refresh-cache")
        elapsed_time = time.time() - start_time
        
        if refresh_response.status_code == 200:
            refresh_data = refresh_response.json()
            print(f"✅ Cache refresh successful!")
            print(f"✅ Success: {refresh_data.get('success', False)}")
            print(f"✅ Cache size: {refresh_data.get('cache_size', 0)}")
            print(f"✅ Refresh time: {refresh_data.get('elapsed_time', 0)}s")
            print(f"✅ New timestamp: {refresh_data.get('last_updated', 'N/A')}")
            print(f"✅ Total request time: {elapsed_time:.3f}s")
        else:
            print(f"❌ Cache refresh failed: {refresh_response.status_code}")
            print(f"Response: {refresh_response.text}")
        
        # Check cache status after refresh
        print("\n4. Checking cache status after refresh...")
        status_response2 = session.get(f"{base_url}/cache/status")
        if status_response2.status_code == 200:
            status_data2 = status_response2.json()
            print(f"✅ Cache size: {status_data2.get('cache_size', 0)}")
            print(f"✅ Last updated: {status_data2.get('last_updated', 'N/A')}")
            print(f"✅ Next refresh: {status_data2.get('next_refresh', 'N/A')}")
            
            # Check if timestamp was updated
            old_time = status_data.get('last_updated', '')
            new_time = status_data2.get('last_updated', '')
            if new_time != old_time:
                print("✅ TIMESTAMP UPDATED SUCCESSFULLY!")
            else:
                print("❌ Timestamp was not updated")
        
        # Test health endpoint
        print("\n5. Testing health endpoint...")
        health_response = session.get(f"{base_url}/health")
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ Health status: {health_data.get('status', 'unknown')}")
            print(f"✅ App version: {health_data.get('version', 'unknown')}")
            print(f"✅ Uptime: {health_data.get('uptime', 0):.1f}s")
        
        print("\n🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_cache_refresh()
