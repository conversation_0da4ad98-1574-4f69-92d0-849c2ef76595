#!/usr/bin/env python3
"""
Simple test for cache refresh
"""

import requests
import time

def test_refresh():
    """Test cache refresh"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Check available routes
        print("\nTesting available endpoints...")
        
        # Test cache status
        status_response = session.get(f"{base_url}/cache/status")
        print(f"Cache status endpoint: {status_response.status_code}")
        
        # Test refresh endpoint with different methods
        print("\nTesting refresh endpoint...")
        refresh_response = session.post(f"{base_url}/cache/refresh")
        print(f"POST /cache/refresh: {refresh_response.status_code}")
        if refresh_response.status_code != 200:
            print(f"Response: {refresh_response.text[:200]}")
        
        # Try alternative endpoint
        refresh_response2 = session.post(f"{base_url}/refresh-cache")
        print(f"POST /refresh-cache: {refresh_response2.status_code}")
        if refresh_response2.status_code == 200:
            print("Found working endpoint!")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_refresh()
