#!/usr/bin/env python3
"""
Test single intelligent mapping query
"""

import requests
import time

def test_single_intelligent():
    """Test a single intelligent mapping query"""
    
    print("🧠 TESTING SINGLE INTELLIGENT MAPPING")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    try:
        login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'}, timeout=10)
        if login_response.status_code != 200:
            print("❌ Login failed")
            return False
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test one intelligent query
    query = "A tip to prevent cancer?"
    print(f"\n🔍 Testing intelligent query: '{query}'")
    
    try:
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': query}, timeout=15)
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"   ✅ Search completed: {matches} matches in {search_time:.3f}s")
            
            if matches > 0:
                for i, match in enumerate(result.get('results', []), 1):
                    filename = match.get('filename', 'N/A')
                    match_type = match.get('match_type', 'Unknown')
                    score = match.get('score', 0)
                    
                    print(f"   Match {i}:")
                    print(f"      📄 {filename}")
                    print(f"      🎯 {match_type}")
                    print(f"      📊 Score: {score}")
                    
                    if "Intelligent" in match_type:
                        print(f"      🎉 INTELLIGENT MAPPING SUCCESS!")
                        return True
            else:
                print(f"   ❌ No matches found")
        else:
            print(f"   ❌ Search failed: {search_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Search error: {e}")
    
    return False

if __name__ == "__main__":
    print("🚀 SINGLE INTELLIGENT MAPPING TEST")
    
    success = test_single_intelligent()
    
    if success:
        print("\n🎉 Intelligent mapping is working!")
    else:
        print("\n🔧 Intelligent mapping needs debugging")
    
    print("\n🚀 Test complete!")
