#!/usr/bin/env python3
"""
Debug the search function to see why it's not finding existing video IDs
"""

import pandas as pd
import requests

def test_search_function_directly():
    """Test the search function directly on known existing video IDs"""
    
    print("🔍 DEBUGGING SEARCH FUNCTION")
    print("=" * 60)
    
    # Load cache directly
    df = pd.read_csv('archives_cache.csv')
    print(f"✅ Loaded cache: {len(df):,} rows")
    
    # Test video IDs that we know exist in cache
    known_existing_ids = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'COzgvYcgBAx']
    
    for video_id in known_existing_ids:
        print(f"\n🔍 Testing: {video_id}")
        
        # Direct pandas search
        exact_matches = df[df['video_id'] == video_id]
        print(f"   Direct pandas search: {len(exact_matches)} matches")
        
        if len(exact_matches) > 0:
            match = exact_matches.iloc[0]
            print(f"   📄 Filename: {match['filename']}")
            print(f"   🔢 OCD/VP: {match['ocd_vp']}")
            print(f"   🎥 Video ID: {match['video_id']}")
            print(f"   📋 Sheet: {match['sheet_name']}")
            
            # Test Flask search
            print(f"   🌐 Testing Flask search...")
            session = requests.Session()
            session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
            
            search_response = session.post("http://127.0.0.1:8080/search", data={'query': video_id})
            
            if search_response.status_code == 200:
                result = search_response.json()
                flask_matches = result.get('total_matches', 0)
                search_time = result.get('search_time', 0)
                
                print(f"   Flask search: {flask_matches} matches in {search_time:.3f}s")
                
                if flask_matches > 0:
                    print(f"   ✅ Flask search WORKS!")
                else:
                    print(f"   ❌ Flask search FAILED - video ID exists but not found!")
            else:
                print(f"   ❌ Flask search ERROR: {search_response.status_code}")
        else:
            print(f"   ❌ Video ID not found in cache (this shouldn't happen)")

def test_cache_status_vs_file():
    """Test if Flask cache status matches file"""
    
    print(f"\n📊 TESTING CACHE STATUS VS FILE")
    print("=" * 60)
    
    # File cache
    df = pd.read_csv('archives_cache.csv')
    file_size = len(df)
    print(f"📄 Cache file size: {file_size:,} rows")
    
    # Flask cache status
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    status_response = session.get("http://127.0.0.1:8080/cache/status")
    
    if status_response.status_code == 200:
        status = status_response.json()
        flask_size = status.get('cache_size', 0)
        print(f"🌐 Flask cache size: {flask_size:,} rows")
        
        if file_size == flask_size:
            print(f"✅ Cache sizes match!")
        else:
            print(f"❌ Cache size MISMATCH!")
            print(f"   Difference: {abs(file_size - flask_size)} rows")
    else:
        print(f"❌ Flask cache status error: {status_response.status_code}")

def test_simple_search_variations():
    """Test simple search variations"""
    
    print(f"\n🔍 TESTING SIMPLE SEARCH VARIATIONS")
    print("=" * 60)
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    # Test various search terms
    test_queries = [
        "CPN-zOup_uS",      # Known to exist
        "f7ZrEl04CLk",      # Known to work
        "VP-38906",         # Known OCD/VP
        "Stems not available", # Common filename pattern
        "Social Media"      # Sheet name pattern
    ]
    
    for query in test_queries:
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': query})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"🔍 '{query}': {matches} matches in {search_time:.3f}s")
            
            if matches > 0:
                first_match = result['results'][0]
                match_type = first_match.get('match_type', 'Unknown')
                print(f"   🎯 Match Type: {match_type}")
        else:
            print(f"❌ '{query}': Search error {search_response.status_code}")

if __name__ == "__main__":
    print("🚀 DEBUGGING SEARCH FUNCTION")
    print("Finding out why search doesn't work for existing video IDs")
    
    # Test search function directly
    test_search_function_directly()
    
    # Test cache status
    test_cache_status_vs_file()
    
    # Test simple variations
    test_simple_search_variations()
    
    print("\n" + "=" * 60)
    print("🎯 SEARCH FUNCTION DEBUG RESULTS")
    print("=" * 60)
    
    print("💡 Key findings:")
    print("1. Video IDs exist in cache file")
    print("2. Flask search function not finding them")
    print("3. Need to investigate search algorithm")
    
    print("\n🚀 Search function debugging complete!")
