#!/usr/bin/env python3
"""
Minimal test app to verify basic functionality
"""

from flask import Flask, render_template_string, request, jsonify, session, redirect, url_for
import pandas as pd
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# Global cache
cached_df = None

def load_cache():
    """Load cache from file"""
    global cached_df
    try:
        cached_df = pd.read_csv('archives_cache.csv')
        logging.info(f"✅ Loaded cache: {len(cached_df)} rows")
        return True
    except Exception as e:
        logging.error(f"❌ Failed to load cache: {e}")
        return False

def simple_search(query):
    """Simple search function"""
    if cached_df is None or cached_df.empty:
        return []
    
    matches = []
    query_lower = query.lower()
    
    for _, row in cached_df.iterrows():
        filename = str(row.get('filename', ''))
        if query_lower in filename.lower():
            matches.append({
                'filename': filename,
                'ocd_vp': str(row.get('ocd_vp', '')) if pd.notna(row.get('ocd_vp')) else 'Not Available',
                'video_id': str(row.get('video_id', '')) if pd.notna(row.get('video_id')) else 'Not Available',
                'sheet_name': str(row.get('sheet_name', '')),
                'score': 100,
                'match_type': 'Simple Match',
                'matched_variation': query,
                'row_index': 0
            })
    
    return matches[:20]  # Return top 20

# HTML template
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Minimal Test App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .search-box { width: 100%; padding: 10px; margin: 10px 0; }
        .result { border: 1px solid #ddd; padding: 10px; margin: 5px 0; }
        .login-form { max-width: 300px; margin: 50px auto; }
    </style>
</head>
<body>
    <div class="container">
        {% if 'authenticated' in session %}
            <h1>🔍 Minimal Test App</h1>
            <form method="post" action="/search">
                <input type="text" name="query" class="search-box" placeholder="Enter search query..." required>
                <button type="submit">Search</button>
            </form>
            
            {% if results %}
                <h3>Results ({{ results|length }}):</h3>
                {% for result in results %}
                    <div class="result">
                        <strong>{{ result.filename }}</strong><br>
                        Type: {{ result.match_type }}<br>
                        OCD/VP: {{ result.ocd_vp }}<br>
                        Video ID: {{ result.video_id }}
                    </div>
                {% endfor %}
            {% endif %}
            
            {% if query %}
                <p>Searched for: "{{ query }}"</p>
            {% endif %}
        {% else %}
            <div class="login-form">
                <h2>Login</h2>
                <form method="post" action="/login">
                    <input type="password" name="password" placeholder="Password" required>
                    <button type="submit">Login</button>
                </form>
                {% if error %}
                    <p style="color: red;">{{ error }}</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
</body>
</html>
'''

@app.route('/')
def index():
    if 'authenticated' not in session:
        return redirect(url_for('login'))
    return render_template_string(HTML_TEMPLATE)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        password = request.form.get('password')
        if password == 'Shiva@123':
            session['authenticated'] = True
            return redirect(url_for('index'))
        else:
            return render_template_string(HTML_TEMPLATE, error='Invalid password')
    return render_template_string(HTML_TEMPLATE)

@app.route('/search', methods=['POST'])
def search():
    if 'authenticated' not in session:
        return redirect(url_for('login'))
    
    query = request.form.get('query', '').strip()
    if not query:
        return render_template_string(HTML_TEMPLATE, error='No query provided')
    
    start_time = time.time()
    
    try:
        matches = simple_search(query)
        elapsed = time.time() - start_time
        
        logging.info(f"Search for '{query}': {len(matches)} matches in {elapsed:.3f}s")
        
        # For AJAX requests
        if request.headers.get('Content-Type') == 'application/json':
            return jsonify({
                'query': query,
                'total_matches': len(matches),
                'results': matches,
                'search_time': round(elapsed, 3)
            })
        
        # For form requests
        return render_template_string(HTML_TEMPLATE, results=matches, query=query)
    
    except Exception as e:
        logging.error(f"Search error: {e}")
        return render_template_string(HTML_TEMPLATE, error='Search failed')

if __name__ == '__main__':
    print("🚀 Starting Minimal Test App")
    
    # Load cache
    if load_cache():
        print(f"✅ Cache loaded successfully")
    else:
        print("❌ Failed to load cache")
    
    print("🌐 Starting Flask app on http://127.0.0.1:8080")
    app.run(host='0.0.0.0', port=8080, debug=False)
