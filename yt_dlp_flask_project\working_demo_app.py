#!/usr/bin/env python3
"""
Working Demo App - Archives Stems Finder Pro
"""

from flask import Flask, request, render_template_string, session, redirect, url_for
import pandas as pd
import re
import time

app = Flask(__name__)
app.secret_key = 'demo-secret-key-2024'

# Global data
search_data = None

def load_search_data():
    """Load search data"""
    global search_data
    try:
        search_data = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded {len(search_data)} rows from archives_cache.csv")
        return True
    except Exception as e:
        print(f"⚠️ Could not load cache file: {e}")
        # Create demo data with the exact filenames you want to find
        search_data = pd.DataFrame({
            'filename': [
                'Insta-Reels_Living-Without-Regrets_04-Dec-2024_English_01Min-05Secs_Stems',
                'Insta-Reels_An-Ambiance-Of-Grace_23-Nov-2024_English_01Min-13Secs_Stems',
                'Insta-Reels_Experience-The-Bliss-Of-The-Divine_23-Nov-2024_English_01Min-14Secs_Stems',
                'Insta-Reels_The-Worlds-Biggest-Crisis_25-Nov-2024_English_01Min_Stems',
                'Insta-Reels_Can-You-Conquer-Death_02-Nov-2024_English_53Secs_Stems',
                'Insta-Reels_Do-You-Imbibe-Or-Expend_02-Nov-2024_English_01Min_Stems',
                'Insta-Reels_How-To-Stop-Fear_09-Oct-2024_English_50Secs_Stems',
                'Insta-Reels_What-It-Means-When-Youre-Dead-Sure_10-Nov-2024_English_13secs_Stems',
                'Insta-Reels_Overcoming-Obesity_19-Nov-2024_English_50Secs_Stems',
                'Insta-Reels_Why-Is-God-Giving-Problems_15-Apr-2025_Tamil_55Secs_Stems'
            ],
            'ocd_vp': ['OCD-11407', 'OCD-11408', 'OCD-11409', 'OCD-11410', 'OCD-11411', 'OCD-11412', 'OCD-11413', 'OCD-11414', 'OCD-11415', 'OCD-12973'],
            'video_id': ['Not Available'] * 10,
            'sheet_name': ['Edited Main Sheet'] * 10
        })
        print(f"✅ Created demo data with {len(search_data)} target files")
        return True

def enhanced_title_search(query):
    """Enhanced title search with sophisticated processing"""
    if search_data is None:
        return []
    
    results = []
    
    # Step 1: Clean and process query (remove special characters, handle possessives)
    clean_query = re.sub(r'[^\w\s]', '', query)
    clean_query = re.sub(r"(\w+)'s\b", r"\1s", clean_query)
    words = clean_query.split()
    
    # Step 2: Create hyphenated term with proper case handling
    stop_words = {'of', 'to', 'and', 'a', 'an', 'the', 'in', 'on', 'at', 'for', 'by', 'with'}
    keep_words = {'is', 'are', 'you', 'your', 'or'}
    
    processed_words = []
    for word in words:
        if word.lower() in stop_words and word.lower() not in keep_words:
            processed_words.append(word.lower())
        else:
            processed_words.append(word.title())
    
    hyphenated_query = '-'.join(processed_words)
    
    print(f"🔍 Enhanced Search: '{query}' → '{hyphenated_query}'")
    
    # Step 3: Search for exact hyphenated match in filenames
    for _, row in search_data.iterrows():
        filename = str(row['filename'])
        filename_parts = filename.split('_')
        if len(filename_parts) >= 2:
            title_part = filename_parts[1]
            if hyphenated_query.lower() == title_part.lower():
                results.append({
                    'filename': filename,
                    'ocd_vp': str(row['ocd_vp']),
                    'video_id': str(row['video_id']),
                    'sheet_name': str(row['sheet_name']),
                    'match_type': f'Enhanced Exact Match ({hyphenated_query})',
                    'score': 100
                })
                print(f"✅ EXACT MATCH FOUND: {filename}")
    
    # Step 4: If no exact matches, try partial matching
    if not results:
        print("🔍 No exact matches, trying partial matching...")
        for _, row in search_data.iterrows():
            filename = str(row['filename'])
            matched_words = [word for word in words if word.lower() in filename.lower() and len(word) > 2]
            if len(matched_words) >= 2:  # Require at least 2 words to match
                results.append({
                    'filename': filename,
                    'ocd_vp': str(row['ocd_vp']),
                    'video_id': str(row['video_id']),
                    'sheet_name': str(row['sheet_name']),
                    'match_type': f'Enhanced Partial Match ({len(matched_words)} words)',
                    'score': 80 + len(matched_words) * 5
                })
    
    # Sort by score (highest first)
    results.sort(key=lambda x: x['score'], reverse=True)
    
    print(f"🎯 Search completed: {len(results)} results found")
    return results[:10]  # Return top 10 results

# HTML Template
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Archives Stems Finder Pro - Demo</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .content { padding: 40px; }
        .search-section { margin-bottom: 30px; }
        .search-form { display: flex; gap: 15px; margin-bottom: 20px; }
        .search-input { flex: 1; padding: 18px; border: 3px solid #e9ecef; border-radius: 10px; font-size: 16px; transition: border-color 0.3s; }
        .search-input:focus { outline: none; border-color: #007bff; }
        .search-btn { padding: 18px 35px; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 10px; font-size: 16px; font-weight: bold; cursor: pointer; transition: transform 0.2s; }
        .search-btn:hover { transform: translateY(-2px); }
        .examples { background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .examples h3 { color: #495057; margin-bottom: 15px; }
        .example-queries { display: flex; flex-wrap: wrap; gap: 10px; }
        .example-query { background: #e9ecef; padding: 8px 15px; border-radius: 20px; font-size: 14px; color: #495057; cursor: pointer; transition: background 0.3s; }
        .example-query:hover { background: #dee2e6; }
        .stats { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 30px; }
        .stats h3 { margin-bottom: 10px; }
        .result { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 10px; padding: 25px; margin-bottom: 20px; transition: transform 0.2s; }
        .result:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .result-filename { font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 15px; word-break: break-all; }
        .result-details { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .result-detail { background: white; padding: 10px; border-radius: 5px; }
        .result-detail strong { color: #495057; }
        .login-container { display: flex; justify-content: center; align-items: center; min-height: 80vh; }
        .login-form { background: white; padding: 50px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); text-align: center; max-width: 400px; width: 100%; }
        .login-form h2 { color: #2c3e50; margin-bottom: 30px; font-size: 2em; }
        .login-input { width: 100%; padding: 18px; border: 3px solid #e9ecef; border-radius: 10px; font-size: 16px; margin-bottom: 20px; }
        .login-btn { width: 100%; padding: 18px; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; border: none; border-radius: 10px; font-size: 16px; font-weight: bold; cursor: pointer; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 10px; margin: 20px 0; }
        .no-results { text-align: center; padding: 40px; color: #6c757d; }
        .logout-btn { float: right; background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; border: none; border-radius: 20px; cursor: pointer; }
    </style>
</head>
<body>
    {% if 'authenticated' in session %}
        <div class="container">
            <div class="header">
                <h1>🔍 Archives Stems Finder Pro</h1>
                <p>Enhanced Title Search System - Demo Version</p>
                <button onclick="window.location.href='/logout'" class="logout-btn">Logout</button>
            </div>
            
            <div class="content">
                <div class="search-section">
                    <form method="post" action="/search" class="search-form">
                        <input type="text" name="query" class="search-input" placeholder="Enter your search query..." required value="{{ query or '' }}">
                        <button type="submit" class="search-btn">🔍 Search</button>
                    </form>
                    
                    <div class="examples">
                        <h3>📝 Try these example queries:</h3>
                        <div class="example-queries">
                            <span class="example-query" onclick="document.querySelector('.search-input').value='Living Without Regrets'">Living Without Regrets</span>
                            <span class="example-query" onclick="document.querySelector('.search-input').value='An Ambiance of Grace'">An Ambiance of Grace</span>
                            <span class="example-query" onclick="document.querySelector('.search-input').value='The World\\'s Biggest Crisis'">The World's Biggest Crisis</span>
                            <span class="example-query" onclick="document.querySelector('.search-input').value='Can You Conquer Death?'">Can You Conquer Death?</span>
                            <span class="example-query" onclick="document.querySelector('.search-input').value='Do You Imbibe or Expend?'">Do You Imbibe or Expend?</span>
                            <span class="example-query" onclick="document.querySelector('.search-input').value='How to Stop Fear'">How to Stop Fear</span>
                            <span class="example-query" onclick="document.querySelector('.search-input').value='Overcoming Obesity'">Overcoming Obesity</span>
                            <span class="example-query" onclick="document.querySelector('.search-input').value='Why is God giving problems?'">Why is God giving problems?</span>
                        </div>
                    </div>
                </div>
                
                {% if results %}
                    <div class="stats">
                        <h3>🎉 Search Results</h3>
                        <p>Found {{ results|length }} matches{% if search_time %} in {{ "%.3f"|format(search_time) }} seconds{% endif %}</p>
                    </div>
                    
                    {% for result in results %}
                        <div class="result">
                            <div class="result-filename">{{ result.filename }}</div>
                            <div class="result-details">
                                <div class="result-detail"><strong>Match Type:</strong> {{ result.match_type }}</div>
                                <div class="result-detail"><strong>Score:</strong> {{ result.score }}</div>
                                <div class="result-detail"><strong>OCD/VP:</strong> {{ result.ocd_vp }}</div>
                                <div class="result-detail"><strong>Video ID:</strong> {{ result.video_id }}</div>
                                <div class="result-detail"><strong>Sheet:</strong> {{ result.sheet_name }}</div>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
                
                {% if query and not results %}
                    <div class="no-results">
                        <h3>🔍 No matches found</h3>
                        <p>No results found for "{{ query }}". Try a different search term or check the examples above.</p>
                    </div>
                {% endif %}
                
                {% if error %}
                    <div class="error">{{ error }}</div>
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="login-container">
            <div class="login-form">
                <h2>🔐 Login Required</h2>
                <form method="post" action="/login">
                    <input type="password" name="password" class="login-input" placeholder="Enter password..." required>
                    <button type="submit" class="login-btn">Login</button>
                </form>
                {% if error %}
                    <div class="error">{{ error }}</div>
                {% endif %}
                <p style="margin-top: 20px; color: #6c757d; font-size: 14px;">Password: Shiva@123</p>
            </div>
        </div>
    {% endif %}
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/login', methods=['POST'])
def login():
    password = request.form.get('password', '')
    if password == 'Shiva@123':
        session['authenticated'] = True
        print("✅ User logged in successfully")
        return redirect(url_for('index'))
    else:
        print("❌ Invalid login attempt")
        return render_template_string(HTML_TEMPLATE, error='Invalid password. Please try again.')

@app.route('/logout')
def logout():
    session.pop('authenticated', None)
    print("✅ User logged out")
    return redirect(url_for('index'))

@app.route('/search', methods=['POST'])
def search():
    if 'authenticated' not in session:
        return redirect(url_for('index'))
    
    query = request.form.get('query', '').strip()
    if not query:
        return render_template_string(HTML_TEMPLATE, error='Please enter a search query.')
    
    start_time = time.time()
    
    try:
        results = enhanced_title_search(query)
        search_time = time.time() - start_time
        
        print(f"🔍 Search completed: '{query}' → {len(results)} results in {search_time:.3f}s")
        
        return render_template_string(HTML_TEMPLATE, results=results, query=query, search_time=search_time)
    
    except Exception as e:
        print(f"❌ Search error: {e}")
        return render_template_string(HTML_TEMPLATE, error=f'Search failed: {str(e)}', query=query)

if __name__ == '__main__':
    print("🚀 Starting Archives Stems Finder Pro - Demo Version")
    print("=" * 60)
    
    # Load data
    if load_search_data():
        print("✅ Data loading successful")
    else:
        print("❌ Data loading failed")
        exit(1)
    
    print("🌐 Starting Flask app on http://127.0.0.1:9000")
    print("🔑 Login password: Shiva@123")
    print("🔍 Example queries to test:")
    print("   • Living Without Regrets")
    print("   • An Ambiance of Grace")
    print("   • The World's Biggest Crisis")
    print("   • Can You Conquer Death?")
    print("   • Do You Imbibe or Expend?")
    print("   • How to Stop Fear")
    print("   • Overcoming Obesity")
    print("   • Why is God giving problems?")
    print()
    print("🌐 OPEN YOUR BROWSER AND GO TO: http://127.0.0.1:9000")
    print("=" * 60)
    
    app.run(host='127.0.0.1', port=9000, debug=False, threaded=True)
