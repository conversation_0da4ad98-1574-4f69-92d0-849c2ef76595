#!/usr/bin/env python3
"""
Test debug route
"""

import requests

def test_debug():
    """Test debug route"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Test debug route for OCD-16673
        print("\nTesting debug route for OCD-16673...")
        debug_response = session.get(f"{base_url}/debug-search/OCD-16673")
        if debug_response.status_code == 200:
            data = debug_response.json()
            print(f"Query: {data.get('query', 'N/A')}")
            print(f"Total rows in cache: {data.get('total_rows_in_cache', 0):,}")
            print(f"OCD matches found: {data.get('ocd_matches_found', 0)}")
            print(f"Sample OCD values: {data.get('sample_ocd_values', [])[:5]}")
            
            matching_rows = data.get('matching_rows', [])
            if matching_rows:
                print(f"Matching rows:")
                for row in matching_rows:
                    print(f"  - {row.get('filename', 'N/A')[:50]}... | {row.get('ocd_vp', 'N/A')} | {row.get('sheet_name', 'N/A')}")
            else:
                print("No matching rows found")
        else:
            print(f"Debug route failed: {debug_response.status_code}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_debug()
