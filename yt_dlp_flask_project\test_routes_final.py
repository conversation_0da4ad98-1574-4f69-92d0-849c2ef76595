#!/usr/bin/env python3
"""
Test routes in final app
"""

import requests

def test_routes():
    """Test routes"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("1. Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"   Login status: {login_response.status_code}")
        print(f"   Login response: {login_response.text}")
        
        # Test index
        print("\n2. Testing index...")
        index_response = session.get(f"{base_url}/")
        print(f"   Index status: {index_response.status_code}")
        if index_response.status_code == 200:
            try:
                data = index_response.json()
                print(f"   App: {data.get('app_name', 'N/A')}")
                print(f"   Version: {data.get('app_version', 'N/A')}")
                print(f"   Cache size: {data.get('cache_size', 0)}")
            except:
                print(f"   Response: {index_response.text[:200]}")
        
        # Test cache status
        print("\n3. Testing cache status...")
        cache_response = session.get(f"{base_url}/cache/status")
        print(f"   Cache status: {cache_response.status_code}")
        if cache_response.status_code == 200:
            try:
                data = cache_response.json()
                print(f"   Cache loaded: {data.get('cache_loaded', False)}")
                print(f"   Cache size: {data.get('cache_size', 0)}")
                print(f"   App version: {data.get('app_version', 'N/A')}")
            except:
                print(f"   Response: {cache_response.text[:200]}")
        
        # Test search
        print("\n4. Testing search...")
        search_response = session.post(f"{base_url}/search", data={'query': 'VP-16338'})
        print(f"   Search status: {search_response.status_code}")
        if search_response.status_code == 200:
            try:
                data = search_response.json()
                print(f"   Query: {data.get('query', 'N/A')}")
                print(f"   Matches: {data.get('total_matches', 0)}")
                print(f"   App version: {data.get('app_version', 'N/A')}")
            except:
                print(f"   Response: {search_response.text[:200]}")
        else:
            print(f"   Search response: {search_response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_routes()
