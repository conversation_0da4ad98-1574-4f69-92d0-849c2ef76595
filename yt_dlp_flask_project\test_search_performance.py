#!/usr/bin/env python3
"""
Test script to check search performance
"""

import requests
import time
import json

# Test queries from user
test_queries = [
    "https://youtu.be/Nr_ldMy87lI",
    "https://youtu.be/SMQ-rJlOpSU", 
    "https://youtu.be/-W52myd9nHE",
    "https://youtu.be/SZLK7WYQE8E",
    "dZj8o9hMUX8",
    "Kgowgm1KeZ4",
    "3amX-jVo4-U",
    "OCD-9585",
    "https://youtu.be/AHS1c_vqjxI",
    "https://youtu.be/BtPrmLLHtKY",
    "https://youtu.be/TMHhylNs-3Q",
    "https://youtu.be/rbYdXbEVm6E",
    "QEkHcPt-Vpw",
    "2UHwPxgmfg8",
    "e2UN3d60IQM",
    "Nkh5y4R_RD0",
    "Z3633_Talks-Uploaded-In-Sadhguru-YouTube-Channel-On-Sep-2016-Eng-4Mins-1Mov-HD-With-Logo",
    "Q-And-A_What-Are-The-Limits-Of-My-Mind_07-Sep-2016_English_09Mins-54Secs_MOV",
    "Promo_What-Is-Inner-Engineering_02-Sep-2016_English_07Mins-35Secs_MOV",
    "BmDc0eglgIN",
    "BlcwSDZnvRC",
    "BlaKLssDhRm",
    "BkxrLGZHN1r",
    "Bi9PLrYH3Ef",
    "https://www.instagram.com/p/BtAa0Mihu85/",
    "https://www.instagram.com/reel/BtAa0Mihu85",
    "https://www.instagram.com/p/Bs5j5Owh_aM/",
    "https://www.instagram.com/p/BsvPnhAhB37/",
    "https://www.instagram.com/reel/BsvPnhAhB37",
    "TMHhylNs-3Q",
    "rbYdXbEVm6E",
    "https://youtu.be/5WGshwt_RKw",
    "https://youtu.be/grTMDBwVOPo",
    "https://youtu.be/Vu45G9lMQa0",
    "https://www.instagram.com/reel/DAtKuIAvcs1",
    "https://www.instagram.com/reel/DAsxYHXMGCx",
    "OCD-7152",
    "OCD-8906",
    "OCD-8532",
    "https://youtu.be/4LTe2vsmD_8",
    "https://youtu.be/apK41d5HWfc"
]

def test_search_performance():
    """Test search performance"""
    base_url = "http://127.0.0.1:8080"

    # Create session
    session = requests.Session()

    # Check if server is running first
    try:
        health_check = session.get(f"{base_url}/", timeout=5)
        print(f"🌐 Server status: {health_check.status_code}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        print("🔧 Please start the Flask application first")
        return

    # Login first
    try:
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        if login_response.status_code != 302:  # Should redirect after successful login
            print(f"❌ Login failed: {login_response.status_code}")
            return
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return
    
    # Test search performance
    print(f"\n🚀 Testing {len(test_queries)} search queries...")
    print("=" * 80)
    
    total_time = 0
    successful_searches = 0
    
    for i, query in enumerate(test_queries, 1):
        try:
            start_time = time.time()
            
            # Perform search
            search_response = session.post(f"{base_url}/search", data={'query': query})
            
            elapsed_time = time.time() - start_time
            total_time += elapsed_time
            
            if search_response.status_code == 200:
                successful_searches += 1
                result_data = search_response.json()
                matches = result_data.get('total_matches', 0)
                
                # Performance indicator
                if elapsed_time <= 2.0:
                    perf_icon = "🚀"  # Fast
                elif elapsed_time <= 5.0:
                    perf_icon = "⚡"  # Good
                else:
                    perf_icon = "🐌"  # Slow
                
                print(f"{perf_icon} Query {i:2d}: {elapsed_time:.3f}s | {matches:2d} matches | {query[:50]}")
            else:
                print(f"❌ Query {i:2d}: HTTP {search_response.status_code} | {query[:50]}")
                
        except Exception as e:
            print(f"💥 Query {i:2d}: Error - {e} | {query[:50]}")
    
    # Summary
    print("=" * 80)
    avg_time = total_time / len(test_queries) if test_queries else 0
    print(f"📊 PERFORMANCE SUMMARY:")
    print(f"   Total queries: {len(test_queries)}")
    print(f"   Successful: {successful_searches}")
    print(f"   Average time: {avg_time:.3f}s")
    print(f"   Total time: {total_time:.3f}s")
    
    if avg_time <= 2.0:
        print(f"🎉 EXCELLENT! Average search time is under 2 seconds!")
    elif avg_time <= 5.0:
        print(f"✅ GOOD! Average search time is under 5 seconds")
    else:
        print(f"⚠️  SLOW! Average search time is over 5 seconds - needs optimization")

if __name__ == "__main__":
    test_search_performance()
