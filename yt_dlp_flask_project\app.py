from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
import yt_dlp
import pandas as pd
from fuzzywuzzy import fuzz
import re
import time
import logging
import os
from datetime import datetime, timedelta
from urllib.parse import urlparse
import gspread
from google.oauth2.service_account import Credentials
import requests
from io import StringIO
import threading
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configure logging (Windows-safe, no emojis)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Configuration
class Config:
    # Google Sheets Configuration
    GOOGLE_SHEET_ID = '1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE'
    SHEET_NAMES = [
        'Edited Main Sheet',
        'Social Media Catalog(SG)',
        'Social Media Catalog(IF)',
        'Social Media Catalog(IG)',
        'Social Media Catalog(CP)',
        'Copy Social Media Catalog(SG)',
        'Copy Social Media Catalog(IF)',
        'Copy Social Media Catalog(IG)'
    ]

    # Google Drive Configuration
    GOOGLE_DRIVE_FOLDER = 'https://drive.google.com/drive/folders/1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG'
    GOOGLE_DRIVE_FOLDER_ID = '1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG'

    # App Configuration
    MAX_RESULTS = 10
    DURATION_TOLERANCE = 20  # seconds
    SUPPORTED_DOMAINS = ['youtube.com', 'youtu.be', 'm.youtube.com']

    # Caching Configuration
    CACHE_DURATION_HOURS = 24  # Refresh cache once daily
    CACHE_FILE = 'combined_sheets_cache.json'
    CACHE_METADATA_FILE = 'cache_metadata.json'

    # Google API Configuration
    SCOPES = [
        'https://www.googleapis.com/auth/spreadsheets.readonly',
        'https://www.googleapis.com/auth/drive.readonly'
    ]
    CREDENTIALS_FILE = r'D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\credentials.json'  # Service account credentials

    # Google Drive Configuration
    GOOGLE_DRIVE_FOLDER = "https://drive.google.com/drive/folders/1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG"

config = Config()

# Global cache variables
cached_data = None
cache_last_updated = None
cache_lock = threading.Lock()

# Function to clean and standardize text for comparison
def smart_clean(text):
    """Clean and standardize text for better matching"""
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = re.sub(r'[_\-]', ' ', text)
    text = re.sub(r'\d{1,2}[a-z]{0,2}[- ]?(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*[- ]?\d{2,4}', '', text)
    text = re.sub(r'(sharings|stems|english|hindi|tamil|mins|secs|\d{2,4})', '', text)
    text = re.sub(r'[^a-z\s]', '', text)
    return text.strip()

# Extract keywords from the cleaned text
def extract_keywords(text):
    """Extract keywords from cleaned text"""
    text = smart_clean(text)
    return set(text.split())

# Convert time format (HH:MM:SS) to total seconds and human-readable format
def format_seconds(hms_string):
    """Convert time format to seconds and human-readable format"""
    try:
        if not hms_string or hms_string.strip() == "":
            return "0m 0s", 0

        # Handle decimal format (like 0.041666667)
        if '.' in str(hms_string) and ':' not in str(hms_string):
            try:
                decimal_hours = float(hms_string)
                total_seconds = int(decimal_hours * 3600)
                return f"{total_seconds // 60}m {total_seconds % 60}s", total_seconds
            except ValueError:
                pass

        # Handle HH:MM:SS format
        time_parts = str(hms_string).strip().split(':')
        if len(time_parts) == 3:
            h, m, s = map(int, time_parts)
            total_seconds = h * 3600 + m * 60 + s
        elif len(time_parts) == 2:
            m, s = map(int, time_parts)
            total_seconds = m * 60 + s
        else:
            return "??m ??s", 0

        return f"{total_seconds // 60}m {total_seconds % 60}s", total_seconds
    except (ValueError, TypeError) as e:
        logging.warning(f"Invalid duration format: {hms_string} - {str(e)}")
        return "??m ??s", 0

# Google Sheets Integration
def get_google_sheets_client():
    """Initialize and return Google Sheets client"""
    try:
        if not os.path.exists(config.CREDENTIALS_FILE):
            logging.error(f"Credentials file not found: {config.CREDENTIALS_FILE}")
            return None

        credentials = Credentials.from_service_account_file(
            config.CREDENTIALS_FILE,
            scopes=config.SCOPES
        )
        client = gspread.authorize(credentials)
        return client
    except Exception as e:
        logging.error(f"Error initializing Google Sheets client: {str(e)}")
        return None

def get_sheet_data_as_csv_url(sheet_id, sheet_name):
    """Get Google Sheet data as CSV URL"""
    try:
        # Encode sheet name for URL
        import urllib.parse
        encoded_sheet_name = urllib.parse.quote(sheet_name)
        csv_url = f"https://docs.google.com/spreadsheets/d/{sheet_id}/gviz/tq?tqx=out:csv&sheet={encoded_sheet_name}"
        return csv_url
    except Exception as e:
        logging.error(f"Error creating CSV URL for sheet {sheet_name}: {str(e)}")
        return None

def fetch_sheet_data(sheet_id, sheet_name):
    """Fetch data from a specific Google Sheet"""
    try:
        csv_url = get_sheet_data_as_csv_url(sheet_id, sheet_name)
        if not csv_url:
            return None

        response = requests.get(csv_url, timeout=30)
        response.raise_for_status()

        # Parse CSV data
        csv_data = StringIO(response.text)
        df = pd.read_csv(csv_data)

        logging.info(f"Successfully fetched {len(df)} rows from sheet: {sheet_name}")
        return df

    except Exception as e:
        logging.error(f"Error fetching data from sheet {sheet_name}: {str(e)}")
        return None

def search_in_google_drive(search_term, search_type='filename'):
    """Search for file in Google Drive and return download link"""
    try:
        # Construct search URL based on search type
        if search_type in ['ocd_number', 'vp_number']:
            # For OCD/VP numbers, search directly in the drive folder
            search_query = search_term.replace(' ', '+').replace('-', '+')
            drive_search_url = f"{config.GOOGLE_DRIVE_FOLDER}?q={search_query}"
        else:
            # For filenames, use the original approach
            search_query = search_term.replace(' ', '+')
            drive_search_url = f"{config.GOOGLE_DRIVE_FOLDER}?q={search_query}"

        return drive_search_url

    except Exception as e:
        logging.error(f"Error searching in Google Drive for {search_term}: {str(e)}")
        return None

def search_by_ocd_vp_number(search_term):
    """Search specifically for OCD or VP numbers in cached data and Google Drive"""
    global cached_data

    if not cached_data:
        return []

    search_term_clean = search_term.upper().replace('-', '').replace(' ', '')
    matches = []

    for item in cached_data:
        filename = item['filename'].upper()

        # Search in filename for OCD/VP numbers
        if search_term in filename or search_term_clean in filename.replace('-', '').replace(' ', ''):
            drive_link = search_in_google_drive(search_term, 'ocd_number' if 'OCD' in search_term else 'vp_number')

            matches.append({
                'filename': item['filename'],
                'score': 100,  # High score for exact matches
                'duration': item['duration'],
                'sheet_name': item['sheet_name'],
                'drive_link': drive_link,
                'search_type': 'OCD/VP Number Match'
            })

    return matches

# Cache Management Functions
def save_cache_metadata(total_rows, sheets_processed, last_updated, sheet_row_counts=None):
    """Save cache metadata with sheet-specific row counts for incremental updates"""
    try:
        metadata = {
            'last_updated': last_updated.isoformat(),
            'total_rows': total_rows,
            'sheets_processed': sheets_processed,
            'cache_duration_hours': config.CACHE_DURATION_HOURS,
            'cache_version': '2.0',
            'sheet_row_counts': sheet_row_counts or {},
            'last_incremental_update': datetime.now().isoformat()
        }
        with open(config.CACHE_METADATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2)
        logging.info(f"Cache metadata saved: {total_rows} rows from {sheets_processed} sheets")
    except Exception as e:
        logging.error(f"Error saving cache metadata: {str(e)}")

def load_cache_metadata():
    """Load cache metadata from file"""
    try:
        if os.path.exists(config.CACHE_METADATA_FILE):
            with open(config.CACHE_METADATA_FILE, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
                last_updated = datetime.fromisoformat(metadata['last_updated'])
                sheet_row_counts = metadata.get('sheet_row_counts', {})
                return last_updated, metadata.get('total_rows', 0), metadata.get('sheets_processed', 0), sheet_row_counts
    except Exception as e:
        logging.error(f"Error loading cache metadata: {str(e)}")
    return None, 0, 0, {}

def get_sheet_current_row_count(sheet_name):
    """Get current row count of a specific sheet"""
    try:
        client = get_google_sheets_client()
        if not client:
            return 0

        sheet = client.open_by_key(config.GOOGLE_SHEET_ID).worksheet(sheet_name)
        # Get all values to count rows
        all_values = sheet.get_all_values()
        return len(all_values)
    except Exception as e:
        logging.error(f"Error getting row count for sheet {sheet_name}: {str(e)}")
        return 0

def fetch_incremental_sheet_data(sheet_name, last_known_row_count):
    """Fetch only new rows from a sheet since last update"""
    try:
        client = get_google_sheets_client()
        if not client:
            return sheet_name, [], 0

        sheet = client.open_by_key(config.GOOGLE_SHEET_ID).worksheet(sheet_name)

        # Get current row count
        current_row_count = get_sheet_current_row_count(sheet_name)

        if current_row_count <= last_known_row_count:
            logging.info(f"No new rows in sheet: {sheet_name}")
            return sheet_name, [], current_row_count

        # Calculate range for new rows
        start_row = max(last_known_row_count + 1, 2)  # Skip header
        new_rows_count = current_row_count - last_known_row_count

        logging.info(f"Fetching {new_rows_count} new rows from sheet: {sheet_name} (rows {start_row}-{current_row_count})")

        # Fetch only new rows
        range_name = f"A{start_row}:Z{current_row_count}"
        new_values = sheet.get(range_name).get('values', [])

        # Process new rows
        processed_data = []
        for row in new_values:
            if len(row) >= 2:  # Ensure we have at least filename and duration
                filename = row[0].strip() if row[0] else ""
                duration_str = row[1].strip() if len(row) > 1 and row[1] else "0"

                if filename:  # Only process rows with filenames
                    try:
                        duration_seconds = parse_duration_to_seconds(duration_str)
                        processed_data.append({
                            'filename': filename,
                            'duration': duration_seconds,
                            'sheet_name': sheet_name
                        })
                    except Exception as e:
                        logging.warning(f"Error processing row in {sheet_name}: {str(e)}")
                        continue

        logging.info(f"Successfully processed {len(processed_data)} new rows from sheet: {sheet_name}")
        return sheet_name, processed_data, current_row_count

    except Exception as e:
        logging.error(f"Error fetching incremental data from sheet {sheet_name}: {str(e)}")
        return sheet_name, [], last_known_row_count

def is_cache_valid():
    """Check if cache is still valid (within 2 hours)"""
    global cache_last_updated
    if cache_last_updated is None:
        cache_last_updated, _, _, _ = load_cache_metadata()

    if cache_last_updated is None:
        return False

    time_diff = datetime.now() - cache_last_updated
    return time_diff < timedelta(hours=config.CACHE_DURATION_HOURS)

def fetch_single_sheet_data(sheet_name):
    """Fetch data from a single sheet (for parallel processing)"""
    try:
        logging.info(f"Fetching data from sheet: {sheet_name}")
        df = fetch_sheet_data(config.GOOGLE_SHEET_ID, sheet_name)

        if df is None or df.empty:
            logging.warning(f"No data found in sheet: {sheet_name}")
            return sheet_name, []

        # Find filename and duration columns
        filename_col = None
        duration_col = None

        for col in df.columns:
            col_lower = col.lower()
            if 'filename' in col_lower or 'name' in col_lower or 'title' in col_lower:
                filename_col = col
            if 'duration' in col_lower or 'time' in col_lower:
                duration_col = col

        if not filename_col:
            logging.warning(f"No filename column found in sheet: {sheet_name}")
            return sheet_name, []

        # Convert to list of dictionaries
        sheet_data = []
        filenames = df[filename_col].fillna('').tolist()
        durations = df[duration_col].fillna("00:00:00").tolist() if duration_col else ["00:00:00"] * len(filenames)

        for filename, duration in zip(filenames, durations):
            if filename and filename.strip():
                sheet_data.append({
                    'filename': filename.strip(),
                    'duration': duration,
                    'sheet_name': sheet_name
                })

        logging.info(f"Successfully processed {len(sheet_data)} rows from sheet: {sheet_name}")
        return sheet_name, sheet_data

    except Exception as e:
        logging.error(f"Error fetching data from sheet {sheet_name}: {str(e)}")
        return sheet_name, []

def refresh_cache_full():
    """Full cache refresh - rebuild entire cache from scratch"""
    global cached_data, cache_last_updated

    try:
        logging.info("Starting FULL cache refresh process...")
        start_time = time.time()

        # Use ThreadPoolExecutor for parallel sheet fetching
        combined_data = []
        sheets_processed = 0
        sheet_row_counts = {}

        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit all sheet fetch tasks
            future_to_sheet = {
                executor.submit(fetch_single_sheet_data, sheet_name): sheet_name
                for sheet_name in config.SHEET_NAMES
            }

            # Collect results as they complete
            for future in as_completed(future_to_sheet):
                sheet_name = future_to_sheet[future]
                try:
                    sheet_name_result, sheet_data = future.result()
                    if sheet_data:
                        combined_data.extend(sheet_data)
                        sheets_processed += 1
                        # Store row count for this sheet
                        sheet_row_counts[sheet_name] = get_sheet_current_row_count(sheet_name)
                        logging.info(f"Processed sheet: {sheet_name} ({len(sheet_data)} rows)")
                    else:
                        logging.warning(f"No data from sheet: {sheet_name}")
                        sheet_row_counts[sheet_name] = 0
                except Exception as e:
                    logging.error(f"Error processing sheet {sheet_name}: {str(e)}")
                    sheet_row_counts[sheet_name] = 0

        # Update global cache
        with cache_lock:
            cached_data = combined_data
            cache_last_updated = datetime.now()

        # Save cache to file
        try:
            with open(config.CACHE_FILE, 'w', encoding='utf-8') as f:
                json.dump(combined_data, f, indent=2, ensure_ascii=False)
            logging.info(f"Cache saved to file: {len(combined_data)} total rows")
        except Exception as e:
            logging.error(f"Error saving cache to file: {str(e)}")

        # Save metadata with sheet row counts
        save_cache_metadata(len(combined_data), sheets_processed, cache_last_updated, sheet_row_counts)

        elapsed_time = time.time() - start_time
        logging.info(f"FULL cache refresh completed! {len(combined_data)} rows from {sheets_processed} sheets in {elapsed_time:.2f}s")

        return True

    except Exception as e:
        logging.error(f"Error refreshing cache: {str(e)}")
        return False

def refresh_cache_incremental():
    """Incremental cache refresh - only fetch new rows"""
    global cached_data, cache_last_updated

    try:
        logging.info("Starting INCREMENTAL cache refresh process...")
        start_time = time.time()

        # Load existing cache and metadata
        if not load_cache_from_file():
            logging.info("No existing cache found, performing full refresh")
            return refresh_cache_full()

        # Load metadata to get last known row counts
        _, _, _, sheet_row_counts = load_cache_metadata()

        if not sheet_row_counts:
            logging.info("No sheet row counts found, performing full refresh")
            return refresh_cache_full()

        # Fetch only new rows from each sheet
        new_data = []
        updated_sheet_counts = {}
        total_new_rows = 0

        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit incremental fetch tasks
            future_to_sheet = {
                executor.submit(fetch_incremental_sheet_data, sheet_name, sheet_row_counts.get(sheet_name, 0)): sheet_name
                for sheet_name in config.SHEET_NAMES
            }

            # Collect results as they complete
            for future in as_completed(future_to_sheet):
                sheet_name = future_to_sheet[future]
                try:
                    sheet_name_result, sheet_new_data, current_row_count = future.result()
                    if sheet_new_data:
                        new_data.extend(sheet_new_data)
                        total_new_rows += len(sheet_new_data)
                        logging.info(f"Added {len(sheet_new_data)} new rows from sheet: {sheet_name}")

                    updated_sheet_counts[sheet_name] = current_row_count

                except Exception as e:
                    logging.error(f"Error processing incremental update for sheet {sheet_name}: {str(e)}")
                    # Keep the old count if there's an error
                    updated_sheet_counts[sheet_name] = sheet_row_counts.get(sheet_name, 0)

        if total_new_rows > 0:
            # Append new data to existing cache
            with cache_lock:
                if cached_data is None:
                    cached_data = []
                cached_data.extend(new_data)
                cache_last_updated = datetime.now()

            # Save updated cache to file
            try:
                with open(config.CACHE_FILE, 'w', encoding='utf-8') as f:
                    json.dump(cached_data, f, indent=2, ensure_ascii=False)
                logging.info(f"Updated cache saved: {len(cached_data)} total rows ({total_new_rows} new)")
            except Exception as e:
                logging.error(f"Error saving updated cache to file: {str(e)}")

            # Save updated metadata
            save_cache_metadata(len(cached_data), len(config.SHEET_NAMES), cache_last_updated, updated_sheet_counts)

            elapsed_time = time.time() - start_time
            logging.info(f"INCREMENTAL cache refresh completed! Added {total_new_rows} new rows in {elapsed_time:.2f}s")
        else:
            logging.info("No new rows found in any sheet")
            # Update metadata with current row counts anyway
            save_cache_metadata(len(cached_data) if cached_data else 0, len(config.SHEET_NAMES), cache_last_updated, updated_sheet_counts)

        return True

    except Exception as e:
        logging.error(f"Error in incremental cache refresh: {str(e)}")
        return False

def refresh_cache():
    """Smart cache refresh - incremental by default, full if needed"""
    global cached_data

    # If no cache exists, do full refresh
    if cached_data is None or not os.path.exists(config.CACHE_FILE):
        return refresh_cache_full()

    # Otherwise, do incremental refresh
    return refresh_cache_incremental()

def load_cache_from_file():
    """Load cache from file if it exists and is valid"""
    global cached_data, cache_last_updated

    try:
        if not os.path.exists(config.CACHE_FILE):
            logging.info("No cache file found, will create new cache")
            return False

        # Check if cache is still valid
        if not is_cache_valid():
            logging.info("Cache expired, will refresh")
            return False

        # Load cache data
        with open(config.CACHE_FILE, 'r', encoding='utf-8') as f:
            cached_data = json.load(f)

        cache_last_updated, total_rows, sheets_processed, _ = load_cache_metadata()

        logging.info(f"Loaded cache from file: {len(cached_data)} rows (last updated: {cache_last_updated})")
        return True

    except Exception as e:
        logging.error(f"Error loading cache from file: {str(e)}")
        return False

def ensure_cache_ready():
    """Ensure cache is ready, refresh if needed"""
    global cached_data

    with cache_lock:
        if cached_data is None:
            # Try to load from file first
            if not load_cache_from_file():
                # If file load fails or cache is expired, refresh
                logging.info("Initializing cache for the first time...")
                refresh_cache()
        elif not is_cache_valid():
            # Cache is expired, refresh in background
            logging.info("Cache expired, refreshing...")
            refresh_cache()

def start_cache_refresh_scheduler():
    """Start background thread to refresh cache every 2 hours"""
    def cache_scheduler():
        while True:
            try:
                time.sleep(config.CACHE_DURATION_HOURS * 3600)  # Sleep for 2 hours
                logging.info("Scheduled cache refresh starting...")
                refresh_cache()
            except Exception as e:
                logging.error(f"Error in cache scheduler: {str(e)}")
                time.sleep(300)  # Wait 5 minutes before retrying

    scheduler_thread = threading.Thread(target=cache_scheduler, daemon=True)
    scheduler_thread.start()
    logging.info(f"Cache scheduler started (refresh every {config.CACHE_DURATION_HOURS} hours)")

# Extract YouTube Video ID from URL or direct ID
def extract_video_id(input_text):
    """Extract YouTube video ID from URL or return if it's already an ID"""
    try:
        input_text = input_text.strip()

        # If it's already a video ID (11 characters, alphanumeric + underscore/dash)
        if len(input_text) == 11 and input_text.replace('_', '').replace('-', '').isalnum():
            return input_text

        # Extract from various YouTube URL formats
        if 'youtu.be/' in input_text:
            return input_text.split('youtu.be/')[-1].split('?')[0].split('&')[0]
        elif 'youtube.com/watch' in input_text:
            from urllib.parse import parse_qs, urlparse
            parsed = urlparse(input_text)
            return parse_qs(parsed.query).get('v', [None])[0]
        elif 'youtube.com/embed/' in input_text:
            return input_text.split('youtube.com/embed/')[-1].split('?')[0]

        return None
    except Exception:
        return None

# Detect search type
def detect_search_type(query):
    """Detect what type of search this is"""
    query = query.strip().upper()

    # Check for OCD number
    if query.startswith('OCD-') or (query.startswith('OCD') and any(c.isdigit() for c in query)):
        return 'ocd_number'

    # Check for VP number
    if query.startswith('VP-') or (query.startswith('VP') and any(c.isdigit() for c in query)):
        return 'vp_number'

    # Check for Video ID (11 characters)
    if len(query) == 11 and query.replace('_', '').replace('-', '').isalnum():
        return 'video_id'

    # Check for YouTube URL
    if any(domain in query.lower() for domain in ['youtube.com', 'youtu.be']):
        return 'youtube_url'

    # Default to title search
    return 'title_search'

# Validate YouTube URL
def is_valid_youtube_url(url):
    """Validate if the URL is a valid YouTube URL"""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        return any(supported_domain in domain for supported_domain in config.SUPPORTED_DOMAINS)
    except Exception:
        return False

# Get video information from YouTube using yt-dlp
def get_video_info(url):
    """Extract video information from YouTube URL"""
    try:
        if not is_valid_youtube_url(url):
            logging.error(f"Invalid YouTube URL: {url}")
            return None, None, 0

        ydl_opts = {
            'quiet': True,
            'forcejson': True,
            'noplaylist': True,
            'nocheckcertificate': True,
            'extract_flat': False
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info_dict = ydl.extract_info(url, download=False)
            title = info_dict.get('title', None)
            duration = info_dict.get('duration', 0)

            if duration:
                duration_str = f"{duration // 60}m {duration % 60}s"
            else:
                duration_str = "Unknown"

            logging.info(f"Successfully extracted video info: {title} ({duration_str})")
            return title, duration_str, duration

    except Exception as e:
        logging.error(f"Error fetching video info for {url}: {str(e)}")
        return None, None, 0

# Match the video title with filenames in the CSV
def match_filename(video_title, csv_file, top_n=5):
    """Match video title with filenames in CSV and return top matches"""
    try:
        if not os.path.exists(csv_file):
            logging.error(f"CSV file not found: {csv_file}")
            return []

        df = pd.read_csv(csv_file)

        if 'filename' not in df.columns or 'duration' not in df.columns:
            logging.error("CSV file missing required columns: 'filename' and 'duration'")
            return []

        filenames = df['filename'].fillna('').tolist()
        durations = df['duration'].fillna("00:00:00").tolist()

        video_clean = smart_clean(video_title)
        video_keywords = extract_keywords(video_title)

        match_results = []

        for filename, file_duration in zip(filenames, durations):
            if not filename:  # Skip empty filenames
                continue

            file_clean = smart_clean(filename)
            file_keywords = extract_keywords(filename)
            keyword_overlap = len(video_keywords.intersection(file_keywords))
            token_score = fuzz.token_set_ratio(video_clean, file_clean)
            combined_score = token_score + (keyword_overlap * 5)

            match_results.append((filename, combined_score, file_duration))

        match_results.sort(key=lambda x: x[1], reverse=True)
        return match_results[:top_n]

    except Exception as e:
        logging.error(f"Error in match_filename: {str(e)}")
        return []

# Universal search function
def universal_search(query, top_n=5):
    """Universal search function that handles all search types"""
    global cached_data

    # Ensure cache is ready
    ensure_cache_ready()

    if not cached_data:
        logging.warning("No cached data available")
        return []

    start_time = time.time()
    search_type = detect_search_type(query)

    logging.info(f"Performing {search_type} search for: {query}")

    # Handle different search types
    if search_type in ['ocd_number', 'vp_number']:
        matches = search_by_ocd_vp_number(query)
    elif search_type == 'video_id':
        # For video ID, try to get video info first, then search by title
        video_url = f"https://www.youtube.com/watch?v={query}"
        video_title, yt_duration, yt_duration_seconds = get_video_info(video_url)
        if video_title:
            matches = search_by_title(video_title, top_n)
            # Add video info to matches
            for match in matches:
                match['youtube_title'] = video_title
                match['youtube_duration'] = yt_duration
                match['search_type'] = 'Video ID Search'
        else:
            matches = []
    elif search_type == 'youtube_url':
        # Extract video ID and get video info
        video_id = extract_video_id(query)
        if video_id:
            video_title, yt_duration, yt_duration_seconds = get_video_info(query)
            if video_title:
                matches = search_by_title(video_title, top_n)
                # Add video info to matches
                for match in matches:
                    match['youtube_title'] = video_title
                    match['youtube_duration'] = yt_duration
                    match['search_type'] = 'YouTube URL Search'
            else:
                matches = []
        else:
            matches = []
    else:
        # Default title search
        matches = search_by_title(query, top_n)

    search_time = time.time() - start_time
    logging.info(f"Search completed in {search_time:.3f}s - found {len(matches)} matches")

    return matches[:top_n]

# Fast cached search function for title-based searches
def search_by_title(video_title, top_n=5):
    """Search for matching filenames by title in cached data"""
    global cached_data

    if not cached_data:
        return []

    all_matches = []
    video_clean = smart_clean(video_title)
    video_keywords = extract_keywords(video_title)

    # Search through cached data (lightning fast!)
    for item in cached_data:
        filename = item['filename']
        file_duration = item['duration']
        sheet_name = item['sheet_name']

        file_clean = smart_clean(filename)
        file_keywords = extract_keywords(filename)
        keyword_overlap = len(video_keywords.intersection(file_keywords))
        token_score = fuzz.token_set_ratio(video_clean, file_clean)
        combined_score = token_score + (keyword_overlap * 5)

        # Get Google Drive link
        drive_link = search_in_google_drive(filename)

        all_matches.append({
            'filename': filename,
            'score': combined_score,
            'duration': file_duration,
            'sheet_name': sheet_name,
            'drive_link': drive_link,
            'search_type': 'Title Search'
        })

    # Sort by score and return top matches
    all_matches.sort(key=lambda x: x['score'], reverse=True)

    return all_matches

# Fallback function for live search (original implementation)
def match_filename_in_sheets_live(video_title, top_n=5):
    """Search for matching filenames across all Google Sheets (live/slow)"""
    all_matches = []
    video_clean = smart_clean(video_title)
    video_keywords = extract_keywords(video_title)

    for sheet_name in config.SHEET_NAMES:
        try:
            logging.info(f"Searching in sheet: {sheet_name}")
            df = fetch_sheet_data(config.GOOGLE_SHEET_ID, sheet_name)

            if df is None or df.empty:
                logging.warning(f"No data found in sheet: {sheet_name}")
                continue

            # Try to find filename and duration columns (case-insensitive)
            filename_col = None
            duration_col = None

            for col in df.columns:
                col_lower = col.lower()
                if 'filename' in col_lower or 'name' in col_lower or 'title' in col_lower:
                    filename_col = col
                if 'duration' in col_lower or 'time' in col_lower:
                    duration_col = col

            if not filename_col:
                logging.warning(f"No filename column found in sheet: {sheet_name}")
                continue

            filenames = df[filename_col].fillna('').tolist()
            durations = df[duration_col].fillna("00:00:00").tolist() if duration_col else ["00:00:00"] * len(filenames)

            for filename, file_duration in zip(filenames, durations):
                if not filename or filename.strip() == "":
                    continue

                file_clean = smart_clean(filename)
                file_keywords = extract_keywords(filename)
                keyword_overlap = len(video_keywords.intersection(file_keywords))
                token_score = fuzz.token_set_ratio(video_clean, file_clean)
                combined_score = token_score + (keyword_overlap * 5)

                # Get Google Drive link
                drive_link = search_in_google_drive(filename)

                all_matches.append({
                    'filename': filename,
                    'score': combined_score,
                    'duration': file_duration,
                    'sheet_name': sheet_name,
                    'drive_link': drive_link
                })

        except Exception as e:
            logging.error(f"Error processing sheet {sheet_name}: {str(e)}")
            continue

    # Sort by score and return top matches
    all_matches.sort(key=lambda x: x['score'], reverse=True)
    return all_matches[:top_n]

@app.route('/')
def index():
    return render_template('index.html')

# Endpoint for matching by URL
@app.route('/match_by_url', methods=['POST'])
def match_by_url():
    start_time = time.time()  # Start the timer

    try:
        url = request.form.get('url', '').strip()
        if not url:
            return jsonify({'error': 'Please paste a YouTube URL.'})

        if not is_valid_youtube_url(url):
            return jsonify({'error': 'Please enter a valid YouTube URL.'})

        video_title, yt_duration, yt_duration_seconds = get_video_info(url)
        if not video_title:
            return jsonify({'error': 'Failed to fetch video title. Please check the URL and try again.'})

        # Use universal search for all types
        top_matches = universal_search(url, config.MAX_RESULTS)

        if not top_matches:
            # Fallback to local CSV if available
            if os.path.exists('StemsName.csv'):
                logging.info("Falling back to local CSV file")
                csv_matches = match_filename(video_title, 'StemsName.csv')
                top_matches = [{'filename': m[0], 'score': m[1], 'duration': m[2], 'sheet_name': 'Local CSV', 'drive_link': None} for m in csv_matches]

        result = []
        for match in top_matches:
            file_duration_fmt, file_duration_seconds = format_seconds(match['duration'])

            # Check the mismatch condition
            duration_diff = abs(yt_duration_seconds - file_duration_seconds) if yt_duration_seconds > 0 else 0
            if duration_diff <= config.DURATION_TOLERANCE:
                color = "green"  # If within tolerance
            else:
                color = "red"  # If mismatch is more than tolerance

            result.append({
                'filename': match['filename'],
                'score': match['score'],
                'file_duration': file_duration_fmt,
                'yt_duration': match.get('youtube_duration', yt_duration),
                'color': color,
                'sheet_name': match['sheet_name'],
                'drive_link': match['drive_link'],
                'search_type': match.get('search_type', 'URL Search')
            })

        # Update the timer label with time taken for the task
        end_time = time.time()  # End the timer
        elapsed_time = round(end_time - start_time, 2)

        return jsonify({
            'video_title': video_title,
            'yt_duration': yt_duration,
            'result': result,
            'elapsed_time': elapsed_time,
            'total_matches': len(result)
        })

    except Exception as e:
        logging.error(f"Error in match_by_url: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'})

# Endpoint for matching by title and keyword
@app.route('/match_by_title', methods=['POST'])
def match_by_title():
    start_time = time.time()  # Start the timer

    try:
        title = request.form.get('title', '').strip()
        if not title:
            return jsonify({'error': 'Please enter a video title or keyword.'})

        # Use universal search for all types
        top_matches = universal_search(title, config.MAX_RESULTS)

        if not top_matches:
            # Fallback to local CSV if available
            if os.path.exists('StemsName.csv'):
                logging.info("Falling back to local CSV file")
                csv_matches = match_filename(title, 'StemsName.csv')
                top_matches = [{'filename': m[0], 'score': m[1], 'duration': m[2], 'sheet_name': 'Local CSV', 'drive_link': None} for m in csv_matches]

        result = []
        for match in top_matches:
            file_duration_fmt, file_duration_seconds = format_seconds(match['duration'])

            result.append({
                'filename': match['filename'],
                'score': match['score'],
                'file_duration': file_duration_fmt,
                'color': "blue",
                'sheet_name': match['sheet_name'],
                'drive_link': match['drive_link'],
                'search_type': match.get('search_type', 'Title Search')
            })

        # Update the timer label with time taken for the task
        end_time = time.time()  # End the timer
        elapsed_time = round(end_time - start_time, 2)

        return jsonify({
            'title': title,
            'result': result,
            'elapsed_time': elapsed_time,
            'total_matches': len(result)
        })

    except Exception as e:
        logging.error(f"Error in match_by_title: {str(e)}")
        return jsonify({'error': f'An error occurred: {str(e)}'})

# Cache management endpoints
@app.route('/cache/status')
def cache_status():
    """Get cache status information"""
    global cached_data, cache_last_updated

    cache_last_updated_loaded, total_rows, sheets_processed, _ = load_cache_metadata()

    return jsonify({
        'cache_loaded': cached_data is not None,
        'cache_size': len(cached_data) if cached_data else 0,
        'last_updated': cache_last_updated_loaded.isoformat() if cache_last_updated_loaded else None,
        'is_valid': is_cache_valid(),
        'total_rows': total_rows,
        'sheets_processed': sheets_processed,
        'cache_duration_hours': config.CACHE_DURATION_HOURS,
        'next_refresh': (cache_last_updated_loaded + timedelta(hours=config.CACHE_DURATION_HOURS)).isoformat() if cache_last_updated_loaded else None
    })

@app.route('/cache/refresh', methods=['POST'])
def manual_cache_refresh():
    """Manually trigger cache refresh"""
    try:
        logging.info("Manual cache refresh triggered")
        success = refresh_cache()
        if success:
            return jsonify({
                'status': 'success',
                'message': 'Cache refreshed successfully',
                'cache_size': len(cached_data) if cached_data else 0,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Cache refresh failed'
            }), 500
    except Exception as e:
        logging.error(f"Error in manual cache refresh: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Cache refresh failed: {str(e)}'
        }), 500

@app.route('/cache/refresh/full', methods=['POST'])
def manual_full_cache_refresh():
    """Manually trigger full cache refresh"""
    try:
        logging.info("Manual FULL cache refresh triggered")
        success = refresh_cache_full()

        if success:
            cache_size = len(cached_data) if cached_data else 0
            return jsonify({
                'status': 'success',
                'message': 'Full cache refresh completed successfully',
                'cache_size': cache_size,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Full cache refresh failed'
            }), 500

    except Exception as e:
        logging.error(f"Error in manual full cache refresh: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'Full cache refresh failed: {str(e)}'
        }), 500

# Health check endpoint
@app.route('/health')
def health_check():
    """Health check endpoint"""
    global cached_data
    cache_last_updated_loaded, total_rows, sheets_processed, _ = load_cache_metadata()

    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'google_sheets_configured': os.path.exists(config.CREDENTIALS_FILE),
        'cache_status': {
            'loaded': cached_data is not None,
            'size': len(cached_data) if cached_data else 0,
            'last_updated': cache_last_updated_loaded.isoformat() if cache_last_updated_loaded else None,
            'is_valid': is_cache_valid()
        }
    })

if __name__ == '__main__':
    logging.info("Starting Stems Finder Flask Application with Smart Caching")
    logging.info(f"Google Sheets ID: {config.GOOGLE_SHEET_ID}")
    logging.info(f"Available sheets: {', '.join(config.SHEET_NAMES)}")
    logging.info(f"Cache refresh interval: {config.CACHE_DURATION_HOURS} hours")

    # Start background cache initialization (non-blocking)
    def initialize_cache_background():
        logging.info("Initializing cache system in background...")
        ensure_cache_ready()
        start_cache_refresh_scheduler()

    cache_thread = threading.Thread(target=initialize_cache_background, daemon=True)
    cache_thread.start()

    # Run on port 8080 instead of 5000
    logging.info("Starting Flask server on http://localhost:8080")
    app.run(debug=True, host='0.0.0.0', port=8080)
