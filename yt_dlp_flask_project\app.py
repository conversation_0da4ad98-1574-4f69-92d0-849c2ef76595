#!/usr/bin/env python3
"""
Archives Stems Finder Pro - Clean Version with Google OAuth2
Production-ready Flask application with NGINX proxy support
"""

import os
import time
import logging
import pandas as pd
import csv
from datetime import datetime
from flask import Flask, request, jsonify, session, redirect, url_for, render_template
from werkzeug.middleware.proxy_fix import ProxyFix
from functools import wraps
import secrets

# Google OAuth2 imports
from authlib.integrations.flask_client import OAuth
from authlib.common.security import generate_token

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

class Config:
    """Application configuration"""
    APP_NAME = "Archives Stems Finder Pro"
    APP_VERSION = "3.0 OAuth2"
    CACHE_FILE = 'archives_cache.csv'
    ACL_FILE = 'data/acl_file.csv'
    
    # OAuth2 Configuration - Set these environment variables
    GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')
    GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET')
    SECRET_KEY = os.environ.get('SECRET_KEY', secrets.token_hex(32))
    
    # Security Configuration
    SESSION_TIMEOUT_MINUTES = 480  # 8 hours

# Initialize Flask app with proxy support
app = Flask(__name__)
app.config['SECRET_KEY'] = Config.SECRET_KEY

# Configure for NGINX reverse proxy
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)

# Initialize OAuth
oauth = OAuth(app)
google = oauth.register(
    name='google',
    client_id=Config.GOOGLE_CLIENT_ID,
    client_secret=Config.GOOGLE_CLIENT_SECRET,
    authorize_url='https://accounts.google.com/o/oauth2/auth',
    access_token_url='https://oauth2.googleapis.com/token',
    client_kwargs={
        'scope': 'email profile'
    }
)

# Global variables
cached_df = None
cache_last_updated = None
allowed_emails = set()

def load_allowed_emails():
    """Load allowed emails from ACL file"""
    global allowed_emails
    try:
        with open(Config.ACL_FILE, 'r', newline='', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            allowed_emails = {row['email'].strip().lower() for row in reader}
        logging.info(f"✅ Loaded {len(allowed_emails)} allowed emails from ACL")
        return True
    except Exception as e:
        logging.error(f"❌ Failed to load ACL file: {e}")
        return False

def is_email_allowed(email):
    """Check if email is in the allowed list"""
    return email.lower() in allowed_emails

def require_auth(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def load_cache():
    """Load cache from CSV file"""
    global cached_df, cache_last_updated
    try:
        if os.path.exists(Config.CACHE_FILE):
            cached_df = pd.read_csv(Config.CACHE_FILE)
            cache_last_updated = datetime.now()
            logging.info(f"✅ Cache loaded: {len(cached_df)} rows")
            return True
        else:
            logging.warning("⚠️ Cache file not found")
            return False
    except Exception as e:
        logging.error(f"❌ Failed to load cache: {e}")
        return False

def search_cache_simple(query):
    """Simple cache search function"""
    if cached_df is None or cached_df.empty:
        return []
    
    matches = []
    query_lower = query.lower()
    
    try:
        # Search in filename column
        mask = cached_df['filename'].astype(str).str.lower().str.contains(query_lower, na=False)
        results = cached_df[mask]
        
        for _, row in results.iterrows():
            matches.append({
                'filename': str(row.get('filename', '')),
                'ocd_vp': str(row.get('ocd_vp', '')) if pd.notna(row.get('ocd_vp')) else 'Not Available',
                'video_id': str(row.get('video_id', '')) if pd.notna(row.get('video_id')) else 'Not Available',
                'sheet_name': str(row.get('sheet_name', '')),
                'score': 100,
                'match_type': 'Simple Match',
                'matched_variation': query,
                'row_index': 0
            })
        
        return matches[:20]  # Return top 20 matches
        
    except Exception as e:
        logging.error(f"❌ Search error: {e}")
        return []

# Routes
@app.route('/')
def index():
    """Main page"""
    if 'user' not in session:
        return redirect(url_for('login'))
    return render_template('main.html',
                         user=session['user'],
                         cache_info=get_cache_info())

@app.route('/login')
def login():
    """Login page - redirect to Google OAuth"""
    if 'user' in session:
        return redirect(url_for('index'))
    
    # Generate state token for security
    session['oauth_state'] = generate_token()
    
    # Use relative URL for callback
    redirect_uri = url_for('auth_callback', _external=True)
    return google.authorize_redirect(redirect_uri, state=session['oauth_state'])

@app.route('/auth/callback')
def auth_callback():
    """OAuth callback handler"""
    # Verify state token
    if request.args.get('state') != session.get('oauth_state'):
        logging.warning("❌ OAuth state mismatch")
        return redirect(url_for('login'))
    
    try:
        # Get the authorization token without parsing JWT
        token = google.authorize_access_token()

        # Get user info from Google using the access token
        import requests
        resp = requests.get(
            'https://www.googleapis.com/oauth2/v2/userinfo',
            headers={'Authorization': f'Bearer {token["access_token"]}'},
            timeout=10
        )

        if resp.status_code == 200:
            user_info = resp.json()

            if user_info and 'email' in user_info:
                email = user_info.get('email')

                # Check if email is allowed
                if not is_email_allowed(email):
                    logging.warning(f"❌ Unauthorized access attempt: {email}")
                    return render_template('error.html',
                                         error="Access Denied",
                                         message="Your email is not authorized to access this application.")

                # Store user info in session
                session['user'] = {
                    'email': email,
                    'name': user_info.get('name', 'User'),
                    'picture': user_info.get('picture', ''),
                    'login_time': datetime.now().isoformat()
                }

                logging.info(f"✅ User logged in: {email}")
                return redirect(url_for('index'))
            else:
                logging.error("❌ No email in user info")
                return render_template('error.html',
                                     error="Authentication Error",
                                     message="Could not retrieve email from Google.")
        else:
            logging.error(f"❌ Failed to get user info: {resp.status_code}")
            return render_template('error.html',
                                 error="Authentication Error",
                                 message="Failed to retrieve user information from Google.")
        
    except Exception as e:
        logging.error(f"❌ OAuth callback error: {e}")
        return render_template('error.html',
                             error="Authentication Error",
                             message="Failed to authenticate with Google.")
    
    return redirect(url_for('login'))

@app.route('/logout')
def logout():
    """Logout user"""
    if 'user' in session:
        email = session['user'].get('email')
        logging.info(f"✅ User logged out: {email}")
        session.clear()
    return redirect(url_for('login'))

@app.route('/search', methods=['POST'])
@require_auth
def search():
    """Search endpoint"""
    query = request.form.get('query', '').strip()
    
    if not query:
        return jsonify({
            'error': 'No query provided',
            'total_matches': 0,
            'results': []
        })
    
    start_time = time.time()
    
    try:
        matches = search_cache_simple(query)
        elapsed = time.time() - start_time
        
        logging.info(f"🔍 Search by {session['user']['email']}: '{query}' → {len(matches)} results in {elapsed:.3f}s")
        
        return jsonify({
            'query': query,
            'total_matches': len(matches),
            'results': matches,
            'search_time': round(elapsed, 3),
            'cache_info': get_cache_info()
        })
        
    except Exception as e:
        logging.error(f"❌ Search error: {e}")
        return jsonify({
            'error': str(e),
            'total_matches': 0,
            'results': []
        })

def get_cache_info():
    """Get cache information"""
    global cached_df, cache_last_updated
    
    if cached_df is not None:
        return {
            'total_records': len(cached_df),
            'last_updated': cache_last_updated.strftime('%Y-%m-%d %H:%M:%S') if cache_last_updated else 'Unknown',
            'status': 'Loaded'
        }
    else:
        return {
            'total_records': 0,
            'last_updated': 'Never',
            'status': 'Not Loaded'
        }

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'app': Config.APP_NAME,
        'version': Config.APP_VERSION,
        'timestamp': datetime.now().isoformat(),
        'cache_status': 'loaded' if cached_df is not None else 'not_loaded'
    })

if __name__ == '__main__':
    print(f"🚀 Starting {Config.APP_NAME} v{Config.APP_VERSION}")
    print("=" * 60)
    
    # Check environment variables
    if not Config.GOOGLE_CLIENT_ID or not Config.GOOGLE_CLIENT_SECRET:
        print("❌ Missing Google OAuth2 credentials!")
        print("Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables")
        exit(1)
    
    # Load allowed emails
    if not load_allowed_emails():
        print("❌ Failed to load ACL file. Exiting.")
        exit(1)
    
    # Load cache
    if load_cache():
        print("✅ Cache loaded successfully")
    else:
        print("⚠️ Cache not loaded - some features may not work")
    
    print("🔐 OAuth2 Authentication enabled")
    print("🌐 Domain: https://publications.isha.in/stemsfinder")
    print("📧 Check data/acl_file.csv for allowed emails")
    print("=" * 60)
    
    # Run the app
    app.run(host='127.0.0.1', port=5000, debug=False)
