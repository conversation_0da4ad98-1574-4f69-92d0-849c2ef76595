#!/usr/bin/env python3
"""
Archives Stems Finder Pro - Clean Version with Google OAuth2
Production-ready Flask application with NGINX proxy support
"""

import os
import time
import logging
import pandas as pd
import csv
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, session, redirect, url_for, render_template
from werkzeug.middleware.proxy_fix import ProxyFix
from functools import wraps
import secrets
import threading
import schedule
from fuzzywuzzy import fuzz
import gspread
from google.oauth2.service_account import Credentials
from collections import defaultdict

# Google OAuth2 imports
from authlib.integrations.flask_client import OAuth
from authlib.common.security import generate_token

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

class Config:
    """Application configuration"""
    APP_NAME = "Archives Stems Finder Pro"
    APP_VERSION = "3.0 OAuth2"
    CACHE_FILE = 'archives_cache.csv'
    ACL_FILE = 'data/acl_file.csv'
    GOOGLE_SHEET_ID = "1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE"
    SHEET_NAMES = [
        "Edited Main Sheet",
        "Social Media Catalog(SG)",
        "Social Media Catalog(IF)",
        "Social Media Catalog(IG)",
        "Social Media Catalog(CP)",
        "Copy Social Media Catalog(SG)",
        "Copy Social Media Catalog(IF)",
        "Copy Social Media Catalog(IG)"
    ]
    AUTO_REFRESH_TIME = "03:00"  # 3:00 AM daily refresh

    # OAuth2 Configuration - Set these environment variables
    GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')
    GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET')
    SECRET_KEY = os.environ.get('SECRET_KEY', secrets.token_hex(32))

    # Security Configuration
    MAX_CONCURRENT_USERS = 100
    SESSION_TIMEOUT_MINUTES = 480  # 8 hours
    RATE_LIMIT_REQUESTS_PER_MINUTE = 60

# Initialize Flask app with proxy support
app = Flask(__name__)
app.config['SECRET_KEY'] = Config.SECRET_KEY

# Configure for NGINX reverse proxy
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)

# Initialize OAuth
oauth = OAuth(app)
google = oauth.register(
    name='google',
    client_id=Config.GOOGLE_CLIENT_ID,
    client_secret=Config.GOOGLE_CLIENT_SECRET,
    authorize_url='https://accounts.google.com/o/oauth2/auth',
    access_token_url='https://oauth2.googleapis.com/token',
    client_kwargs={
        'scope': 'email profile'
    }
)

# Global variables
cached_df = None
cache_last_updated = None
next_refresh_time = None
allowed_emails = set()
cache_refresh_in_progress = False

def load_allowed_emails():
    """Load allowed emails from ACL file"""
    global allowed_emails
    try:
        with open(Config.ACL_FILE, 'r', newline='', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            allowed_emails = set()
            for row in reader:
                if 'email' in row and row['email']:
                    email = row['email'].strip().lower()
                    if email:  # Only add non-empty emails
                        allowed_emails.add(email)
        logging.info(f"Loaded {len(allowed_emails)} allowed emails from ACL")
        return True
    except Exception as e:
        logging.error(f"Failed to load ACL file: {e}")
        return False

def is_email_allowed(email):
    """Check if email is in the allowed list"""
    return email.lower() in allowed_emails

def require_auth(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def load_cache():
    """Load cache from CSV file"""
    global cached_df, cache_last_updated, next_refresh_time
    try:
        if os.path.exists(Config.CACHE_FILE):
            cached_df = pd.read_csv(Config.CACHE_FILE)
            cache_last_updated = datetime.fromtimestamp(os.path.getmtime(Config.CACHE_FILE))
            next_refresh_time = calculate_next_refresh()
            logging.info(f"Cache loaded: {len(cached_df)} rows")
            return True
        else:
            logging.warning("Cache file not found")
            return False
    except Exception as e:
        logging.error(f"Failed to load cache: {e}")
        return False

def download_sheet_data(sheet_name):
    """Download data from a specific Google Sheet with error handling"""
    try:
        # Check if credentials file exists
        if not os.path.exists('credentials.json'):
            logging.error("Google Sheets credentials file 'credentials.json' not found")
            return pd.DataFrame()

        # Load Google Sheets credentials
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        creds = Credentials.from_service_account_file('credentials.json', scopes=scope)
        client = gspread.authorize(creds)

        # Open the spreadsheet and worksheet
        spreadsheet = client.open_by_key(Config.GOOGLE_SHEET_ID)
        worksheet = spreadsheet.worksheet(sheet_name)

        # Handle sheets with duplicate headers (like "Edited Main Sheet")
        try:
            records = worksheet.get_all_records()
        except Exception as header_error:
            if "duplicates" in str(header_error).lower():
                logging.warning(f"Sheet {sheet_name} has duplicate headers, using raw values")
                # Get all values and create DataFrame manually
                all_values = worksheet.get_all_values()
                if len(all_values) > 1:
                    # Use first row as headers, but handle duplicates
                    headers = all_values[0]
                    # Fix duplicate headers by adding numbers
                    seen_headers = {}
                    fixed_headers = []
                    for header in headers:
                        if header == '' or header in seen_headers:
                            # Create unique header name
                            base_name = header if header else 'unnamed'
                            count = seen_headers.get(base_name, 0) + 1
                            seen_headers[base_name] = count
                            fixed_headers.append(f"{base_name}_{count}" if base_name != 'unnamed' else f"col_{count}")
                        else:
                            seen_headers[header] = 0
                            fixed_headers.append(header)

                    # Create DataFrame with fixed headers
                    data_rows = all_values[1:]
                    df = pd.DataFrame(data_rows, columns=fixed_headers)

                    # Convert to records format
                    records = df.to_dict('records')
                else:
                    records = []
            else:
                raise header_error

        if records:
            df = pd.DataFrame(records)

            # Try to find the right columns (flexible column mapping)
            filename_col = None
            ocd_vp_col = None
            video_id_col = None

            # Look for filename column (more flexible patterns)
            for col in df.columns:
                col_lower = str(col).lower()
                if ('filename' in col_lower or 'file' in col_lower or 'name' in col_lower or
                    'title' in col_lower or 'stems' in col_lower):
                    filename_col = col
                    break

            # Look for OCD/VP column (more flexible patterns)
            for col in df.columns:
                col_lower = str(col).lower()
                if ('ocd' in col_lower or 'vp' in col_lower or 'ticket' in col_lower or
                    'number' in col_lower or '#' in col_lower):
                    ocd_vp_col = col
                    break

            # Look for video ID column (more flexible patterns)
            for col in df.columns:
                col_lower = str(col).lower()
                if (('video' in col_lower and 'id' in col_lower) or 'videoid' in col_lower or
                    (col_lower == 'id' and 'video' in str(df.columns).lower())):
                    video_id_col = col
                    break

            # Log what columns were found for debugging
            logging.info(f"Sheet {sheet_name} - Found columns: filename='{filename_col}', ocd_vp='{ocd_vp_col}', video_id='{video_id_col}'")

            # Create standardized columns
            df['filename'] = df[filename_col].fillna('').astype(str) if filename_col else ''
            df['ocd_vp'] = df[ocd_vp_col].fillna('').astype(str) if ocd_vp_col else ''
            df['video_id'] = df[video_id_col].fillna('').astype(str) if video_id_col else ''
            df['sheet_name'] = sheet_name

            # Remove empty rows
            df = df[df['filename'].str.len() > 0]

            logging.info(f"Downloaded {len(df)} rows from {sheet_name}")
            return df[['filename', 'ocd_vp', 'video_id', 'sheet_name']]  # Return only needed columns
        else:
            logging.warning(f"No records found in {sheet_name}")
            return pd.DataFrame()

    except Exception as e:
        logging.error(f"Error downloading {sheet_name}: {e}")
        return pd.DataFrame()

def build_cache_from_google_sheets(progress_callback=None):
    """Build cache by downloading all Google Sheets"""
    global cached_df, cache_last_updated, next_refresh_time, cache_refresh_in_progress

    cache_refresh_in_progress = True
    logging.info("Building cache from Google Sheets...")
    start_time = time.time()

    all_data = []
    total_sheets = len(Config.SHEET_NAMES)

    if progress_callback:
        progress_callback(0, "Initializing cache refresh...")

    for i, sheet_name in enumerate(Config.SHEET_NAMES, 1):
        if progress_callback:
            progress = int((i / total_sheets) * 80)
            progress_callback(progress, f"Downloading {sheet_name} ({i}/{total_sheets})")

        logging.info(f"[{i}/{total_sheets}] Downloading {sheet_name}")
        sheet_data = download_sheet_data(sheet_name)

        if not sheet_data.empty:
            all_data.append(sheet_data)
            logging.info(f"Got {len(sheet_data)} rows from {sheet_name}")
        else:
            logging.warning(f"No data from {sheet_name}")

    if progress_callback:
        progress_callback(85, "Processing and merging data...")

    if all_data:
        cached_df = pd.concat(all_data, ignore_index=True)
        cache_last_updated = datetime.now()
        next_refresh_time = calculate_next_refresh()

        if progress_callback:
            progress_callback(95, "Saving cache file...")

        # Save to cache file
        cached_df.to_csv(Config.CACHE_FILE, index=False)

        if progress_callback:
            progress_callback(100, "Cache refresh completed!")

        elapsed = time.time() - start_time
        logging.info(f"SUCCESS! Built cache: {len(cached_df):,} rows in {elapsed:.3f}s")
        cache_refresh_in_progress = False
        return True
    else:
        logging.error("FAILED! No data downloaded")
        cache_refresh_in_progress = False
        return False

def calculate_next_refresh():
    """Calculate next 3:00 AM refresh time"""
    now = datetime.now()
    next_3am = now.replace(hour=3, minute=0, second=0, microsecond=0)

    # If it's already past 3 AM today, schedule for tomorrow
    if now.hour >= 3:
        next_3am += timedelta(days=1)

    return next_3am

def auto_refresh_cache():
    """Automatic cache refresh function"""
    logging.info("Automatic cache refresh triggered at 3:00 AM")
    success = build_cache_from_google_sheets()
    if success:
        logging.info("Automatic cache refresh completed successfully")
    else:
        logging.error("Automatic cache refresh failed")

def start_scheduler():
    """Start the background scheduler for automatic cache refresh"""
    def run_scheduler():
        schedule.every().day.at(Config.AUTO_REFRESH_TIME).do(auto_refresh_cache)

        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    logging.info(f"Automatic cache refresh scheduled for {Config.AUTO_REFRESH_TIME} daily")

def search_cache_enhanced(query):
    """Enhanced search with multiple strategies for better results"""
    if cached_df is None or cached_df.empty:
        logging.error("No cache loaded for search")
        return []

    query = query.strip()
    logging.info(f"Enhanced search for: '{query}' across {len(cached_df):,} rows")

    matches = []

    try:
        # Strategy 1: Exact match in video_id column
        video_id_matches = cached_df[cached_df['video_id'].astype(str).str.contains(query, case=False, na=False)]
        for _, row in video_id_matches.iterrows():
            matches.append({
                'filename': str(row.get('filename', '')),
                'ocd_vp': str(row.get('ocd_vp', '')) if pd.notna(row.get('ocd_vp')) else 'Not Available',
                'video_id': str(row.get('video_id', '')) if pd.notna(row.get('video_id')) else 'Not Available',
                'sheet_name': str(row.get('sheet_name', '')),
                'score': 100,
                'match_type': 'Video ID Match',
                'matched_variation': query,
                'row_index': 0
            })

        # Strategy 2: Exact match in ocd_vp column
        ocd_matches = cached_df[cached_df['ocd_vp'].astype(str).str.contains(query, case=False, na=False)]
        for _, row in ocd_matches.iterrows():
            matches.append({
                'filename': str(row.get('filename', '')),
                'ocd_vp': str(row.get('ocd_vp', '')) if pd.notna(row.get('ocd_vp')) else 'Not Available',
                'video_id': str(row.get('video_id', '')) if pd.notna(row.get('video_id')) else 'Not Available',
                'sheet_name': str(row.get('sheet_name', '')),
                'score': 95,
                'match_type': 'OCD/VP Match',
                'matched_variation': query,
                'row_index': 0
            })

        # Strategy 3: Filename search
        filename_matches = cached_df[cached_df['filename'].astype(str).str.lower().str.contains(query.lower(), na=False)]
        for _, row in filename_matches.iterrows():
            matches.append({
                'filename': str(row.get('filename', '')),
                'ocd_vp': str(row.get('ocd_vp', '')) if pd.notna(row.get('ocd_vp')) else 'Not Available',
                'video_id': str(row.get('video_id', '')) if pd.notna(row.get('video_id')) else 'Not Available',
                'sheet_name': str(row.get('sheet_name', '')),
                'score': 90,
                'match_type': 'Filename Match',
                'matched_variation': query,
                'row_index': 0
            })

        # Strategy 4: Fuzzy matching for partial queries
        if len(matches) < 5:  # Only if we don't have enough matches
            for _, row in cached_df.iterrows():
                filename = str(row.get('filename', ''))
                video_id = str(row.get('video_id', ''))
                ocd_vp = str(row.get('ocd_vp', ''))

                # Check fuzzy match
                filename_score = fuzz.partial_ratio(query.lower(), filename.lower())
                video_id_score = fuzz.ratio(query.lower(), video_id.lower())
                ocd_score = fuzz.ratio(query.lower(), ocd_vp.lower())

                max_score = max(filename_score, video_id_score, ocd_score)

                if max_score > 70:  # Threshold for fuzzy matching
                    matches.append({
                        'filename': filename,
                        'ocd_vp': ocd_vp if pd.notna(row.get('ocd_vp')) else 'Not Available',
                        'video_id': video_id if pd.notna(row.get('video_id')) else 'Not Available',
                        'sheet_name': str(row.get('sheet_name', '')),
                        'score': max_score,
                        'match_type': 'Fuzzy Match',
                        'matched_variation': query,
                        'row_index': 0
                    })

        # Remove duplicates and sort by score
        seen_filenames = set()
        unique_matches = []
        for match in matches:
            filename = match['filename']
            if filename not in seen_filenames:
                seen_filenames.add(filename)
                unique_matches.append(match)

        # Sort by score (highest first)
        unique_matches.sort(key=lambda x: x['score'], reverse=True)

        logging.info(f"Enhanced search completed: {len(unique_matches)} unique matches")
        return unique_matches[:20]  # Return top 20 matches

    except Exception as e:
        logging.error(f"Enhanced search error: {e}")
        return []

# Routes
@app.route('/')
def index():
    """Main page"""
    if 'user' not in session:
        return redirect(url_for('login'))
    return render_template('main.html',
                         user=session['user'],
                         cache_info=get_cache_info())

@app.route('/login')
def login():
    """Login page - redirect to Google OAuth"""
    if 'user' in session:
        return redirect(url_for('index'))
    
    # Generate state token for security
    session['oauth_state'] = generate_token()
    
    # Use relative URL for callback
    redirect_uri = url_for('auth_callback', _external=True)
    return google.authorize_redirect(redirect_uri, state=session['oauth_state'])

@app.route('/auth/callback')
def auth_callback():
    """OAuth callback handler"""
    # Verify state token
    if request.args.get('state') != session.get('oauth_state'):
        logging.warning("❌ OAuth state mismatch")
        return redirect(url_for('login'))
    
    try:
        # Get the authorization token without parsing JWT
        token = google.authorize_access_token()

        # Get user info from Google using the access token
        import requests
        resp = requests.get(
            'https://www.googleapis.com/oauth2/v2/userinfo',
            headers={'Authorization': f'Bearer {token["access_token"]}'},
            timeout=10
        )

        if resp.status_code == 200:
            user_info = resp.json()

            if user_info and 'email' in user_info:
                email = user_info.get('email')

                # Check if email is allowed
                if not is_email_allowed(email):
                    logging.warning(f"❌ Unauthorized access attempt: {email}")
                    return render_template('error.html',
                                         error="Access Denied",
                                         message="Your email is not authorized to access this application.")

                # Store user info in session
                session['user'] = {
                    'email': email,
                    'name': user_info.get('name', 'User'),
                    'picture': user_info.get('picture', ''),
                    'login_time': datetime.now().isoformat()
                }

                logging.info(f"✅ User logged in: {email}")
                return redirect(url_for('index'))
            else:
                logging.error("❌ No email in user info")
                return render_template('error.html',
                                     error="Authentication Error",
                                     message="Could not retrieve email from Google.")
        else:
            logging.error(f"❌ Failed to get user info: {resp.status_code}")
            return render_template('error.html',
                                 error="Authentication Error",
                                 message="Failed to retrieve user information from Google.")
        
    except Exception as e:
        logging.error(f"OAuth callback error: {e}")
        return render_template('error.html',
                             error="Authentication Error",
                             message="Failed to authenticate with Google.")

@app.route('/logout')
def logout():
    """Logout user"""
    if 'user' in session:
        email = session['user'].get('email')
        logging.info(f"✅ User logged out: {email}")
        session.clear()
    return redirect(url_for('login'))

@app.route('/search', methods=['POST'])
@require_auth
def search():
    """Search endpoint"""
    query = request.form.get('query', '').strip()
    
    if not query:
        return jsonify({
            'error': 'No query provided',
            'total_matches': 0,
            'results': []
        })
    
    start_time = time.time()
    
    try:
        matches = search_cache_enhanced(query)
        elapsed = time.time() - start_time
        
        logging.info(f"🔍 Search by {session['user']['email']}: '{query}' → {len(matches)} results in {elapsed:.3f}s")
        
        return jsonify({
            'query': query,
            'total_matches': len(matches),
            'results': matches,
            'search_time': round(elapsed, 3),
            'cache_info': get_cache_info()
        })
        
    except Exception as e:
        logging.error(f"❌ Search error: {e}")
        return jsonify({
            'error': str(e),
            'total_matches': 0,
            'results': []
        })

def get_cache_info():
    """Get cache information"""
    global cached_df, cache_last_updated, next_refresh_time

    if cached_df is not None:
        return {
            'total_records': len(cached_df),
            'last_updated': cache_last_updated.strftime('%Y-%m-%d %H:%M:%S') if cache_last_updated else 'Unknown',
            'next_refresh': next_refresh_time.strftime('%Y-%m-%d %H:%M:%S') if next_refresh_time else 'Unknown',
            'status': 'Loaded',
            'refresh_in_progress': cache_refresh_in_progress
        }
    else:
        return {
            'total_records': 0,
            'last_updated': 'Never',
            'next_refresh': 'Unknown',
            'status': 'Not Loaded',
            'refresh_in_progress': cache_refresh_in_progress
        }

@app.route('/cache/status')
@require_auth
def cache_status():
    """Get cache status information"""
    return jsonify({
        'cache_loaded': cached_df is not None,
        'cache_size': len(cached_df) if cached_df is not None else 0,
        'app_version': Config.APP_VERSION,
        'cache_file': Config.CACHE_FILE,
        'last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
        'next_refresh': next_refresh_time.isoformat() if next_refresh_time else None,
        'refresh_in_progress': cache_refresh_in_progress
    })

@app.route('/cache/refresh', methods=['POST'])
@require_auth
def manual_cache_refresh():
    """Manual cache refresh endpoint with progress tracking"""
    try:
        logging.info("Manual cache refresh triggered by user")

        # Check if Google Sheets credentials are available
        if not os.path.exists('credentials.json'):
            return jsonify({
                'success': False,
                'error': 'Google Sheets credentials not found. Please add credentials.json file.',
                'progress': 0
            }), 400

        # Track progress for better user experience
        progress_data = {'current': 0, 'message': 'Starting...'}

        def progress_callback(percentage, message):
            progress_data['current'] = percentage
            progress_data['message'] = message
            logging.info(f"Progress: {percentage}% - {message}")

        success = build_cache_from_google_sheets(progress_callback)

        if success:
            cache_size = len(cached_df) if cached_df is not None else 0
            return jsonify({
                'success': True,
                'message': f'Cache refreshed successfully! Loaded {cache_size:,} records.',
                'cache_size': cache_size,
                'last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
                'next_refresh': next_refresh_time.isoformat() if next_refresh_time else None,
                'progress': 100
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to refresh cache from Google Sheets. Check logs for details.',
                'progress': 0
            }), 500

    except Exception as e:
        logging.error(f"Manual cache refresh failed: {e}")
        return jsonify({
            'success': False,
            'error': f'Cache refresh failed: {str(e)}',
            'progress': 0
        }), 500

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'app': Config.APP_NAME,
        'version': Config.APP_VERSION,
        'timestamp': datetime.now().isoformat(),
        'cache_status': 'loaded' if cached_df is not None else 'not_loaded'
    })

if __name__ == '__main__':
    print(f"Starting {Config.APP_NAME} v{Config.APP_VERSION}")
    print("=" * 60)

    # Check environment variables
    if not Config.GOOGLE_CLIENT_ID or not Config.GOOGLE_CLIENT_SECRET:
        print("Missing Google OAuth2 credentials!")
        print("Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables")
        exit(1)

    # Load allowed emails
    if not load_allowed_emails():
        print("Failed to load ACL file. Exiting.")
        exit(1)

    # Load cache
    if load_cache():
        print("Cache loaded successfully")
    else:
        print("Cache not loaded - some features may not work")

    # Start automatic cache refresh scheduler
    start_scheduler()

    print("OAuth2 Authentication enabled")
    print("Domain: https://publications.isha.in/stemsfinder")
    print("Check data/acl_file.csv for allowed emails")
    print(f"Automatic cache refresh scheduled for {Config.AUTO_REFRESH_TIME} daily")
    print("=" * 60)

    # Run the app
    app.run(host='127.0.0.1', port=5000, debug=False)
