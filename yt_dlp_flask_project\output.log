2025-06-03 18:47:03,774 - INFO - \U0001f680 Starting Archives Stems Finder Pro v2.0 OPTIMIZED
2025-06-03 18:47:03,775 - INFO - Cache file: archives_cache.csv
2025-06-03 18:47:03,839 - INFO - \u26a1 Cache loaded: 21762 rows in 0.063s
 * Serving Flask app 'lightning_app_clean'
 * Debug mode: on
2025-06-03 18:47:03,893 - INFO - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://**************:8080
2025-06-03 18:47:03,894 - INFO - Press CTRL+C to quit
2025-06-03 18:47:03,913 - INFO -  * Restarting with watchdog (windowsapi)
2025-06-03 18:47:05,193 - INFO - \U0001f680 Starting Archives Stems Finder Pro v2.0 OPTIMIZED
2025-06-03 18:47:05,194 - INFO - Cache file: archives_cache.csv
2025-06-03 18:47:05,261 - INFO - \u26a1 Cache loaded: 21762 rows in 0.067s
2025-06-03 18:47:05,315 - WARNING -  * Debugger is active!
2025-06-03 18:47:05,325 - INFO -  * Debugger PIN: 653-556-705
2025-06-03 18:47:05,387 - INFO - 127.0.0.1 - - [03/Jun/2025 18:47:05] "GET / HTTP/1.1" 302 -
2025-06-03 18:47:05,408 - INFO - 127.0.0.1 - - [03/Jun/2025 18:47:05] "GET /login HTTP/1.1" 200 -
2025-06-03 18:47:12,948 - INFO - 127.0.0.1 - - [03/Jun/2025 18:47:12] "POST /login HTTP/1.1" 302 -
2025-06-03 18:47:12,967 - INFO - 127.0.0.1 - - [03/Jun/2025 18:47:12] "GET / HTTP/1.1" 200 -
2025-06-03 18:47:13,026 - INFO - 127.0.0.1 - - [03/Jun/2025 18:47:13] "GET /cache/status HTTP/1.1" 200 -
2025-06-03 18:47:23,904 - INFO - 127.0.0.1 - - [03/Jun/2025 18:47:23] "POST /search HTTP/1.1" 200 -
2025-06-03 18:47:29,147 - INFO - ************** - - [03/Jun/2025 18:47:29] "GET /cache/status HTTP/1.1" 302 -
2025-06-03 18:47:29,169 - INFO - ************** - - [03/Jun/2025 18:47:29] "GET /login HTTP/1.1" 200 -
2025-06-03 18:47:35,150 - INFO - 127.0.0.1 - - [03/Jun/2025 18:47:35] "POST /search HTTP/1.1" 200 -
