#!/usr/bin/env python3
"""
Test Google Sheets connection directly
"""

import gspread
from google.oauth2.service_account import Credentials
import pandas as pd

def test_google_sheets():
    """Test Google Sheets connection"""
    try:
        print("Testing Google Sheets connection...")
        
        # Define the scope
        scope = ['https://spreadsheets.google.com/feeds',
                 'https://www.googleapis.com/auth/drive']
        
        # Load credentials
        print("Loading credentials...")
        credentials = Credentials.from_service_account_file('credentials.json', scopes=scope)
        client = gspread.authorize(credentials)
        print("✅ Google Sheets client initialized successfully")
        
        # Test opening the spreadsheet
        sheet_id = "1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE"
        print(f"Opening spreadsheet: {sheet_id}")
        spreadsheet = client.open_by_key(sheet_id)
        print(f"✅ Spreadsheet opened: {spreadsheet.title}")
        
        # List all worksheets
        worksheets = spreadsheet.worksheets()
        print(f"✅ Found {len(worksheets)} worksheets:")
        for ws in worksheets:
            print(f"   - {ws.title}")
        
        # Test downloading first sheet
        if worksheets:
            first_sheet = worksheets[0]
            print(f"\nTesting download from: {first_sheet.title}")
            
            # Get all values
            all_values = first_sheet.get_all_values()
            print(f"✅ Downloaded {len(all_values)} rows")
            
            if all_values:
                headers = all_values[0]
                print(f"✅ Headers: {headers[:5]}...")  # Show first 5 headers
                
                if len(all_values) > 1:
                    data = all_values[1:]
                    df = pd.DataFrame(data, columns=headers)
                    print(f"✅ Created DataFrame with {len(df)} rows")
                    
                    # Show sample data
                    print("\nSample data:")
                    print(df.head(3).to_string())
                    
                    return True
        
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    test_google_sheets()
