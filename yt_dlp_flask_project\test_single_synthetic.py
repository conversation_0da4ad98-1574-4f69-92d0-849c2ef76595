#!/usr/bin/env python3
"""
Test single synthetic search to debug the issue
"""

import requests
import json

def test_single_synthetic_search():
    """Test a single synthetic search with detailed debugging"""
    
    print("🔍 TESTING SINGLE SYNTHETIC SEARCH")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test CPN-zOup_uS specifically
    video_id = "CPN-zOup_uS"
    print(f"\n🔍 Testing: {video_id}")
    print("Expected: Should find Daily-Mystic-Quote with Z9906")
    
    search_response = session.post("http://127.0.0.1:8080/search", data={'query': video_id})
    
    print(f"Search response status: {search_response.status_code}")
    
    if search_response.status_code == 200:
        try:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"Total matches: {matches}")
            print(f"Search time: {search_time:.3f}s")
            
            if matches > 0:
                print("✅ FOUND MATCHES!")
                
                for i, match in enumerate(result.get('results', []), 1):
                    filename = match.get('filename', 'N/A')
                    ocd_vp = match.get('ocd_vp', 'N/A')
                    video_id_found = match.get('video_id', 'N/A')
                    match_type = match.get('match_type', 'Unknown')
                    score = match.get('score', 0)
                    
                    print(f"\n   Match {i}:")
                    print(f"   📄 Filename: {filename}")
                    print(f"   🔢 OCD/VP: {ocd_vp}")
                    print(f"   🎥 Video ID: {video_id_found}")
                    print(f"   🎯 Match Type: {match_type}")
                    print(f"   📊 Score: {score}")
                    
                    if "Daily-Mystic-Quote" in filename or "Z9906" in filename:
                        print(f"   🎉 PERFECT: Found the exact file user mentioned!")
                        return True
                    elif "Synthetic Match" in match_type:
                        print(f"   🎯 SYNTHETIC: Found synthetic match!")
                        return True
                
                return True
            else:
                print("❌ NO MATCHES FOUND")
                
                # Check if there's an error message
                error = result.get('error')
                if error:
                    print(f"Error: {error}")
                
                return False
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            print(f"Response text: {search_response.text[:500]}...")
            return False
    else:
        print(f"❌ Search request failed: {search_response.status_code}")
        print(f"Response: {search_response.text[:200]}...")
        return False

def test_known_working_search():
    """Test a known working search to verify the search function works"""
    
    print("\n🔍 TESTING KNOWN WORKING SEARCH")
    print("=" * 60)
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    # Test a video ID that we know exists
    test_response = session.post("http://127.0.0.1:8080/search", data={'query': 'f7ZrEl04CLk'})
    
    if test_response.status_code == 200:
        result = test_response.json()
        matches = result.get('total_matches', 0)
        print(f"Known working search (f7ZrEl04CLk): {matches} matches")
        
        if matches > 0:
            first_match = result['results'][0]
            print(f"   📄 {first_match.get('filename', 'N/A')[:50]}...")
            print(f"   🔢 {first_match.get('ocd_vp', 'N/A')}")
            return True
    
    print("❌ Known working search failed")
    return False

if __name__ == "__main__":
    print("🚀 TESTING SINGLE SYNTHETIC SEARCH")
    print("Debugging why synthetic matches are not working")
    
    # Test known working search first
    known_works = test_known_working_search()
    
    # Test synthetic search
    synthetic_works = test_single_synthetic_search()
    
    print("\n" + "=" * 60)
    print("🎯 SINGLE SYNTHETIC SEARCH TEST RESULTS")
    print("=" * 60)
    
    if known_works and synthetic_works:
        print("🎉 SUCCESS: Both known and synthetic searches work!")
        print("✅ Synthetic matches are functioning correctly!")
    elif known_works and not synthetic_works:
        print("🔧 PARTIAL: Known search works but synthetic doesn't")
        print("💡 Synthetic match function needs debugging")
    elif not known_works and synthetic_works:
        print("🤔 UNEXPECTED: Synthetic works but known search doesn't")
        print("💡 There may be a general search issue")
    else:
        print("❌ FAILURE: Neither known nor synthetic searches work")
        print("🔧 Search function has fundamental issues")
    
    print(f"\n📈 Known Search: {'✅' if known_works else '❌'}")
    print(f"📈 Synthetic Search: {'✅' if synthetic_works else '❌'}")
    print("\n🚀 Single synthetic search test complete!")
