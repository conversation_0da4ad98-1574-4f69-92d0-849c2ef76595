#!/usr/bin/env python3
"""
Test which route is being called
"""

import requests

def test_routes():
    """Test different routes"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Test both refresh endpoints
        print("\nTesting /cache/refresh...")
        refresh1_response = session.post(f"{base_url}/cache/refresh")
        print(f"Status: {refresh1_response.status_code}")
        if refresh1_response.status_code == 200:
            data = refresh1_response.json()
            print(f"Message: {data.get('message', 'N/A')}")
        
        print("\nTesting /refresh-cache...")
        refresh2_response = session.post(f"{base_url}/refresh-cache")
        print(f"Status: {refresh2_response.status_code}")
        if refresh2_response.status_code == 200:
            data = refresh2_response.json()
            print(f"Message: {data.get('message', 'N/A')}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_routes()
