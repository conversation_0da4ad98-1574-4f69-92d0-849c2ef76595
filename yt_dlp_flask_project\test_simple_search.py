#!/usr/bin/env python3
"""
Test simple search to verify Flask app is working
"""

import requests
import time

def test_simple_search():
    """Test simple search functionality"""
    
    print("🔍 TESTING SIMPLE SEARCH FUNCTIONALITY")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    try:
        login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'}, timeout=10)
        if login_response.status_code != 200:
            print("❌ Login failed")
            return False
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test simple searches
    test_queries = [
        "cancer",
        "Insta-Reels",
        "prevent",
        "Can-We-Prevent-Cancer"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing: '{query}'")
        
        try:
            search_response = session.post("http://127.0.0.1:8080/search", data={'query': query}, timeout=10)
            
            if search_response.status_code == 200:
                result = search_response.json()
                matches = result.get('total_matches', 0)
                search_time = result.get('search_time', 0)
                
                print(f"   ✅ Found {matches} matches in {search_time:.3f}s")
                
                if matches > 0:
                    first_match = result['results'][0]
                    filename = first_match.get('filename', 'N/A')
                    print(f"   📄 First match: {filename[:50]}...")
            else:
                print(f"   ❌ Search failed: {search_response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Search error: {e}")
    
    return True

if __name__ == "__main__":
    print("🚀 SIMPLE SEARCH TEST")
    print("Testing basic search functionality")
    
    success = test_simple_search()
    
    if success:
        print("\n✅ Simple search test completed")
    else:
        print("\n❌ Simple search test failed")
    
    print("\n🚀 Simple search test complete!")
