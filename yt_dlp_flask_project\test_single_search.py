#!/usr/bin/env python3
"""
Test single search to see logs
"""

import requests

def test_single():
    """Test single search"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Test single search
        print("\nTesting search for OCD-16673...")
        search_response = session.post(f"{base_url}/search", data={'query': 'OCD-16673'})
        
        if search_response.status_code == 200:
            data = search_response.json()
            print(f"Total matches: {data.get('total_matches', 0)}")
            print(f"Results: {data.get('results', [])}")
        else:
            print(f"Search failed: {search_response.status_code}")
            print(f"Response: {search_response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_single()
