#!/usr/bin/env python3
"""
Test the new force refresh route
"""

import requests
import time

def test_force_refresh():
    """Test the new force refresh route"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("1. Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"   Login status: {login_response.status_code}")
        
        # Test NEW force refresh route
        print("\n2. Testing NEW force refresh route...")
        print("   This should download fresh data from Google Sheets...")
        
        start_time = time.time()
        refresh_response = session.post(f"{base_url}/force-refresh-google-sheets")
        elapsed_time = time.time() - start_time
        
        print(f"   Force refresh completed in {elapsed_time:.3f}s")
        print(f"   Response status: {refresh_response.status_code}")
        
        if refresh_response.status_code == 200:
            data = refresh_response.json()
            print(f"   Success: {data.get('success', False)}")
            print(f"   Message: {data.get('message', 'N/A')}")
            print(f"   Cache size: {data.get('cache_size', 0):,}")
            print(f"   Elapsed time: {data.get('elapsed_time', 0)}s")
            print(f"   Route: {data.get('route', 'N/A')}")
            
            # Check if this is the new route
            if 'FORCE REFRESH' in data.get('message', ''):
                print("   ✅ SUCCESS: This is the NEW route with Google Sheets integration!")
                
                # Check if cache size increased
                cache_size = data.get('cache_size', 0)
                if cache_size > 22000:
                    print(f"   ✅ SUCCESS: Cache size is {cache_size:,} (expected >22,000)")
                else:
                    print(f"   ⚠️  WARNING: Cache size is {cache_size:,} (expected >22,000)")
            else:
                print("   ❌ ERROR: This is still the old route")
        else:
            print(f"   Error: {refresh_response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_force_refresh()
