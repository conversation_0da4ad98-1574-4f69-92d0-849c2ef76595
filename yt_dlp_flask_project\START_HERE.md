# 🚀 **START HERE - Archives Stems Finder Pro**

## ⚡ **Super Simple Startup (3 Steps)**

### **Step 1: Open Command Prompt**
- Press `Windows + R`
- Type `cmd` and press Enter

### **Step 2: Navigate to Project**
```bash
cd "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project"
```

### **Step 3: Start the App**
```bash
python app.py
```

**That's it!** 🎉

---

## 🌐 **Open in Browser**

Once you see this message:
```
📱 Open your browser and go to: http://127.0.0.1:5000
```

**Click this link or copy to browser:** http://127.0.0.1:5000

---

## 🔑 **Login**

1. Click **"Sign in with Google"**
2. Choose your **@sadhguru.org** email
3. Grant permissions
4. Start searching!

---

## 🔍 **Test Search**

Try searching for these:
- `aEQsJ2LVKbM`
- `VP-37160`
- `OCD-17588`
- `DgQQqwIwfj0`

---

## 🛠️ **First Time Setup (One Time Only)**

If you get "module not found" errors, install dependencies first:

```bash
pip install -r requirements.txt
```

Then run:
```bash
python app.py
```

---

## 📱 **Features to Try**

✅ **Search** - Enter video IDs, OCD numbers, VP numbers
✅ **Google Drive** - Click "📁 Open in Drive" buttons  
✅ **Manual Refresh** - Click "🔄 Manual Refresh" to update cache
✅ **Cache Stats** - View total records and last updated time

---

## 🚨 **Troubleshooting**

### **Port already in use?**
```bash
taskkill /f /im python.exe
python app.py
```

### **Can't access the app?**
- Make sure you see "Running on http://127.0.0.1:5000"
- Try both `http://127.0.0.1:5000` and `http://localhost:5000`

### **Login issues?**
- Use your **@sadhguru.org** email address
- Make sure your email is in `data/acl_file.csv`

---

## 📞 **Need Help?**

Check these files:
- `README.md` - Complete documentation
- `GOOGLE_OAUTH_SETUP.md` - OAuth setup details
- `IMPLEMENTATION_SUMMARY.md` - Technical details

---

# 🎯 **Quick Command Summary**

```bash
# Navigate to project
cd "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project"

# Install dependencies (first time only)
pip install -r requirements.txt

# Start the app
python app.py

# Open browser to: http://127.0.0.1:5000
```

**Environment variables are automatically configured - no manual setup needed!** ✨
