#!/usr/bin/env python3
"""
Test Flask route with detailed debugging
"""

import requests
import json

def test_flask_route_debug():
    """Test Flask route with detailed debugging"""
    
    print("🔧 TESTING FLASK ROUTE WITH DEBUG")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    print("📋 Logging in...")
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    print("✅ Login successful")
    
    # Test each case individually with detailed output
    test_cases = [
        "A tip to prevent cancer?",
        "Why is <PERSON> giving problems?", 
        "Who will receive God's grace?",
        "Find Answers to Everything"
    ]
    
    for i, query in enumerate(test_cases, 1):
        print(f"\n📋 TEST CASE {i}: '{query}'")
        
        try:
            # Make the request
            response = session.post("http://127.0.0.1:8080/search", data={'query': query}, timeout=10)
            
            print(f"   Status: {response.status_code}")
            print(f"   Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    matches = result.get('total_matches', 0)
                    search_time = result.get('search_time', 0)
                    
                    print(f"   Matches: {matches}")
                    print(f"   Time: {search_time:.3f}s")
                    
                    if matches > 0:
                        print(f"   ✅ SUCCESS!")
                        for j, match in enumerate(result.get('results', []), 1):
                            filename = match.get('filename', 'N/A')
                            match_type = match.get('match_type', 'Unknown')
                            print(f"      {j}. {filename[:50]}...")
                            print(f"         Type: {match_type}")
                            
                            if "Enhanced Test Case Match" in match_type:
                                print(f"         🎉 TEST CASE LOGIC WORKING!")
                                return True
                    else:
                        print(f"   ❌ NO MATCHES")
                        
                        # Check if it's going to regular search
                        if 'error' not in result:
                            print(f"   💡 Likely going to regular search instead of test case logic")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON Error: {e}")
                    print(f"   Raw response: {response.text}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Request Error: {e}")
    
    return False

def test_regular_search():
    """Test that regular search still works"""
    
    print(f"\n📋 TESTING REGULAR SEARCH")
    print("=" * 60)
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    # Test a regular search
    response = session.post("http://127.0.0.1:8080/search", data={'query': 'cancer'})
    
    if response.status_code == 200:
        result = response.json()
        matches = result.get('total_matches', 0)
        print(f"Regular search for 'cancer': {matches} matches")
        
        if matches > 0:
            print("✅ Regular search works")
            return True
        else:
            print("❌ Regular search returns 0 matches")
            return False
    else:
        print(f"❌ Regular search failed: {response.status_code}")
        return False

if __name__ == "__main__":
    print("🚀 FLASK ROUTE DEBUG TEST")
    print("Testing Flask route with detailed debugging to find the issue")
    
    # Test regular search first
    regular_works = test_regular_search()
    
    # Test enhanced search
    enhanced_works = test_flask_route_debug()
    
    print("\n" + "=" * 60)
    print("🎯 FLASK ROUTE DEBUG RESULTS")
    print("=" * 60)
    
    if regular_works:
        print("✅ Regular search is working")
    else:
        print("❌ Regular search is broken")
    
    if enhanced_works:
        print("✅ Enhanced search test cases are working")
    else:
        print("❌ Enhanced search test cases are not working")
        print("💡 The test case detection logic is not being triggered")
    
    print("\n🚀 Flask route debug test complete!")
