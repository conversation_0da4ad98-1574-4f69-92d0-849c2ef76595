#!/usr/bin/env python3
"""
Simple test for cache refresh
"""

import requests
import time

def test_simple():
    """Simple test"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Test cache refresh
        print("Testing cache refresh...")
        refresh_response = session.post(f"{base_url}/refresh-cache")
        print(f"Refresh status: {refresh_response.status_code}")
        
        if refresh_response.status_code == 200:
            data = refresh_response.json()
            print(f"Success: {data.get('success', False)}")
            print(f"Cache size: {data.get('cache_size', 0)}")
            print(f"Last updated: {data.get('last_updated', 'N/A')}")
            
            if data.get('success') and data.get('cache_size', 0) > 0:
                print("✅ CACHE REFRESH WORKING!")
                print("✅ TIMESTAMP UPDATE WORKING!")
            else:
                print("❌ Cache refresh failed")
        else:
            print(f"❌ HTTP Error: {refresh_response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_simple()
