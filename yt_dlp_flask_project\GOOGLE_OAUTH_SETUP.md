# Google OAuth2 Setup Guide

This guide will walk you through setting up Google OAuth2 authentication for the Archives Stems Finder Pro application.

## Step 1: Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click on the project dropdown at the top of the page
3. Click "New Project"
4. Enter a project name (e.g., "Archives Stems Finder")
5. Click "Create"

## Step 2: Enable Required APIs

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for and enable the following APIs:
   - **Google+ API** (for user profile information)
   - **People API** (alternative for user info)

## Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type (unless you have a Google Workspace)
3. Fill in the required information:
   - **App name**: Archives Stems Finder Pro
   - **User support email**: Your email address
   - **Developer contact information**: Your email address
4. Click "Save and Continue"
5. On the "Scopes" page, click "Save and Continue" (default scopes are sufficient)
6. On the "Test users" page, add the email addresses that should have access
7. Click "Save and Continue"

## Step 4: Create OAuth2 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application" as the application type
4. Enter a name (e.g., "Archives Stems Finder Web Client")
5. Add Authorized redirect URIs:
   - For development: `http://127.0.0.1:5000/auth/callback`
   - For production: `https://publications.isha.in/stemsfinder/auth/callback`
6. Click "Create"

## Step 5: Download Credentials

1. After creating the OAuth client, you'll see a dialog with your credentials
2. Note down the **Client ID** and **Client Secret**
3. You can also download the JSON file for backup

## Step 6: Configure the Application

### Option A: Use the Setup Script (Windows)

1. Run `setup_env.bat`
2. Enter your Client ID and Client Secret when prompted
3. Restart your command prompt
4. Run `start.bat` to start the application

### Option B: Manual Setup

Set environment variables in Windows CMD:
```cmd
set GOOGLE_CLIENT_ID=your_client_id_here
set GOOGLE_CLIENT_SECRET=your_client_secret_here
set SECRET_KEY=your_secret_key_here
```

Or set them permanently:
```cmd
setx GOOGLE_CLIENT_ID "your_client_id_here"
setx GOOGLE_CLIENT_SECRET "your_client_secret_here"
setx SECRET_KEY "your_secret_key_here"
```

## Step 7: Configure Access Control

1. Edit `data/acl_file.csv`
2. Add the email addresses that should have access to the application:
```csv
email
<EMAIL>
<EMAIL>
<EMAIL>
```

## Step 8: Test the Setup

1. Run the application: `python app.py`
2. Open your browser and go to `http://127.0.0.1:5000`
3. You should be redirected to Google login
4. After successful login, you should see the main application

## Production Deployment

For production deployment at `https://publications.isha.in/stemsfinder`:

1. Update the OAuth redirect URI in Google Cloud Console:
   - Add: `https://publications.isha.in/stemsfinder/auth/callback`
2. Configure your web server (NGINX) to proxy requests
3. Set environment variables on your production server
4. Ensure the ACL file contains the correct email addresses

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" error**:
   - Check that the redirect URI in Google Cloud Console matches exactly
   - For development: `http://127.0.0.1:5000/auth/callback`
   - For production: `https://publications.isha.in/stemsfinder/auth/callback`

2. **"access_denied" error**:
   - Check that the user's email is in `data/acl_file.csv`
   - Ensure the OAuth consent screen is properly configured

3. **"invalid_client" error**:
   - Verify that the Client ID and Client Secret are correct
   - Check that the environment variables are set properly

4. **"unauthorized_client" error**:
   - Ensure the OAuth client is configured for "Web application"
   - Check that the required APIs are enabled

### Verification Steps

1. Check environment variables:
   ```cmd
   echo %GOOGLE_CLIENT_ID%
   echo %GOOGLE_CLIENT_SECRET%
   ```

2. Verify ACL file:
   - Open `data/acl_file.csv`
   - Ensure your email address is listed

3. Check application logs:
   - Look at `app.log` for detailed error messages

## Security Notes

- Keep your Client Secret confidential
- Regularly review the list of authorized users in the ACL file
- Monitor application logs for unauthorized access attempts
- Consider implementing additional security measures for production use

## Support

If you encounter issues:
1. Check the application logs (`app.log`)
2. Verify all setup steps have been completed
3. Test with a simple OAuth flow first
4. Consult the Google OAuth2 documentation for advanced troubleshooting
