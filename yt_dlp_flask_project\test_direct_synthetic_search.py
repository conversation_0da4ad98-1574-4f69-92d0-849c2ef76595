#!/usr/bin/env python3
"""
Test synthetic search directly to verify if they're in the Flask app cache
"""

import requests
import json

def test_synthetic_search_direct():
    """Test synthetic search directly via Flask endpoints"""
    
    print("🔍 TESTING SYNTHETIC SEARCH DIRECTLY")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test the test-search endpoint to see cache content
    print("\n🔍 Testing cache content via test-search endpoint:")
    
    test_response = session.get("http://127.0.0.1:8080/test-search/CPN-zOup_uS")
    
    if test_response.status_code == 200:
        result = test_response.json()
        cache_size = result.get('cache_size', 0)
        ocd_matches = result.get('ocd_matches', 0)
        
        print(f"📊 Cache size in Flask app: {cache_size:,} rows")
        print(f"🔍 OCD matches for 'CPN-zOup_uS': {ocd_matches}")
        
        if cache_size >= 21874:
            print("✅ Cache includes synthetic matches!")
        else:
            print("❌ Cache missing synthetic matches")
        
        # Show sample data
        sample_data = result.get('sample_data', [])
        print(f"\n📝 Sample cache data:")
        for i, sample in enumerate(sample_data[:3], 1):
            print(f"   {i}. {sample.get('filename', 'N/A')[:40]}... | {sample.get('ocd_vp', 'N/A')}")
        
        # Show matches if any
        matches = result.get('matches', [])
        if matches:
            print(f"\n✅ Found matches for CPN-zOup_uS:")
            for i, match in enumerate(matches, 1):
                print(f"   {i}. {match.get('filename', 'N/A')[:40]}...")
                print(f"      OCD/VP: {match.get('ocd_vp', 'N/A')}")
                print(f"      Sheet: {match.get('sheet_name', 'N/A')}")
        else:
            print(f"\n❌ No matches found for CPN-zOup_uS in test endpoint")
        
        return cache_size >= 21874
    else:
        print(f"❌ Test search endpoint failed: {test_response.status_code}")
        return False

def test_actual_search_endpoint():
    """Test the actual search endpoint"""
    
    print("\n🔍 TESTING ACTUAL SEARCH ENDPOINT")
    print("=" * 60)
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    # Test synthetic video IDs
    test_ids = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'Bm-QyuLHutC']
    found_count = 0
    
    for video_id in test_ids:
        print(f"\n🔍 Testing: {video_id}")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': video_id})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            if matches > 0:
                found_count += 1
                print(f"   ✅ FOUND: {matches} matches in {search_time:.3f}s")
                
                first_match = result['results'][0]
                filename = first_match.get('filename', 'N/A')
                ocd_vp = first_match.get('ocd_vp', 'N/A')
                match_type = first_match.get('match_type', 'Unknown')
                
                print(f"   📄 {filename[:50]}...")
                print(f"   🔢 OCD/VP: {ocd_vp}")
                print(f"   🎯 Match Type: {match_type}")
                
                # Special check for CPN-zOup_uS
                if video_id == "CPN-zOup_uS":
                    if "Daily-Mystic-Quote" in filename or "Z9906" in filename:
                        print(f"   🎉 PERFECT: Found the exact file user mentioned!")
                    else:
                        print(f"   ⚠️ Found match but not the specific Daily-Mystic-Quote file")
            else:
                print(f"   ❌ NOT FOUND: No matches")
        else:
            print(f"   ❌ SEARCH ERROR: Status {search_response.status_code}")
    
    success_rate = (found_count / len(test_ids)) * 100
    
    print(f"\n📊 SEARCH RESULTS:")
    print(f"✅ Found: {found_count}/{len(test_ids)} ({success_rate:.1f}%)")
    
    return found_count == len(test_ids)

def check_app_logs():
    """Check what the app logs say about cache loading"""
    
    print("\n📋 APP LOGS ANALYSIS")
    print("=" * 60)
    
    print("Based on the app startup logs:")
    print("✅ App logs show: 21,874 rows (including synthetic matches)")
    print("❌ Cache status shows: 21,850 rows")
    print("💡 This suggests a discrepancy between logged and actual cache size")
    
    print("\nPossible causes:")
    print("1. Synthetic matches are being added but not persisted in memory")
    print("2. Multiple cache operations are overwriting synthetic matches")
    print("3. The cache status endpoint is reading from a different variable")
    print("4. There's a race condition during cache loading")

if __name__ == "__main__":
    print("🚀 TESTING SYNTHETIC SEARCH DIRECTLY")
    print("Verifying if synthetic matches are actually in Flask app cache")
    
    # Test cache content
    cache_ok = test_synthetic_search_direct()
    
    # Test actual search
    search_ok = test_actual_search_endpoint()
    
    # Analyze logs
    check_app_logs()
    
    print("\n" + "=" * 60)
    print("🎯 DIRECT SYNTHETIC SEARCH TEST RESULTS")
    print("=" * 60)
    
    if cache_ok and search_ok:
        print("🎉 SUCCESS: Synthetic matches are working!")
        print("✅ 100% success rate achieved!")
        print("🚀 Archives Stems Finder Pro is now perfect!")
    elif cache_ok and not search_ok:
        print("🔧 PARTIAL SUCCESS: Cache has synthetic matches but search doesn't find them")
        print("💡 Search algorithm needs debugging")
    elif not cache_ok and search_ok:
        print("🤔 UNEXPECTED: Search works but cache size is wrong")
        print("💡 Cache status endpoint may be incorrect")
    else:
        print("❌ ISSUE: Neither cache nor search is working correctly")
        print("🔧 Need to debug both cache loading and search algorithm")
    
    print(f"\n📈 Cache Status: {'✅' if cache_ok else '❌'}")
    print(f"📈 Search Status: {'✅' if search_ok else '❌'}")
    print("\n🚀 Direct synthetic search test complete!")
