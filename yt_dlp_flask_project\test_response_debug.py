#!/usr/bin/env python3
"""
Test exact response to identify which route is being called
"""

import requests
import json

def test_response():
    """Test exact response"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Test refresh endpoint with full response
        print("\nTesting /refresh-cache with full response...")
        refresh_response = session.post(f"{base_url}/refresh-cache")
        print(f"Status: {refresh_response.status_code}")
        print(f"Headers: {dict(refresh_response.headers)}")
        
        if refresh_response.status_code == 200:
            try:
                data = refresh_response.json()
                print(f"Full JSON response:")
                print(json.dumps(data, indent=2))
                
                # Check specific fields to identify the route
                message = data.get('message', '')
                if 'Google Sheets' in message:
                    print("✅ This is MY updated route!")
                else:
                    print("❌ This is the OLD route (just reloads CSV)")
                    
            except:
                print(f"Raw response: {refresh_response.text}")
        else:
            print(f"Error response: {refresh_response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_response()
