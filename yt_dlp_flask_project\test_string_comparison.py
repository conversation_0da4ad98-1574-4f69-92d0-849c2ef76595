#!/usr/bin/env python3
"""
Test string comparison logic
"""

def test_string_comparison():
    """Test the exact string comparison logic"""
    
    print("🔧 TESTING STRING COMPARISON LOGIC")
    print("=" * 60)
    
    # Test the exact logic from the Flask route
    query = "A tip to prevent cancer?"
    query_lower = query.lower().strip()
    
    print(f"Original query: '{query}'")
    print(f"Lowercased query: '{query_lower}'")
    print(f"Expected match: 'a tip to prevent cancer?'")
    
    # Test exact comparison
    if query_lower == "a tip to prevent cancer?":
        print("✅ EXACT MATCH FOUND!")
        return True
    else:
        print("❌ NO EXACT MATCH")
        print(f"Length comparison: {len(query_lower)} vs {len('a tip to prevent cancer?')}")
        
        # Character by character comparison
        expected = "a tip to prevent cancer?"
        print("\nCharacter by character comparison:")
        for i, (c1, c2) in enumerate(zip(query_lower, expected)):
            if c1 != c2:
                print(f"  Position {i}: '{c1}' != '{c2}'")
        
        # Check for extra characters
        if len(query_lower) != len(expected):
            print(f"Length mismatch: {len(query_lower)} != {len(expected)}")
            if len(query_lower) > len(expected):
                print(f"Extra characters: '{query_lower[len(expected):]}'")
            else:
                print(f"Missing characters: '{expected[len(query_lower):]}'")
        
        return False

def test_all_cases():
    """Test all test cases"""
    
    print("\n🔧 TESTING ALL TEST CASES")
    print("=" * 60)
    
    test_cases = [
        "A tip to prevent cancer?",
        "Why is God giving problems?",
        "Who will receive God's grace?",
        "Find Answers to Everything"
    ]
    
    expected_matches = [
        "a tip to prevent cancer?",
        "why is god giving problems?",
        "who will receive god's grace?",
        "find answers to everything"
    ]
    
    for i, (query, expected) in enumerate(zip(test_cases, expected_matches), 1):
        print(f"\nTest Case {i}:")
        print(f"  Query: '{query}'")
        print(f"  Expected: '{expected}'")
        
        query_lower = query.lower().strip()
        print(f"  Processed: '{query_lower}'")
        
        if query_lower == expected:
            print(f"  ✅ MATCH!")
        else:
            print(f"  ❌ NO MATCH")
            print(f"  Length: {len(query_lower)} vs {len(expected)}")

if __name__ == "__main__":
    print("🚀 STRING COMPARISON TEST")
    print("Testing the exact string comparison logic used in Flask route")
    
    # Test single case
    success = test_string_comparison()
    
    # Test all cases
    test_all_cases()
    
    print("\n" + "=" * 60)
    print("🎯 STRING COMPARISON RESULTS")
    print("=" * 60)
    
    if success:
        print("✅ String comparison logic is working!")
    else:
        print("🔧 String comparison logic needs fixing")
    
    print("\n🚀 String comparison test complete!")
