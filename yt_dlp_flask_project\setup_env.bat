@echo off
echo ========================================
echo Archives Stems Finder Pro - Environment Setup
echo ========================================
echo.
echo This script will help you set up the required environment variables.
echo You need to get these values from Google Cloud Console.
echo.

set /p CLIENT_ID="Enter your Google Client ID: "
set /p CLIENT_SECRET="Enter your Google Client Secret: "

REM Generate a random secret key
set SECRET_KEY=%RANDOM%%RANDOM%%RANDOM%%RANDOM%

echo.
echo Setting environment variables...

setx GOOGLE_CLIENT_ID "%CLIENT_ID%"
setx GOOGLE_CLIENT_SECRET "%CLIENT_SECRET%"
setx SECRET_KEY "%SECRET_KEY%"

echo.
echo ========================================
echo Environment variables set successfully!
echo ========================================
echo.
echo GOOGLE_CLIENT_ID: %CLIENT_ID%
echo GOOGLE_CLIENT_SECRET: [HIDDEN]
echo SECRET_KEY: [GENERATED]
echo.
echo Please restart your command prompt for changes to take effect.
echo Then run: start.bat
echo.
pause
