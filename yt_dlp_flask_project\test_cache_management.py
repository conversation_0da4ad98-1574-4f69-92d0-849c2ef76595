#!/usr/bin/env python3
"""
Test script for cache management functionality
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8080"
PASSWORD = "Shiva@123"

def test_cache_management():
    """Test the cache management functionality"""
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    print("🧪 Testing Cache Management Functionality")
    print("=" * 50)
    
    # Step 1: Login
    print("1. Testing login...")
    login_data = {'password': PASSWORD}
    login_response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if login_response.status_code == 200 and "Archives Stems Finder Pro" in login_response.text:
        print("✅ Login successful")
    else:
        print("❌ Login failed")
        return False
    
    # Step 2: Test cache status
    print("\n2. Testing cache status...")
    status_response = session.get(f"{BASE_URL}/cache/status")
    
    if status_response.status_code == 200:
        status_data = status_response.json()
        print("✅ Cache status retrieved successfully")
        print(f"   - Cache loaded: {status_data.get('cache_loaded')}")
        print(f"   - Cache size: {status_data.get('cache_size'):,}")
        print(f"   - Last updated: {status_data.get('last_updated')}")
        print(f"   - Next refresh: {status_data.get('next_refresh')}")
    else:
        print("❌ Failed to get cache status")
        return False
    
    # Step 3: Test homepage with cache management section
    print("\n3. Testing homepage with cache management...")
    home_response = session.get(f"{BASE_URL}/")
    
    if home_response.status_code == 200:
        home_content = home_response.text
        if "Cache Management" in home_content and "Records in Cache" in home_content:
            print("✅ Homepage shows cache management section")
        else:
            print("❌ Cache management section not found on homepage")
            return False
    else:
        print("❌ Failed to load homepage")
        return False
    
    # Step 4: Test manual cache refresh (optional - takes time)
    print("\n4. Testing manual cache refresh...")
    print("   Note: This may take some time as it downloads from Google Sheets...")
    
    try:
        refresh_response = session.post(f"{BASE_URL}/cache/refresh", timeout=60)
        
        if refresh_response.status_code == 200:
            refresh_data = refresh_response.json()
            if refresh_data.get('success'):
                print("✅ Manual cache refresh successful")
                print(f"   - Message: {refresh_data.get('message')}")
                print(f"   - Cache size: {refresh_data.get('cache_size'):,}")
            else:
                print(f"❌ Cache refresh failed: {refresh_data.get('error')}")
                return False
        else:
            print(f"❌ Cache refresh request failed with status {refresh_response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("⚠️ Cache refresh timed out (this is normal for large datasets)")
        print("   The refresh may still be running in the background")
    except Exception as e:
        print(f"❌ Cache refresh error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All cache management tests completed successfully!")
    return True

if __name__ == "__main__":
    test_cache_management()
