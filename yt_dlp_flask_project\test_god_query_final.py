#!/usr/bin/env python3
"""
Final test for "Why is <PERSON> giving problems?" query
"""

import requests
import j<PERSON>

def test_god_query_final():
    """Final test for the God query"""
    
    print("🔍 FINAL TEST: 'Why is <PERSON> giving problems?'")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # Login
        print("📋 Step 1: Login")
        login_response = session.post("http://127.0.0.1:8080/login", 
                                    data={'password': 'Shiva@123'}, 
                                    timeout=10)
        
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    try:
        # Test the query
        print("\n📋 Step 2: Search Query")
        query = "Why is <PERSON> giving problems?"
        print(f"Testing: '{query}'")
        
        search_response = session.post("http://127.0.0.1:8080/search", 
                                     data={'query': query}, 
                                     timeout=15)
        
        print(f"Response Status: {search_response.status_code}")
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"Total Matches: {matches}")
            print(f"Search Time: {search_time:.3f}s")
            
            if matches > 0:
                print("\n🎉 SUCCESS! Found matches:")
                
                for i, match in enumerate(result.get('results', []), 1):
                    filename = match.get('filename', 'N/A')
                    match_type = match.get('match_type', 'Unknown')
                    matched_var = match.get('matched_variation', 'N/A')
                    score = match.get('score', 0)
                    
                    print(f"\n   Match {i}:")
                    print(f"   📄 Filename: {filename}")
                    print(f"   🎯 Match Type: {match_type}")
                    print(f"   🔧 Matched: '{matched_var}'")
                    print(f"   📊 Score: {score}")
                    
                    # Check if this is the expected file
                    if "Why-Is-God-Giving-Problems" in filename:
                        print(f"   ✅ THIS IS THE EXPECTED TARGET FILE!")
                        print(f"   🎉 QUERY WORKING PERFECTLY!")
                        return True
                
                print("\n⚠️ Found matches but not the expected target file")
                return False
            else:
                print("\n❌ No matches found")
                return False
        else:
            print(f"❌ Search failed: {search_response.status_code}")
            print(f"Response: {search_response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Search error: {e}")
        return False

def test_processing_manually():
    """Test the processing steps manually to verify algorithm"""
    
    print(f"\n📋 Step 3: Manual Algorithm Verification")
    print("=" * 60)
    
    import re
    
    query = "Why is God giving problems?"
    print(f"Original Query: '{query}'")
    
    # Step 1: Remove special characters
    clean_query = re.sub(r'[^a-zA-Z0-9\s]', '', query)
    print(f"Step 1 - Remove special chars: '{clean_query}'")
    
    # Step 2: Replace spaces with hyphens
    processed_query = re.sub(r'\s+', '-', clean_query.strip())
    print(f"Step 2 - Replace spaces: '{processed_query}'")
    
    # Step 3: Check what should match
    target_filename = "Insta-Reels_Why-Is-God-Giving-Problems_15-Apr-2025_Tamil_55Secs_Stems"
    print(f"Target filename: '{target_filename}'")
    
    # Check if processed query should match target
    if processed_query.lower() in target_filename.lower():
        print(f"✅ '{processed_query}' should match target filename")
    else:
        print(f"❌ '{processed_query}' won't match target filename")
        
        # Try partial matching
        words = processed_query.split('-')
        if len(words) > 2:
            first_part = '-'.join(words[:3])
            second_part = '-'.join(words[3:]) if len(words) > 3 else ''
            
            print(f"Trying partial match:")
            print(f"   First part: '{first_part}'")
            print(f"   Second part: '{second_part}'")
            
            if first_part.lower() in target_filename.lower():
                print(f"   ✅ First part should match")
            if second_part and second_part.lower() in target_filename.lower():
                print(f"   ✅ Second part should match")

if __name__ == "__main__":
    print("🚀 FINAL TEST FOR 'Why is God giving problems?'")
    print("Testing the JavaScript-style algorithm implementation")
    
    # Test the query
    success = test_god_query_final()
    
    # Test processing manually
    test_processing_manually()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 60)
    
    if success:
        print("🎉 PERFECT SUCCESS!")
        print("✅ 'Why is God giving problems?' is working!")
        print("✅ JavaScript-style algorithm implemented correctly!")
        print("✅ Expected target file found!")
        print("🚀 Query processing ready for production!")
    else:
        print("🔧 STILL NEEDS WORK")
        print("❌ Query not returning expected results")
        print("💡 Need to debug the algorithm implementation")
    
    print("\n🚀 Final test complete!")
