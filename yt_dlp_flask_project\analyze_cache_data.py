#!/usr/bin/env python3
"""
Analyze cache data to understand why video IDs are not found
"""

import pandas as pd
import re

def analyze_cache_data():
    """Analyze the cache file to understand data structure"""
    
    print("🔍 ANALYZING CACHE DATA STRUCTURE")
    print("=" * 60)
    
    # Load cache file
    try:
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded cache: {len(df):,} rows")
    except Exception as e:
        print(f"❌ Failed to load cache: {e}")
        return
    
    # Analyze columns
    print(f"\n📊 Columns: {list(df.columns)}")
    print(f"📊 Data types: {df.dtypes.to_dict()}")
    
    # Analyze video_id column
    print(f"\n🎥 VIDEO ID ANALYSIS:")
    video_ids = df['video_id'].dropna()
    print(f"   • Total video IDs: {len(video_ids):,}")
    print(f"   • Unique video IDs: {video_ids.nunique():,}")
    print(f"   • Empty/NaN video IDs: {df['video_id'].isna().sum():,}")
    
    # Sample video IDs
    print(f"\n📝 Sample video IDs:")
    for i, vid in enumerate(video_ids.head(10)):
        print(f"   {i+1:2d}. {vid}")
    
    # Analyze video ID patterns
    print(f"\n🔍 VIDEO ID PATTERNS:")
    
    # Count different prefixes
    prefixes = {}
    for vid in video_ids:
        if pd.notna(vid):
            vid_str = str(vid).strip()
            if len(vid_str) > 2:
                prefix = vid_str[:2]
                prefixes[prefix] = prefixes.get(prefix, 0) + 1
    
    # Show top prefixes
    sorted_prefixes = sorted(prefixes.items(), key=lambda x: x[1], reverse=True)
    print("   Top video ID prefixes:")
    for prefix, count in sorted_prefixes[:15]:
        print(f"     {prefix}: {count:,} video IDs")
    
    # Look for specific patterns mentioned by user
    print(f"\n🎯 SEARCHING FOR USER'S VIDEO ID PATTERNS:")
    
    user_patterns = ['CPN', 'CO2', 'CO0', 'COz', 'COw', 'COx', 'COu', 'COv', 'Bm-', 'Bm7', 'Bkx', 'Bi9']
    
    for pattern in user_patterns:
        matches = video_ids[video_ids.str.contains(pattern, na=False, case=False)]
        print(f"   {pattern}: {len(matches)} matches")
        if len(matches) > 0:
            print(f"      Examples: {list(matches.head(3))}")
    
    # Analyze sheet distribution
    print(f"\n📋 SHEET DISTRIBUTION:")
    sheet_counts = df['sheet_name'].value_counts()
    for sheet, count in sheet_counts.items():
        print(f"   • {sheet}: {count:,} records")
    
    # Look for the specific video ID mentioned by user
    print(f"\n🔍 SEARCHING FOR SPECIFIC USER EXAMPLES:")
    
    user_examples = ['CPN-zOup_uS', 'Daily-Mystic-Quote', 'Z9906']
    
    for example in user_examples:
        # Search in all text fields
        filename_matches = df[df['filename'].str.contains(example, na=False, case=False)]
        video_id_matches = df[df['video_id'].str.contains(example, na=False, case=False)]
        ocd_vp_matches = df[df['ocd_vp'].str.contains(example, na=False, case=False)]
        
        total_matches = len(filename_matches) + len(video_id_matches) + len(ocd_vp_matches)
        
        print(f"   '{example}':")
        print(f"     Filename matches: {len(filename_matches)}")
        print(f"     Video ID matches: {len(video_id_matches)}")
        print(f"     OCD/VP matches: {len(ocd_vp_matches)}")
        print(f"     Total matches: {total_matches}")
        
        if total_matches > 0:
            # Show the matches
            all_matches = pd.concat([filename_matches, video_id_matches, ocd_vp_matches]).drop_duplicates()
            for _, row in all_matches.head(3).iterrows():
                print(f"       → {row['filename'][:50]}... | {row['ocd_vp']} | {row['video_id']}")
    
    # Check for video IDs with special characters or formatting
    print(f"\n🔧 VIDEO ID FORMATTING ANALYSIS:")
    
    # Count video IDs with special characters
    special_chars = 0
    multiline_ids = 0
    
    for vid in video_ids:
        if pd.notna(vid):
            vid_str = str(vid)
            if '\n' in vid_str or '\\n' in vid_str:
                multiline_ids += 1
            if any(char in vid_str for char in ['-', '_', ' ', '\n']):
                special_chars += 1
    
    print(f"   • Video IDs with special characters: {special_chars:,}")
    print(f"   • Video IDs with newlines: {multiline_ids:,}")
    
    # Show examples of complex video IDs
    print(f"\n📝 Examples of complex video IDs:")
    complex_ids = video_ids[video_ids.str.contains(r'[\n\r]', na=False)]
    for i, vid in enumerate(complex_ids.head(5)):
        print(f"   {i+1}. {repr(vid)}")

if __name__ == "__main__":
    analyze_cache_data()
