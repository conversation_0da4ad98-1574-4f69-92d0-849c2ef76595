#!/usr/bin/env python3
"""
Test debug route to identify which app is running
"""

import requests

def test_debug():
    """Test debug route"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Test debug route
        print("\nTesting debug route...")
        debug_response = session.get(f"{base_url}/debug-app-version")
        print(f"Debug route status: {debug_response.status_code}")
        
        if debug_response.status_code == 200:
            data = debug_response.json()
            print(f"App file: {data.get('app_file', 'N/A')}")
            print(f"App name: {data.get('app_name', 'N/A')}")
            print(f"App version: {data.get('app_version', 'N/A')}")
            print(f"Has Google Sheets: {data.get('has_google_sheets', False)}")
            print(f"Debug message: {data.get('debug_message', 'N/A')}")
            
            if data.get('has_google_sheets'):
                print("✅ SUCCESS: This is the UPDATED app with Google Sheets!")
            else:
                print("❌ ERROR: This is the OLD app without Google Sheets!")
        else:
            print(f"Debug route failed: {debug_response.text}")
            print("❌ ERROR: Debug route doesn't exist - wrong app is running!")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_debug()
