#!/usr/bin/env python3
"""
Test specific URL that's causing issues
"""

import pandas as pd
import time
import re
from urllib.parse import urlparse, parse_qs

def extract_video_id_from_url(url):
    """Extract video ID from various social media URLs"""
    # YouTube patterns
    youtube_patterns = [
        r'youtube\.com/watch\?v=([A-Za-z0-9_-]+)',
        r'youtu\.be/([A-Za-z0-9_-]+)',
        r'youtube\.com/embed/([A-Za-z0-9_-]+)',
        r'youtube\.com/shorts/([A-Za-z0-9_-]+)'
    ]
    
    for pattern in youtube_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None

def detect_search_type(query):
    """Detect search type"""
    original_query = query.strip()
    query = query.strip().upper()
    
    # Check for URLs first
    if any(domain in original_query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
        video_id = extract_video_id_from_url(original_query)
        if video_id:
            return 'video_id'
        return 'url'
    
    # Check for OCD/VP numbers
    if query.startswith('OCD-') or query.startswith('VP-') or (query.startswith(('OCD', 'VP')) and any(c.isdigit() for c in query)):
        return 'ocd_number'
    
    # Check for Video ID (11 characters)
    if len(query) == 11 and query.replace('_', '').replace('-', '').isalnum():
        return 'video_id'
    
    return 'title_search'

def test_specific_url():
    """Test the specific URL that's causing issues"""
    test_url = "https://youtu.be/rbYdXbEVm6E"
    
    print(f"🔍 Testing URL: {test_url}")
    print("=" * 60)
    
    # Test video ID extraction
    video_id = extract_video_id_from_url(test_url)
    print(f"📹 Extracted Video ID: {video_id}")
    
    # Test search type detection
    search_type = detect_search_type(test_url)
    print(f"🔍 Detected Search Type: {search_type}")
    
    # Load cache and test search
    try:
        cache_file = 'archives_cache.csv'
        df = pd.read_csv(cache_file)
        print(f"📂 Cache loaded: {len(df)} rows")
        
        # Test search with extracted video ID
        if video_id:
            print(f"\n🔍 Searching for video ID: {video_id}")
            
            # Search in video_id column
            mask1 = df['video_id'].astype(str).str.contains(video_id, na=False, regex=False)
            results1 = df[mask1]
            print(f"   video_id column matches: {len(results1)}")
            
            # Search in video_ids column (if exists)
            if 'video_ids' in df.columns:
                mask2 = df['video_ids'].astype(str).str.contains(video_id, na=False, regex=False)
                results2 = df[mask2]
                print(f"   video_ids column matches: {len(results2)}")
            else:
                print(f"   video_ids column: NOT FOUND")
                results2 = pd.DataFrame()
            
            # Combined results
            combined_mask = mask1
            if 'video_ids' in df.columns:
                combined_mask = mask1 | mask2
            
            final_results = df[combined_mask]
            print(f"   Total matches: {len(final_results)}")
            
            if len(final_results) > 0:
                print(f"\n✅ FOUND MATCHES:")
                for idx, row in final_results.iterrows():
                    print(f"   📁 {row['filename']}")
                    print(f"   ⏱️  {row['duration']}")
                    print(f"   🆔 OCD/VP: {row['ocd_vp']}")
                    print(f"   📹 Video ID: {row['video_id']}")
                    if 'video_ids' in row:
                        print(f"   📹 Video IDs: {row['video_ids']}")
                    print(f"   📊 Sheet: {row.get('sheet_name', 'Unknown')}")
                    print()
            else:
                print(f"\n❌ NO MATCHES FOUND")
                
                # Debug: Check what video IDs exist
                print(f"\n🔍 DEBUG: Checking similar video IDs...")
                video_ids_sample = df['video_id'].dropna().astype(str).head(10)
                print(f"Sample video IDs in cache:")
                for vid in video_ids_sample:
                    if vid != 'nan':
                        print(f"   {vid}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_specific_url()
