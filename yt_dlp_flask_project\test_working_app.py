#!/usr/bin/env python3
"""
Test the working app with missing IDs
"""

import requests
import time

def test_working_app():
    """Test the working app with missing IDs"""
    base_url = "http://127.0.0.1:8080"
    
    # List of missing IDs to test
    missing_ids = [
        "OCD-16673", "OCD-16668", "OCD-16698", "OCD-15138", "OCD-16697", 
        "OCD-16677", "OCD-16693", "VP-9992", "VP-8882", "VP-5552", 
        "VP-3332", "VP-1112", "VP-0002", "VP-42222", "C9SQ3zxyYO9", 
        "DKrifQ0T_8Z", "A75b1NKWCC4"
    ]
    
    session = requests.Session()
    
    try:
        # Login
        print("1. Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"   Login status: {login_response.status_code}")
        
        # Check app status
        print("\n2. Checking app status...")
        status_response = session.get(f"{base_url}/")
        if status_response.status_code == 200:
            data = status_response.json()
            print(f"   App: {data.get('app_name', 'N/A')} v{data.get('app_version', 'N/A')}")
            print(f"   Cache size: {data.get('cache_size', 0):,} records")
            print(f"   Last updated: {data.get('last_updated', 'N/A')}")
            print(f"   Message: {data.get('message', 'N/A')}")
        
        # Test cache status
        print("\n3. Testing cache status...")
        cache_response = session.get(f"{base_url}/cache/status")
        if cache_response.status_code == 200:
            cache_data = cache_response.json()
            print(f"   Cache loaded: {cache_data.get('cache_loaded', False)}")
            print(f"   Cache size: {cache_data.get('cache_size', 0):,} records")
            print(f"   App version: {cache_data.get('app_version', 'N/A')}")
        
        # Test missing IDs
        print(f"\n4. Testing {len(missing_ids)} missing IDs...")
        found_count = 0
        missing_count = 0
        
        for i, test_id in enumerate(missing_ids, 1):
            print(f"\n   [{i}/{len(missing_ids)}] Testing: {test_id}")
            
            start_time = time.time()
            search_response = session.post(f"{base_url}/search", data={'query': test_id})
            elapsed_time = time.time() - start_time
            
            if search_response.status_code == 200:
                result_data = search_response.json()
                total_matches = result_data.get('total_matches', 0)
                
                if total_matches > 0:
                    print(f"      ✅ FOUND: {total_matches} matches in {elapsed_time:.3f}s")
                    found_count += 1
                    # Show first result
                    if result_data.get('results'):
                        first_result = result_data['results'][0]
                        print(f"         File: {first_result.get('filename', 'N/A')[:50]}...")
                        print(f"         OCD/VP: {first_result.get('ocd_vp', 'N/A')}")
                        print(f"         Sheet: {first_result.get('sheet_name', 'N/A')}")
                else:
                    print(f"      ❌ NOT FOUND in {elapsed_time:.3f}s")
                    missing_count += 1
            else:
                print(f"      ❌ Search error: {search_response.status_code}")
                missing_count += 1
        
        # Summary
        print(f"\n5. FINAL SUMMARY:")
        print(f"   ✅ Found: {found_count}/{len(missing_ids)} IDs")
        print(f"   ❌ Missing: {missing_count}/{len(missing_ids)} IDs")
        print(f"   📊 Success Rate: {(found_count/len(missing_ids)*100):.1f}%")
        
        if found_count == len(missing_ids):
            print(f"\n🎉 SUCCESS: All {len(missing_ids)} IDs found! Cache refresh is working perfectly!")
        elif found_count > 0:
            print(f"\n✅ PARTIAL SUCCESS: {found_count} IDs found. Cache refresh is working!")
        else:
            print(f"\n❌ ISSUE: No IDs found. There may still be a problem.")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_working_app()
