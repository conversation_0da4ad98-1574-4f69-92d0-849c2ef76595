#!/usr/bin/env python3
"""
Final test for cache refresh with Google Sheets integration
"""

import requests
import time

def test_cache_refresh_final():
    """Test cache refresh and missing IDs"""
    base_url = "http://127.0.0.1:8080"
    
    # List of missing IDs to test
    missing_ids = [
        "OCD-16673", "OCD-16668", "OCD-16698", "OCD-15138", "OCD-16697", 
        "OCD-16677", "OCD-16693", "VP-9992", "VP-8882", "VP-5552", 
        "VP-3332", "VP-1112", "VP-0002", "VP-42222", "C9SQ3zxyYO9", 
        "DKrifQ0T_8Z", "A75b1NKWCC4"
    ]
    
    session = requests.Session()
    
    try:
        # Login
        print("1. Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': '<PERSON>@123'})
        print(f"   Login status: {login_response.status_code}")
        
        # Check cache status BEFORE refresh
        print("\n2. Checking cache status BEFORE refresh...")
        status_response = session.get(f"{base_url}/cache/status")
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"   Cache loaded: {status_data.get('cache_loaded', False)}")
            print(f"   Cache size BEFORE: {status_data.get('cache_size', 0):,} records")
            print(f"   Last updated BEFORE: {status_data.get('last_updated', 'N/A')}")
            old_cache_size = status_data.get('cache_size', 0)
            old_last_updated = status_data.get('last_updated', 'N/A')
        
        # Test cache refresh from Google Sheets
        print("\n3. Testing cache refresh from Google Sheets...")
        print("   This will download fresh data from all 8 Google Sheets...")
        
        start_time = time.time()
        refresh_response = session.post(f"{base_url}/refresh-cache")
        elapsed_time = time.time() - start_time
        
        if refresh_response.status_code == 200:
            refresh_data = refresh_response.json()
            print(f"   SUCCESS: Cache refresh completed!")
            print(f"   Success: {refresh_data.get('success', False)}")
            print(f"   New cache size: {refresh_data.get('cache_size', 0):,} records")
            print(f"   Refresh time: {elapsed_time:.3f}s")
        else:
            print(f"   FAILED: Cache refresh failed: {refresh_response.status_code}")
            print(f"   Response: {refresh_response.text}")
            return
        
        # Check cache status AFTER refresh
        print("\n4. Checking cache status AFTER refresh...")
        status_response2 = session.get(f"{base_url}/cache/status")
        if status_response2.status_code == 200:
            status_data2 = status_response2.json()
            print(f"   Cache loaded: {status_data2.get('cache_loaded', False)}")
            print(f"   Cache size AFTER: {status_data2.get('cache_size', 0):,} records")
            print(f"   Last updated AFTER: {status_data2.get('last_updated', 'N/A')}")
            
            new_cache_size = status_data2.get('cache_size', 0)
            new_last_updated = status_data2.get('last_updated', 'N/A')
            
            # Check improvements
            if new_cache_size > old_cache_size:
                print(f"   IMPROVEMENT: +{new_cache_size - old_cache_size:,} new records!")
            if new_last_updated != old_last_updated:
                print(f"   IMPROVEMENT: Timestamp updated successfully!")
        
        # Test missing IDs (first 5 for speed)
        print(f"\n5. Testing first 5 missing IDs...")
        found_count = 0
        
        for i, test_id in enumerate(missing_ids[:5], 1):
            print(f"   [{i}/5] Testing: {test_id}")
            
            search_response = session.post(f"{base_url}/search", data={'query': test_id})
            
            if search_response.status_code == 200:
                result_data = search_response.json()
                total_matches = result_data.get('total_matches', 0)
                
                if total_matches > 0:
                    print(f"        FOUND: {total_matches} matches")
                    found_count += 1
                    # Show first result
                    if result_data.get('results'):
                        first_result = result_data['results'][0]
                        print(f"        File: {first_result.get('filename', 'N/A')[:50]}...")
                        print(f"        OCD/VP: {first_result.get('ocd_vp', 'N/A')}")
                else:
                    print(f"        NOT FOUND")
            else:
                print(f"        Search error: {search_response.status_code}")
        
        # Summary
        print(f"\n6. SUMMARY:")
        print(f"   Found: {found_count}/5 test IDs")
        print(f"   Success Rate: {(found_count/5*100):.1f}%")
        
        if found_count == 5:
            print(f"   SUCCESS: All test IDs found! Cache refresh is working!")
        elif found_count > 0:
            print(f"   PARTIAL: Some IDs found. Cache refresh is working but may need more time.")
        else:
            print(f"   ISSUE: No IDs found. Cache refresh may have failed.")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_cache_refresh_final()
