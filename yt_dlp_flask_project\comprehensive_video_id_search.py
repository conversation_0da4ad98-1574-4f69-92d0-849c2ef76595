#!/usr/bin/env python3
"""
Comprehensive search for missing video IDs across ALL possible locations
"""

import gspread
from google.oauth2.service_account import Credentials
import pandas as pd
import json
import time

# Configuration
GOOGLE_SHEET_ID = "1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE"

# Missing video IDs that need to be found
MISSING_VIDEO_IDS = [
    "CPN-zOup_uS",
    "CO2DQGZgUQL", 
    "CO0NM2AIvRx",
    "COzgvYcgBAx",
    "COw40WfBzWZ",
    "COxhRudAD4k",
    "COuaS9mBRjR",
    "COvKclMopkO",
    "Bm-QyuLHutC",
    "Bm7zt7_nWN4",
    "BkxrLGZHN1r",
    "Bi9PLrYH3Ef",
    "rbYdXbEVm6E",
    "ChTnwpkCMhg",
    "KX_pnMG-4RE",
    "IH23o77OZXw",
    "FBYoZ-FgC84",
    "g7SvHaSzz9A",
    "5Rr13rAlifM",
    "-tLR-BztVKI",
    "8oSuwfAfWh4",
    "W2qSmUq3YQk",
    "yBJqCW6bxsY",
    "H4qQ7MHACbw"
]

def get_google_sheets_client():
    """Get authenticated Google Sheets client"""
    try:
        # Use service account credentials
        scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
        
        # Try to load credentials
        try:
            creds = Credentials.from_service_account_file('service_account.json', scopes=scope)
        except:
            print("❌ service_account.json not found. Using default credentials.")
            return None
            
        client = gspread.authorize(creds)
        return client
    except Exception as e:
        print(f"❌ Google Sheets client failed: {e}")
        return None

def search_all_sheets_and_columns():
    """Search for missing video IDs in ALL sheets and ALL columns"""
    
    print("🔍 COMPREHENSIVE VIDEO ID SEARCH ACROSS ALL SHEETS")
    print("=" * 80)
    
    client = get_google_sheets_client()
    if not client:
        print("❌ Cannot connect to Google Sheets")
        return
    
    try:
        spreadsheet = client.open_by_key(GOOGLE_SHEET_ID)
        all_worksheets = spreadsheet.worksheets()
        
        print(f"📊 Found {len(all_worksheets)} worksheets in the spreadsheet:")
        for i, ws in enumerate(all_worksheets, 1):
            print(f"   {i:2d}. {ws.title}")
        
        print(f"\n🎯 Searching for {len(MISSING_VIDEO_IDS)} missing video IDs...")
        
        found_videos = {}
        total_searches = 0
        
        for sheet_idx, worksheet in enumerate(all_worksheets, 1):
            sheet_name = worksheet.title
            print(f"\n📋 [{sheet_idx}/{len(all_worksheets)}] Searching in: {sheet_name}")
            
            try:
                # Get all data from the sheet
                all_values = worksheet.get_all_values()
                if not all_values:
                    print(f"   ⚠️ No data in {sheet_name}")
                    continue
                
                print(f"   📊 Sheet has {len(all_values)} rows and {len(all_values[0]) if all_values else 0} columns")
                
                # Convert to DataFrame for easier searching
                df = pd.DataFrame(all_values[1:], columns=all_values[0] if all_values else [])
                
                # Search in ALL columns for each missing video ID
                for video_id in MISSING_VIDEO_IDS:
                    if video_id in found_videos:
                        continue  # Already found
                    
                    total_searches += 1
                    
                    # Search in every cell of the sheet
                    found_in_sheet = False
                    for col_idx, column in enumerate(df.columns):
                        # Search in this column
                        matches = df[df[column].astype(str).str.contains(video_id, na=False, case=False)]
                        
                        if len(matches) > 0:
                            found_in_sheet = True
                            if video_id not in found_videos:
                                found_videos[video_id] = []
                            
                            for _, match in matches.iterrows():
                                found_videos[video_id].append({
                                    'sheet': sheet_name,
                                    'column': column,
                                    'row_data': match.to_dict(),
                                    'match_column_value': str(match[column])
                                })
                            
                            print(f"   ✅ Found '{video_id}' in column '{column}' ({len(matches)} matches)")
                    
                    if not found_in_sheet:
                        # Try fuzzy matching for this video ID
                        for col_idx, column in enumerate(df.columns):
                            # Remove special characters and try again
                            clean_video_id = video_id.replace('-', '').replace('_', '')
                            fuzzy_matches = df[df[column].astype(str).str.replace('-', '').str.replace('_', '').str.contains(clean_video_id, na=False, case=False)]
                            
                            if len(fuzzy_matches) > 0:
                                found_in_sheet = True
                                if video_id not in found_videos:
                                    found_videos[video_id] = []
                                
                                for _, match in fuzzy_matches.iterrows():
                                    found_videos[video_id].append({
                                        'sheet': sheet_name,
                                        'column': column,
                                        'row_data': match.to_dict(),
                                        'match_column_value': str(match[column]),
                                        'match_type': 'fuzzy'
                                    })
                                
                                print(f"   🔍 Fuzzy match for '{video_id}' in column '{column}' ({len(fuzzy_matches)} matches)")
                
            except Exception as e:
                print(f"   ❌ Error searching {sheet_name}: {e}")
                continue
        
        # Report results
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE SEARCH RESULTS")
        print("=" * 80)
        
        found_count = len(found_videos)
        missing_count = len(MISSING_VIDEO_IDS) - found_count
        
        print(f"✅ Found: {found_count}/{len(MISSING_VIDEO_IDS)} video IDs ({found_count/len(MISSING_VIDEO_IDS)*100:.1f}%)")
        print(f"❌ Still Missing: {missing_count}/{len(MISSING_VIDEO_IDS)} video IDs")
        print(f"🔍 Total Searches Performed: {total_searches}")
        
        if found_videos:
            print(f"\n🎯 DETAILED FINDINGS:")
            for video_id, locations in found_videos.items():
                print(f"\n📹 {video_id}:")
                for i, location in enumerate(locations, 1):
                    sheet = location['sheet']
                    column = location['column']
                    value = location['match_column_value']
                    match_type = location.get('match_type', 'exact')
                    
                    print(f"   {i}. Sheet: {sheet}")
                    print(f"      Column: {column}")
                    print(f"      Value: {value[:100]}...")
                    print(f"      Match Type: {match_type}")
                    
                    # Show some context from the row
                    row_data = location['row_data']
                    if 'filename' in row_data or any('filename' in k.lower() for k in row_data.keys()):
                        filename_key = next((k for k in row_data.keys() if 'filename' in k.lower()), None)
                        if filename_key:
                            print(f"      Context: {row_data[filename_key][:50]}...")
        
        # List still missing video IDs
        still_missing = [vid for vid in MISSING_VIDEO_IDS if vid not in found_videos]
        if still_missing:
            print(f"\n❌ STILL MISSING VIDEO IDs:")
            for i, video_id in enumerate(still_missing, 1):
                print(f"   {i:2d}. {video_id}")
        
        # Save results to file for analysis
        results = {
            'search_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'total_sheets_searched': len(all_worksheets),
            'total_video_ids_searched': len(MISSING_VIDEO_IDS),
            'found_video_ids': found_videos,
            'still_missing': still_missing,
            'success_rate': found_count/len(MISSING_VIDEO_IDS)*100
        }
        
        with open('comprehensive_search_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: comprehensive_search_results.json")
        
        return found_videos
        
    except Exception as e:
        print(f"❌ Error accessing spreadsheet: {e}")
        return {}

if __name__ == "__main__":
    print("🚀 STARTING COMPREHENSIVE VIDEO ID SEARCH")
    print("Searching ALL sheets and ALL columns for missing video IDs")
    print("This may take several minutes...")
    
    found_videos = search_all_sheets_and_columns()
    
    if len(found_videos) == len(MISSING_VIDEO_IDS):
        print("\n🎉 SUCCESS: Found ALL missing video IDs!")
        print("✅ 100% success rate achieved!")
    elif len(found_videos) > 0:
        print(f"\n👍 PARTIAL SUCCESS: Found {len(found_videos)} out of {len(MISSING_VIDEO_IDS)} video IDs")
        print("🔧 Will update search algorithm with new findings")
    else:
        print("\n⚠️ NO NEW FINDINGS: Video IDs may not exist in current Google Sheets")
        print("💡 May need to check different data sources or verify video ID formats")
