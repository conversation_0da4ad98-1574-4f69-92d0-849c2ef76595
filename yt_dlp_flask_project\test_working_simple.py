#!/usr/bin/env python3
"""
Simple test for the working app
"""

import requests
import time

def test_simple():
    """Simple test"""
    base_url = "http://127.0.0.1:8080"
    
    # List of missing IDs to test (first 5 for speed)
    test_ids = [
        "OCD-16673", "OCD-16668", "OCD-16698", "OCD-15138", "OCD-16697"
    ]
    
    session = requests.Session()
    
    try:
        # Login
        print("1. Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"   Login status: {login_response.status_code}")
        
        # Test cache status
        print("\n2. Testing cache status...")
        cache_response = session.get(f"{base_url}/cache/status")
        if cache_response.status_code == 200:
            cache_data = cache_response.json()
            print(f"   Cache loaded: {cache_data.get('cache_loaded', False)}")
            print(f"   Cache size: {cache_data.get('cache_size', 0):,} records")
            print(f"   App version: {cache_data.get('app_version', 'N/A')}")
        
        # Test missing IDs
        print(f"\n3. Testing {len(test_ids)} missing IDs...")
        found_count = 0
        
        for i, test_id in enumerate(test_ids, 1):
            print(f"\n   [{i}/{len(test_ids)}] Testing: {test_id}")
            
            start_time = time.time()
            search_response = session.post(f"{base_url}/search", data={'query': test_id})
            elapsed_time = time.time() - start_time
            
            if search_response.status_code == 200:
                result_data = search_response.json()
                total_matches = result_data.get('total_matches', 0)
                
                if total_matches > 0:
                    print(f"      ✅ FOUND: {total_matches} matches in {elapsed_time:.3f}s")
                    found_count += 1
                    # Show first result
                    if result_data.get('results'):
                        first_result = result_data['results'][0]
                        print(f"         File: {first_result.get('filename', 'N/A')[:50]}...")
                        print(f"         OCD/VP: {first_result.get('ocd_vp', 'N/A')}")
                        print(f"         Sheet: {first_result.get('sheet_name', 'N/A')}")
                else:
                    print(f"      ❌ NOT FOUND in {elapsed_time:.3f}s")
            else:
                print(f"      ❌ Search error: {search_response.status_code}")
        
        # Summary
        print(f"\n4. SUMMARY:")
        print(f"   Found: {found_count}/{len(test_ids)} IDs")
        print(f"   Success Rate: {(found_count/len(test_ids)*100):.1f}%")
        
        if found_count == len(test_ids):
            print(f"\n🎉 SUCCESS: All {len(test_ids)} test IDs found!")
        elif found_count > 0:
            print(f"\n✅ PARTIAL: {found_count} IDs found - cache refresh working!")
        else:
            print(f"\n❌ ISSUE: No IDs found")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_simple()
