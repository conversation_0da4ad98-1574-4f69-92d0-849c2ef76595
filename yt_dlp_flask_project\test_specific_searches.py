#!/usr/bin/env python3
"""
Test specific OCD numbers and video IDs that should return exact matches
"""

import pandas as pd
import requests
import time

def test_specific_searches():
    """Test the specific searches provided by the user"""
    
    print("🔍 Testing Specific OCD Numbers and Video IDs")
    print("=" * 80)
    
    # Load cache directly to test
    try:
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded cache: {len(df):,} rows")
    except Exception as e:
        print(f"❌ Failed to load cache: {e}")
        return
    
    # Test cases from user
    test_cases = [
        # OCD Numbers with expected results
        {
            'query': 'OCD-15170',
            'expected': 'Insta-Reels_The-Only-Problem-In-The-World_20-May-2025_English_52Secs_Stems'
        },
        {
            'query': 'OCD-16908', 
            'expected': 'Promo_Save-Soil-Donar-Video_19-May-2025_Tamil_03Mins-53Secs_Stems'
        },
        {
            'query': 'OCD-17266',
            'expected': 'Insta-Reels_Save-Soil-<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON>ya<PERSON>ayam-Nagalingam_19-May-2025_Tamil_01Min-04Secs_Stems'
        },
        {
            'query': 'OCD-15307',
            'expected': 'Sharings_April-2025-<PERSON>nga_21-May-2025_Tamil_01Min-50Secs_Stems'
        },
        {
            'query': 'OCD-17269',
            'expected': 'Promo_Save-Soil-Thaimannu-Kappum-Vyavasayam-Sundararajan_19-May-2025_Tamil_01Min-01Sec_Stems'
        },
        {
            'query': 'OCD-15343',
            'expected': 'Insta-Reels_Unlock-The-Miracle-Of-Mind_23-May-2025_English_45Secs_Stems'
        },
        {
            'query': 'OCD-15901',
            'expected': 'Insta-Reels_Aum-Namah-Shivaya-Can-Take-You-Beyond-Birth-And-Death_21-May-2025_English_01Min_Stems'
        },
        
        # Video IDs with expected results
        {
            'query': 'oWBTMp35RfA',
            'expected': 'Insta-Reels_Bhairavi-Sadhana-Experience-A-Flood-Of-Devotion_17-Dec-2024_English_01Min-08Secs_Stems'
        },
        {
            'query': 'dDLvjAk50gc',
            'expected': 'Insta-Reels_Real-Love-Starts-With-Responsibility_25-Jun-2024_English_50Secs_Stems'
        },
        {
            'query': 'W8L48CJAygQ',
            'expected': 'Insta-Reels_Dont-Live-on-Autopilot_28-Jun-2024_English_42Secs_Stems'
        },
        {
            'query': 'H4qQ7MHACbw',
            'expected': 'Q-And-A_How-Do-I-Control-Excitement_17-Aug-2010_English_05Mins-03Secs_MOV'
        },
        {
            'query': 'DEJsoRSTr3o',
            'expected': 'Insta-Reels_Collective-Makara-Sankranti-Offerings-At-Sadhguru-Sannidhi-Bengaluru_26-Dec-2024_English_49Secs_Stems'
        },
        {
            'query': 'DEHw7rhTEe_',
            'expected': 'Promo_Sadhguru-Darshan-On-New-Years-Eve_28-Dec-2024_English_41Secs_Stems'
        },
        {
            'query': 'Bi9PLrYH3Ef',
            'expected': 'Talk_Stop-Using-I-Am-Only-Human-As-An-Excuse_19-May-2018_English_58Secs_Consolidated'
        },
        {
            'query': 'Bm-QyuLHutC',
            'expected': 'Z5129_Celebrity-QnA_Youth-And-Truth_Former-Indian-Cricketer-VVS-Laxman_Should-Kids-Be-Free-To-Make-Decisions_English_Stems_19-Sep-2018'
        },
        {
            'query': 'Bm7zt7_nWN4',
            'expected': 'Z5125_Celebrity-QnA_Youth-And-Truth_Cricketer-Mithali-Raj_How-Do-I-Ignore-Negative-Opinions_English_Stems_19-Sep-2018'
        },
        
        # More OCD Numbers
        {
            'query': 'OCD-11170',
            'expected': 'Insta-Reels_Is-It-Better-To-God-Or-Demon_11-Jun-2025_Tamil_45Secs_Stems'
        },
        {
            'query': 'OCD-17232',
            'expected': 'Promo_Guru-Purnima-At-Linga-Bhairavi-A-Day-Of-Grace_26-Jun-2025_English_29Secs_Stems'
        },
        {
            'query': 'OCD-16513',
            'expected': 'Talk_Love-Is-Not-A-Marketplace-Deal_16-Jun-2025_English_35Mins-14Secs_Stems'
        },
        {
            'query': 'OCD-13113',
            'expected': 'Q-And-A_Bad-Luck-Karma-Accidents-And-Spirituality-Unreleased-Talk-From-2006_20-Jun-2025_English_40Mins-40Secs_Stems'
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case['query']
        expected = test_case['expected']
        
        print(f"\n🧪 TEST {i:2d}: '{query}'")
        print(f"   Expected: {expected[:60]}...")
        
        # Search in cache
        found = False
        matches = []
        
        # Search in OCD/VP column
        ocd_matches = df[df['ocd_vp'].astype(str).str.contains(query, case=False, na=False)]
        if not ocd_matches.empty:
            matches.extend(ocd_matches['filename'].tolist())
        
        # Search in video_id column
        video_matches = df[df['video_id'].astype(str).str.contains(query, case=False, na=False)]
        if not video_matches.empty:
            matches.extend(video_matches['filename'].tolist())
        
        # Search in filename column
        filename_matches = df[df['filename'].astype(str).str.contains(query, case=False, na=False)]
        if not filename_matches.empty:
            matches.extend(filename_matches['filename'].tolist())
        
        # Check if expected result is found
        for match in matches:
            if expected.lower() in match.lower() or match.lower() in expected.lower():
                found = True
                break
        
        if found:
            print(f"   ✅ FOUND: Expected result found in matches")
        else:
            print(f"   ❌ NOT FOUND: Expected result not found")
            if matches:
                print(f"   📋 Found {len(matches)} other matches:")
                for match in matches[:3]:  # Show first 3 matches
                    print(f"      - {match[:70]}...")
            else:
                print(f"   📋 No matches found at all")
        
        results.append({
            'query': query,
            'expected': expected,
            'found': found,
            'match_count': len(matches)
        })
    
    # Summary
    print(f"\n📊 SEARCH TEST SUMMARY")
    print("=" * 80)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['found'])
    failed_tests = total_tests - successful_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"✅ Successful: {successful_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"📊 Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if failed_tests > 0:
        print(f"\n❌ Failed Tests:")
        for result in results:
            if not result['found']:
                print(f"   - {result['query']}: {result['match_count']} matches found")
    
    print("=" * 80)

if __name__ == "__main__":
    test_specific_searches()
