<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archives Stems Finder Pro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .user-info {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .content {
            padding: 40px;
        }

        .search-section {
            margin-bottom: 30px;
        }

        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            padding: 18px;
            border: 3px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .search-btn {
            padding: 18px 35px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .search-btn:hover {
            transform: translateY(-2px);
        }

        .cache-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .cache-stat {
            text-align: center;
        }

        .cache-stat h3 {
            color: #495057;
            margin-bottom: 5px;
        }

        .cache-stat p {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }

        .results-section {
            margin-top: 30px;
        }

        .stats {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }

        .stats h3 {
            margin-bottom: 10px;
        }

        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            transition: transform 0.2s;
        }

        .result:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .result-filename {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            word-break: break-all;
        }

        .result-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .result-detail {
            background: white;
            padding: 10px;
            border-radius: 5px;
        }

        .result-detail strong {
            color: #495057;
        }

        .drive-btn {
            background: #059669;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s ease;
            font-family: inherit;
            white-space: nowrap;
        }

        .drive-btn:hover {
            background: #047857;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
            color: white;
            text-decoration: none;
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            gap: 15px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .no-results {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .search-form {
                flex-direction: column;
            }
            
            .user-info {
                position: static;
                justify-content: center;
                margin-top: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Archives Stems Finder Pro</h1>
            <p>Enhanced Search System with Google Authentication</p>
            
            <div class="user-info">
                <img src="{{ user.picture }}" alt="User Avatar" class="user-avatar">
                <span>{{ user.name }}</span>
                <a href="{{ url_for('logout') }}" class="logout-btn">Logout</a>
            </div>
        </div>
        
        <div class="content">
            <!-- Cache Information -->
            <div class="cache-info">
                <div class="cache-stat">
                    <h3>Total Records</h3>
                    <p>{{ cache_info.total_records }}</p>
                </div>
                <div class="cache-stat">
                    <h3>Last Updated</h3>
                    <p>{{ cache_info.last_updated }}</p>
                </div>
                <div class="cache-stat">
                    <h3>Next Refresh</h3>
                    <p>{{ cache_info.next_refresh or 'Unknown' }}</p>
                </div>
                <div class="cache-stat">
                    <h3>Status</h3>
                    <p>{{ cache_info.status }}</p>
                </div>
            </div>

            <!-- Cache Management -->
            <div class="cache-management" style="background: #e9ecef; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                <h3 style="color: #495057; margin-bottom: 15px;">Cache Management</h3>
                <div style="display: flex; gap: 15px; align-items: center;">
                    <button id="refreshCacheBtn" class="search-btn" style="padding: 10px 20px; font-size: 14px;">
                        🔄 Manual Refresh
                    </button>
                    <div id="refreshProgress" style="display: none; flex: 1;">
                        <div style="background: #f8f9fa; border-radius: 10px; padding: 10px;">
                            <div style="background: #007bff; height: 20px; border-radius: 5px; width: 0%; transition: width 0.3s;" id="progressBar"></div>
                            <p id="progressText" style="margin: 5px 0 0 0; font-size: 12px; color: #666;">Starting...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search Section -->
            <div class="search-section">
                <form id="searchForm" class="search-form">
                    <input type="text" id="searchInput" class="search-input" placeholder="Enter your search query..." required>
                    <button type="submit" class="search-btn">🔍 Search</button>
                </form>
            </div>
            
            <!-- Results Section -->
            <div id="resultsSection" class="results-section" style="display: none;">
                <div id="stats" class="stats">
                    <h3>🎉 Search Results</h3>
                    <p id="statsText"></p>
                </div>
                
                <div id="results"></div>
            </div>
            
            <!-- Loading -->
            <div id="loading" class="loading" style="display: none;">
                <h3>🔍 Searching...</h3>
                <p>Please wait while we search the archives...</p>
            </div>
            
            <!-- Error -->
            <div id="error" class="error" style="display: none;"></div>
            
            <!-- No Results -->
            <div id="noResults" class="no-results" style="display: none;">
                <h3>🔍 No matches found</h3>
                <p>Try a different search term or check your spelling.</p>
            </div>
        </div>
    </div>

    <script>
        // Extract OCD/VP number from string for Google Drive link
        function extractOcdVpNumber(ocdVpString) {
            if (!ocdVpString || ocdVpString === 'Not Available') return null;

            // Match patterns like OCD-16693, VP-17523, etc.
            const match = ocdVpString.match(/(OCD|VP)-\d+/i);
            return match ? match[0].toUpperCase() : null;
        }

        // Generate Google Drive search URL
        function generateDriveUrl(ocdVpNumber) {
            if (!ocdVpNumber) return null;
            return `https://drive.google.com/drive/search?q=${encodeURIComponent(ocdVpNumber)}`;
        }

        // Search functionality using relative URLs
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });

        function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) return;

            // Show loading
            showLoading();

            // Prepare form data
            const formData = new FormData();
            formData.append('query', query);

            // Make search request using relative URL
            fetch('{{ url_for("search") }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                displayResults(data);
            })
            .catch(error => {
                hideLoading();
                showError('Search failed: ' + error.message);
            });
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('error').style.display = 'none';
            document.getElementById('noResults').style.display = 'none';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('noResults').style.display = 'none';
        }

        function displayResults(data) {
            if (data.error) {
                showError(data.error);
                return;
            }

            if (data.total_matches === 0) {
                document.getElementById('noResults').style.display = 'block';
                document.getElementById('resultsSection').style.display = 'none';
                document.getElementById('error').style.display = 'none';
                return;
            }

            // Update stats
            document.getElementById('statsText').textContent =
                `Found ${data.total_matches} matches in ${data.search_time} seconds`;

            // Display results
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';

            data.results.forEach(result => {
                const ocdVpNumber = extractOcdVpNumber(result.ocd_vp);
                const driveUrl = generateDriveUrl(ocdVpNumber);

                const resultDiv = document.createElement('div');
                resultDiv.className = 'result';
                resultDiv.innerHTML = `
                    <div class="result-header">
                        <div class="result-filename">${result.filename}</div>
                        ${driveUrl ? `<a href="${driveUrl}" target="_blank" class="drive-btn">📁 Open in Drive</a>` : ''}
                    </div>
                    <div class="result-details">
                        <div class="result-detail"><strong>Match Type:</strong> ${result.match_type}</div>
                        <div class="result-detail"><strong>Score:</strong> ${result.score}</div>
                        <div class="result-detail"><strong>OCD/VP:</strong> ${result.ocd_vp}</div>
                        <div class="result-detail"><strong>Video ID:</strong> ${result.video_id}</div>
                        <div class="result-detail"><strong>Sheet:</strong> ${result.sheet_name}</div>
                    </div>
                `;
                resultsDiv.appendChild(resultDiv);
            });

            document.getElementById('resultsSection').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('noResults').style.display = 'none';
        }

        // Cache refresh functionality
        document.getElementById('refreshCacheBtn').addEventListener('click', function() {
            refreshCache();
        });

        function refreshCache() {
            const btn = document.getElementById('refreshCacheBtn');
            const progressDiv = document.getElementById('refreshProgress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            // Disable button and show progress
            btn.disabled = true;
            btn.textContent = '🔄 Refreshing...';
            progressDiv.style.display = 'flex';
            progressBar.style.width = '0%';
            progressText.textContent = 'Starting cache refresh...';

            // Make refresh request
            fetch('{{ url_for("manual_cache_refresh") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    progressBar.style.width = '100%';
                    progressText.textContent = data.message;

                    // Update cache info on page
                    setTimeout(() => {
                        location.reload(); // Refresh page to show updated cache info
                    }, 2000);
                } else {
                    progressText.textContent = 'Error: ' + (data.error || 'Unknown error');
                    progressBar.style.backgroundColor = '#dc3545';
                }
            })
            .catch(error => {
                progressText.textContent = 'Network error: ' + error.message;
                progressBar.style.backgroundColor = '#dc3545';
            })
            .finally(() => {
                // Re-enable button after delay
                setTimeout(() => {
                    btn.disabled = false;
                    btn.textContent = '🔄 Manual Refresh';
                    progressDiv.style.display = 'none';
                    progressBar.style.backgroundColor = '#007bff';
                }, 3000);
            });
        }
    </script>
</body>
</html>
