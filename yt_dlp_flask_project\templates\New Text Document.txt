<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Title to Stems Matcher</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .result {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>YouTube Title to Stems Matcher</h1>
    <form id="urlForm" method="POST" action="/match">
        <label for="url">Paste YouTube URL:</label><br>
        <input type="text" id="url" name="url" style="width: 100%;" required><br><br>
        <button type="submit">Match Filename</button>
    </form>

    <div id="result" class="result"></div>
    <div id="error"></div>

    <script>
        const form = document.getElementById('urlForm');
        form.onsubmit = async function(e) {
            e.preventDefault();
            const url = document.getElementById('url').value;
            
            const response = await fetch('/match', {
                method: 'POST',
                body: new URLSearchParams('url=' + url),
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            const data = await response.json();
            if (data.error) {
                document.getElementById('error').innerText = data.error;
            } else {
                let resultHtml = `<h2>Video: ${data.video_title} (${data.yt_duration})</h2>`;
                resultHtml += `<h3>Matches found:</h3><ul>`;
                data.result.forEach(match => {
                    resultHtml += `<li style="color: ${match.color}">${match.filename} - [${match.file_duration}] | [YT: ${match.yt_duration}] | Score: ${match.score}%</li>`;
                });
                resultHtml += `</ul>`;
                resultHtml += `<p>Task completed in: ${data.elapsed_time} seconds</p>`;
                document.getElementById('result').innerHTML = resultHtml;
            }
        };
    </script>
</body>
</html>
