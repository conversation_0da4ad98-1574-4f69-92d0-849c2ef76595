#!/usr/bin/env python3
"""
Test cache refresh with debug logging
"""

import requests
import time

def test_refresh_debug():
    """Test cache refresh with debug"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("1. Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"   Login status: {login_response.status_code}")
        
        # Test cache refresh
        print("\n2. Testing cache refresh...")
        print("   Watch the app logs for detailed debug information...")
        
        start_time = time.time()
        refresh_response = session.post(f"{base_url}/refresh-cache")
        elapsed_time = time.time() - start_time
        
        print(f"   Refresh completed in {elapsed_time:.3f}s")
        print(f"   Response status: {refresh_response.status_code}")
        
        if refresh_response.status_code == 200:
            data = refresh_response.json()
            print(f"   Success: {data.get('success', False)}")
            print(f"   Message: {data.get('message', 'N/A')}")
            print(f"   Cache size: {data.get('cache_size', 0):,}")
            print(f"   Elapsed time: {data.get('elapsed_time', 0)}s")
        else:
            print(f"   Error: {refresh_response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_refresh_debug()
