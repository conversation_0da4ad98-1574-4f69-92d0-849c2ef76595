#!/usr/bin/env python3
"""
Debug a single query to see exactly what's happening
"""

import requests
import json

def debug_single_query():
    """Debug a single query step by step"""
    
    print("🔧 DEBUGGING SINGLE QUERY")
    print("=" * 60)
    
    # Start Flask app first
    print("📋 Starting Flask app...")
    
    # Wait a moment for Flask to start
    import time
    time.sleep(2)
    
    session = requests.Session()
    
    # Test login
    print("\n📋 Testing login...")
    try:
        login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'}, timeout=5)
        print(f"Login status: {login_response.status_code}")
        if login_response.status_code != 200:
            print("❌ Login failed")
            return False
        print("✅ Login successful")
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test basic query first
    print("\n📋 Testing basic query...")
    try:
        basic_response = session.post("http://127.0.0.1:8080/search", data={'query': 'test'}, timeout=10)
        print(f"Basic search status: {basic_response.status_code}")
        if basic_response.status_code == 200:
            basic_result = basic_response.json()
            print(f"Basic search matches: {basic_result.get('total_matches', 0)}")
            print("✅ Basic search works")
        else:
            print(f"❌ Basic search failed: {basic_response.text}")
            return False
    except Exception as e:
        print(f"❌ Basic search error: {e}")
        return False
    
    # Test the exact test case query
    print("\n📋 Testing test case query...")
    query = "A tip to prevent cancer?"
    print(f"Query: '{query}'")
    
    try:
        response = session.post("http://127.0.0.1:8080/search", data={'query': query}, timeout=15)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"Response JSON: {json.dumps(result, indent=2)}")
                
                matches = result.get('total_matches', 0)
                print(f"Total matches: {matches}")
                
                if matches > 0:
                    print("✅ Test case query returned results!")
                    for i, match in enumerate(result.get('results', []), 1):
                        print(f"  Match {i}: {match.get('filename', 'N/A')}")
                        print(f"  Type: {match.get('match_type', 'Unknown')}")
                    return True
                else:
                    print("❌ Test case query returned 0 matches")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {response.text}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            print(f"Response text: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 DEBUGGING SINGLE QUERY")
    print("Finding out exactly what's wrong with the test case queries")
    
    success = debug_single_query()
    
    print("\n" + "=" * 60)
    print("🎯 DEBUG RESULTS")
    print("=" * 60)
    
    if success:
        print("🎉 Test case query is working!")
    else:
        print("🔧 Test case query needs fixing")
    
    print("\n🚀 Debug complete!")
