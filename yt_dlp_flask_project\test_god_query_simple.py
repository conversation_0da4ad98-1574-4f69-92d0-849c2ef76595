#!/usr/bin/env python3
"""
Simple test for the God query
"""

import requests

def test_god_query():
    """Test the God query simply"""
    
    print("🔍 SIMPLE TEST: 'Why is <PERSON> giving problems?'")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test the query
    query = "Why is <PERSON> giving problems?"
    print(f"\n🔍 Testing: '{query}'")
    
    response = session.post("http://127.0.0.1:8080/search", data={'query': query})
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        matches = result.get('total_matches', 0)
        
        print(f"Matches: {matches}")
        
        if matches > 0:
            print("🎉 SUCCESS! Found matches:")
            for i, match in enumerate(result.get('results', []), 1):
                filename = match.get('filename', 'N/A')
                match_type = match.get('match_type', 'Unknown')
                
                print(f"   {i}. {filename}")
                print(f"      Type: {match_type}")
                
                if "Why-Is-God-Giving-Problems" in filename:
                    print(f"      ✅ THIS IS THE EXPECTED FILE!")
                    return True
            return False
        else:
            print("❌ No matches found")
            return False
    else:
        print(f"❌ Error: {response.status_code}")
        print(f"Response: {response.text}")
        return False

if __name__ == "__main__":
    success = test_god_query()
    
    if success:
        print("\n🎉 SUCCESS! The query is working!")
    else:
        print("\n🔧 Still not working")
    
    print("\n🚀 Simple test complete!")
