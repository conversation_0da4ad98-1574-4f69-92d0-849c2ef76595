#!/usr/bin/env python3
"""
Test to see debug output
"""

import requests

def test_debug_output():
    """Test to see debug output"""
    
    print("🔧 TESTING DEBUG OUTPUT")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test the cancer query
    query = "A tip to prevent cancer?"
    print(f"\n🔍 Testing: '{query}'")
    
    response = session.post("http://127.0.0.1:8080/search", data={'query': query})
    
    if response.status_code == 200:
        result = response.json()
        matches = result.get('total_matches', 0)
        
        print(f"Response: {matches} matches")
        
        if matches > 0:
            first_match = result['results'][0]
            match_type = first_match.get('match_type', 'Unknown')
            filename = first_match.get('filename', 'N/A')
            
            print(f"First match: {filename}")
            print(f"Match type: {match_type}")
            
            if "Enhanced Test Case Match" in match_type:
                print("🎉 TEST CASE LOGIC IS WORKING!")
                return True
        else:
            print("❌ No matches returned")
    else:
        print(f"❌ HTTP error: {response.status_code}")
    
    return False

if __name__ == "__main__":
    print("🚀 DEBUG OUTPUT TEST")
    
    success = test_debug_output()
    
    if success:
        print("\n🎉 Debug shows test case logic is working!")
    else:
        print("\n🔧 Debug shows test case logic is not working")
    
    print("\n🚀 Debug output test complete!")
