#!/usr/bin/env python3
"""
Startup script for Archives Stems Finder Pro
This script sets up environment variables and starts the Flask application
"""

import os
import sys
from app import app, Config

def check_environment():
    """Check if all required environment variables are set"""
    required_vars = ['GOOGLE_CLIENT_ID', 'GOOGLE_CLIENT_SECRET']
    missing_vars = []
    
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease set these environment variables before starting the application.")
        print("See README.md for setup instructions.")
        return False
    
    return True

def main():
    """Main startup function"""
    print("🚀 Starting Archives Stems Finder Pro")
    print("=" * 60)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    print("✅ Environment variables configured")
    print(f"🔐 Google Client ID: {Config.GOOGLE_CLIENT_ID[:20]}...")
    print("🌐 Starting Flask application...")
    print("=" * 60)
    
    # Start the Flask app
    app.run(host='127.0.0.1', port=5000, debug=False)

if __name__ == '__main__':
    main()
