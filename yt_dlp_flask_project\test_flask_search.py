#!/usr/bin/env python3
"""
Test Flask search functionality directly
"""

import sys
import os
sys.path.append('.')

# Import the search function from the clean app
from lightning_app_clean import search_cache, detect_search_type, ensure_cache_ready

def test_flask_search():
    """Test the Flask search function directly"""
    
    test_queries = [
        "https://youtu.be/rbYdXbEVm6E",
        "rbYdXbEVm6E",
        "OCD-9585",
        "TMHhylNs-3Q",
        "https://www.instagram.com/p/BtAa0Mihu85/"
    ]
    
    print("🧪 TESTING FLASK SEARCH FUNCTION")
    print("=" * 60)
    
    # Ensure cache is ready
    ensure_cache_ready()
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: {query}")
        print("-" * 40)
        
        try:
            # Test search type detection
            search_type = detect_search_type(query)
            print(f"   Search Type: {search_type}")
            
            # Test search
            import time
            start_time = time.time()
            results = search_cache(query)
            elapsed_time = time.time() - start_time
            
            print(f"   Results: {len(results)} matches")
            print(f"   Time: {elapsed_time:.3f}s")
            
            if results:
                print(f"   First match: {results[0]['filename'][:50]}...")
                print(f"   Drive link: {results[0]['drive_link']}")
            
            print(f"   ✅ SUCCESS")
            
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 Flask search function test completed!")

if __name__ == "__main__":
    test_flask_search()
