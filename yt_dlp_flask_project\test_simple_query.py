#!/usr/bin/env python3
"""
Simple test for one specific query
"""

import requests
import time

def test_simple_query():
    """Test one simple query"""
    
    print("🔍 SIMPLE QUERY TEST")
    print("=" * 50)
    
    try:
        session = requests.Session()
        
        # Login
        print("📋 Step 1: Login")
        login_response = session.post("http://127.0.0.1:8080/login", 
                                    data={'password': 'Shiva@123'}, 
                                    timeout=5)
        
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    try:
        # Test query
        print("\n📋 Step 2: Search")
        query = "Living Without Regrets"
        print(f"Testing: '{query}'")
        
        search_response = session.post("http://127.0.0.1:8080/search", 
                                     data={'query': query}, 
                                     timeout=10)
        
        print(f"Response Status: {search_response.status_code}")
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            print(f"Total Matches: {matches}")
            print(f"Search Time: {search_time:.3f}s")
            
            if matches > 0:
                print("\n🎉 SUCCESS! Found matches:")
                
                for i, match in enumerate(result.get('results', [])[:3], 1):
                    filename = match.get('filename', 'N/A')
                    match_type = match.get('match_type', 'Unknown')
                    
                    print(f"   {i}. {filename}")
                    print(f"      Type: {match_type}")
                    
                    if "Living-Without-Regrets" in filename:
                        print(f"      ✅ FOUND EXPECTED FILE!")
                        return True
                
                return False
            else:
                print("\n❌ No matches found")
                return False
        else:
            print(f"❌ Search failed: {search_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Search error: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_query()
    
    if success:
        print("\n🎉 SUCCESS! Query is working!")
    else:
        print("\n🔧 Still not working")
    
    print("\n🚀 Simple test complete!")
