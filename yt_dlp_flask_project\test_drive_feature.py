#!/usr/bin/env python3
"""
Test the Google Drive feature functionality
"""

import pandas as pd
import re

def test_drive_feature():
    """Test the Google Drive link generation"""
    
    print("🔍 Testing Google Drive Feature")
    print("=" * 50)
    
    # Load the cache to test with real data
    try:
        df = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded {len(df)} rows from cache")
        
        # Test with some sample OCD/VP numbers
        test_queries = ['VP-37160', 'OCD-17588', 'VP-17523']
        
        for query in test_queries:
            print(f"\n🧪 Testing: {query}")
            
            # Search for matches
            matches = df[df['ocd_vp'].astype(str).str.contains(query, case=False, na=False)]
            
            if not matches.empty:
                for _, row in matches.head(3).iterrows():  # Show first 3 matches
                    filename = row['filename']
                    ocd_vp = row['ocd_vp']
                    
                    # Extract OCD/VP number
                    match = re.search(r'(OCD|VP)-\d+', str(ocd_vp))
                    if match:
                        ocd_vp_number = match.group(0).upper()
                        drive_url = f"https://drive.google.com/drive/search?q={ocd_vp_number}"
                        
                        print(f"   📄 File: {filename[:60]}...")
                        print(f"   🔢 OCD/VP: {ocd_vp}")
                        print(f"   🔗 Drive URL: {drive_url}")
                        print(f"   ✅ Drive button will be shown")
                    else:
                        print(f"   📄 File: {filename[:60]}...")
                        print(f"   🔢 OCD/VP: {ocd_vp}")
                        print(f"   ❌ No valid OCD/VP number found")
            else:
                print(f"   ❌ No matches found for {query}")
        
        print(f"\n📊 Drive Feature Test Complete")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_drive_feature()
