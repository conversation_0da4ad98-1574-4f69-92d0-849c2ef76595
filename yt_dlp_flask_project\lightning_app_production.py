#!/usr/bin/env python3
"""
Archives Stems Finder Pro - PRODUCTION VERSION (24/7 Ready)
Optimized for maximum search performance with cache refresh features
"""

import os
import time
import logging
import pandas as pd
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
import re
from flask import Flask, request, jsonify, render_template, session, redirect, url_for
import json
import secrets
import threading
from logging.handlers import RotatingFileHandler
import sys

# Configure production-ready logging for 24/7 operation
def setup_production_logging():
    """Setup production-ready logging with rotation"""
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.handlers.clear()
    
    # File handler with rotation (10MB max, keep 5 files)
    file_handler = RotatingFileHandler(
        'lightning_app.log', 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(detailed_formatter)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Suppress Flask's default logging in production
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    
    return root_logger

# Setup logging
logger = setup_production_logging()

class ArchivesConfig:
    """Configuration for Archives Stems Finder Pro - 24/7 Production Ready"""
    APP_NAME = "Archives Stems Finder Pro"
    APP_VERSION = "2.1 PRODUCTION"
    SECRET_KEY = secrets.token_hex(32)
    ACCESS_PASSWORD = "Shiva@123"
    SESSION_TIMEOUT_HOURS = 24  # Extended for 24/7 operation
    CACHE_FILE = 'archives_cache.csv'
    MAX_RESULTS = 10
    GOOGLE_DRIVE_FOLDER = "https://drive.google.com/drive/folders/1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG"
    
    # Production settings for 24/7 operation
    DAILY_REFRESH_HOUR = 3  # 3:00 AM daily refresh
    LOG_RETENTION_DAYS = 30  # Keep logs for 30 days
    HEALTH_CHECK_INTERVAL = 300  # Health check every 5 minutes

# Global variables
app = Flask(__name__)
app.secret_key = ArchivesConfig.SECRET_KEY
cached_df = None
cache_last_updated = None

def load_cache():
    """Load cache from file - OPTIMIZED FOR SPEED"""
    global cached_df, cache_last_updated
    
    if os.path.exists(ArchivesConfig.CACHE_FILE):
        try:
            start_time = time.time()
            cached_df = pd.read_csv(ArchivesConfig.CACHE_FILE)
            cache_last_updated = datetime.fromtimestamp(os.path.getmtime(ArchivesConfig.CACHE_FILE))
            elapsed = time.time() - start_time
            logging.info(f"Cache loaded: {len(cached_df)} rows in {elapsed:.3f}s")
            return True
        except Exception as e:
            logging.error(f"Error loading cache: {str(e)}")
    
    return False

def ensure_cache_ready():
    """Ensure cache is ready"""
    global cached_df
    if cached_df is None:
        load_cache()

# Authentication functions
def check_password(password):
    return password == ArchivesConfig.ACCESS_PASSWORD

def is_authenticated():
    if 'authenticated' not in session:
        return False
    if 'login_time' in session:
        login_time = datetime.fromisoformat(session['login_time'])
        if datetime.now() - login_time > timedelta(hours=ArchivesConfig.SESSION_TIMEOUT_HOURS):
            session.clear()
            return False
    return session.get('authenticated', False)

def require_auth(f):
    def decorated_function(*args, **kwargs):
        if not is_authenticated():
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def extract_video_id_from_url(url):
    """Extract video ID from various social media URLs"""
    # Instagram patterns
    instagram_patterns = [
        r'instagram\.com/p/([A-Za-z0-9_-]+)',
        r'instagram\.com/reel/([A-Za-z0-9_-]+)'
    ]
    
    for pattern in instagram_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    # YouTube patterns
    youtube_patterns = [
        r'youtube\.com/watch\?v=([A-Za-z0-9_-]+)',
        r'youtu\.be/([A-Za-z0-9_-]+)',
        r'youtube\.com/embed/([A-Za-z0-9_-]+)',
        r'youtube\.com/shorts/([A-Za-z0-9_-]+)'
    ]
    
    for pattern in youtube_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    return None

def detect_search_type(query):
    """Detect search type"""
    original_query = query.strip()
    query = query.strip().upper()
    
    # Check for URLs first
    if any(domain in original_query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
        video_id = extract_video_id_from_url(original_query)
        if video_id:
            return 'video_id'
        return 'url'
    
    # Check for OCD/VP numbers
    if query.startswith('OCD-') or query.startswith('VP-') or (query.startswith(('OCD', 'VP')) and any(c.isdigit() for c in query)):
        return 'ocd_number'
    
    # Check for Video ID (11 characters)
    if len(query) == 11 and query.replace('_', '').replace('-', '').isalnum():
        return 'video_id'
    
    return 'title_search'

def search_cache(query):
    """Search the cache dataframe - ULTRA FAST"""
    global cached_df
    ensure_cache_ready()
    
    if cached_df is None or cached_df.empty:
        return []
    
    search_type = detect_search_type(query)
    
    if search_type == 'video_id':
        if any(domain in query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
            extracted_video_id = extract_video_id_from_url(query)
            if extracted_video_id:
                query = extracted_video_id
        
        # Search in video_id and video_ids columns
        try:
            mask = cached_df['video_id'].astype(str).str.contains(query, na=False, regex=False)
            if 'video_ids' in cached_df.columns:
                mask = mask | cached_df['video_ids'].astype(str).str.contains(query, na=False, regex=False)
        except Exception as e:
            logging.error(f"Error in video_id search: {e}")
            mask = cached_df['video_id'].astype(str).str.contains(query, na=False, regex=False)
    
    elif search_type == 'ocd_number':
        # Search in ocd_vp column
        mask = cached_df['ocd_vp'].astype(str).str.upper().str.contains(query.upper(), na=False, regex=False)
    
    else:
        # Search in filename
        mask = cached_df['filename'].astype(str).str.lower().str.contains(query.lower(), na=False, regex=False)
    
    results = cached_df[mask].head(ArchivesConfig.MAX_RESULTS)
    
    # Convert to list of dictionaries
    matches = []
    for _, row in results.iterrows():
        ocd_vp = str(row.get('ocd_vp', '')).strip()
        video_id = str(row.get('video_id', '')).strip()
        
        if ocd_vp and ocd_vp not in ['nan', 'NaN', '']:
            drive_link = f"https://drive.google.com/drive/search?q={ocd_vp}"
        elif video_id and video_id not in ['nan', 'NaN', '']:
            drive_link = f"https://drive.google.com/drive/search?q={video_id}"
        else:
            drive_link = f"{ArchivesConfig.GOOGLE_DRIVE_FOLDER}?q={row['filename'].replace(' ', '+')}"
        
        def safe_str(value):
            if pd.isna(value) or str(value).lower() in ['nan', 'none', 'null']:
                return ''
            return str(value)
        
        matches.append({
            'filename': safe_str(row['filename']),
            'duration': safe_str(row['duration']),
            'ocd_vp': safe_str(row['ocd_vp']),
            'video_id': safe_str(row['video_id']),
            'sheet_name': safe_str(row.get('sheet_name', 'Unknown')),
            'score': 100,
            'search_type': search_type.replace('_', ' ').title(),
            'drive_link': drive_link
        })
    
    return matches

# Flask routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        password = request.form.get('password', '')
        if check_password(password):
            session['authenticated'] = True
            session['login_time'] = datetime.now().isoformat()
            return redirect(url_for('index'))
        else:
            return render_template('login.html', error='Invalid password', app_name=ArchivesConfig.APP_NAME)
    return render_template('login.html', app_name=ArchivesConfig.APP_NAME)

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/')
@require_auth
def index():
    return render_template('archives_index.html', app_name=ArchivesConfig.APP_NAME, app_version=ArchivesConfig.APP_VERSION)

@app.route('/search', methods=['POST'])
@require_auth
def search():
    try:
        query = request.form.get('query', '').strip()
        if not query:
            return jsonify({'error': 'Query is required'}), 400

        logging.info(f"Search request: '{query}'")
        start_time = time.time()
        results = search_cache(query)
        elapsed_time = time.time() - start_time

        logging.info(f"Search completed: {len(results)} results in {elapsed_time:.3f}s")

        return jsonify({
            'success': True,
            'query': query,
            'results': results,
            'total_matches': len(results),
            'elapsed_time': round(elapsed_time, 3),
            'search_type': detect_search_type(query)
        })

    except Exception as e:
        logging.error(f"Error in search for '{query}': {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return jsonify({'error': f'Search failed: {str(e)}'}), 500

@app.route('/cache/status')
@require_auth
def cache_status():
    global cached_df, cache_last_updated
    ensure_cache_ready()

    # Calculate next refresh time (3:00 AM next day)
    now = datetime.now()
    next_refresh = now.replace(hour=3, minute=0, second=0, microsecond=0)
    if now.hour >= 3:
        next_refresh += timedelta(days=1)

    return jsonify({
        'cache_loaded': cached_df is not None,
        'cache_size': len(cached_df) if cached_df is not None else 0,
        'last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
        'next_refresh': next_refresh.isoformat(),
        'cache_file_exists': os.path.exists(ArchivesConfig.CACHE_FILE)
    })

@app.route('/refresh-cache', methods=['POST'])
@require_auth
def refresh_cache():
    """Manual cache refresh endpoint"""
    try:
        global cached_df, cache_last_updated

        logging.info("Manual cache refresh requested")
        start_time = time.time()

        # Force reload cache and update timestamp
        if os.path.exists(ArchivesConfig.CACHE_FILE):
            cached_df = pd.read_csv(ArchivesConfig.CACHE_FILE)
            # Update the timestamp to current time for manual refresh
            cache_last_updated = datetime.now()
            elapsed_time = time.time() - start_time

            logging.info(f"Cache refreshed successfully in {elapsed_time:.3f}s")
            logging.info(f"Cache timestamp updated to: {cache_last_updated}")

            return jsonify({
                'success': True,
                'message': 'Cache refreshed successfully',
                'cache_size': len(cached_df) if cached_df is not None else 0,
                'elapsed_time': round(elapsed_time, 3),
                'last_updated': cache_last_updated.isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Cache file not found'
            }), 500

    except Exception as e:
        logging.error(f"Error refreshing cache: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Cache refresh failed: {str(e)}'
        }), 500

@app.route('/health')
def health_check():
    """Health check endpoint for 24/7 monitoring"""
    try:
        global cached_df, cache_last_updated

        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'cache_loaded': cached_df is not None,
            'cache_size': len(cached_df) if cached_df is not None else 0,
            'cache_last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
            'uptime': time.time() - app.start_time if hasattr(app, 'start_time') else 0,
            'version': ArchivesConfig.APP_VERSION,
            'app_name': ArchivesConfig.APP_NAME
        }

        # Check if cache is healthy
        if cached_df is None or cached_df.empty:
            health_status['status'] = 'degraded'
            health_status['issues'] = ['Cache not loaded or empty']

        return jsonify(health_status)

    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }), 500

def schedule_daily_refresh():
    """Schedule daily cache refresh at 3:00 AM - Production Ready"""
    def daily_refresh_worker():
        while True:
            try:
                now = datetime.now()
                # Calculate next refresh time
                next_refresh = now.replace(
                    hour=ArchivesConfig.DAILY_REFRESH_HOUR,
                    minute=0,
                    second=0,
                    microsecond=0
                )
                if now.hour >= ArchivesConfig.DAILY_REFRESH_HOUR:
                    next_refresh += timedelta(days=1)

                # Calculate sleep time
                sleep_seconds = (next_refresh - now).total_seconds()
                logging.info(f"Next automatic cache refresh scheduled for: {next_refresh}")

                # Sleep until refresh time
                time.sleep(sleep_seconds)

                # Perform refresh with error handling
                logging.info("Performing scheduled daily cache refresh...")
                global cached_df, cache_last_updated

                try:
                    if os.path.exists(ArchivesConfig.CACHE_FILE):
                        cached_df = pd.read_csv(ArchivesConfig.CACHE_FILE)
                        cache_last_updated = datetime.now()
                        logging.info(f"Scheduled cache refresh completed successfully - {len(cached_df)} records")
                        logging.info(f"Cache timestamp updated to: {cache_last_updated}")
                    else:
                        logging.error("Scheduled cache refresh failed - cache file not found")

                except Exception as refresh_error:
                    logging.error(f"Error in scheduled cache refresh: {refresh_error}")
                    import traceback
                    logging.error(traceback.format_exc())

            except Exception as scheduler_error:
                logging.error(f"Error in refresh scheduler: {scheduler_error}")
                # Sleep for 1 hour before retrying to prevent rapid failures
                time.sleep(3600)

    # Start the scheduler in a background thread
    refresh_thread = threading.Thread(target=daily_refresh_worker, daemon=True)
    refresh_thread.start()
    logging.info(f"Daily cache refresh scheduler started ({ArchivesConfig.DAILY_REFRESH_HOUR}:00 AM)")

def start_health_monitor():
    """Start health monitoring for 24/7 operation"""
    def health_monitor_worker():
        while True:
            try:
                # Check cache health
                global cached_df
                if cached_df is None or cached_df.empty:
                    logging.warning("WARNING: Health Check: Cache is empty or not loaded")
                    try:
                        ensure_cache_ready()
                        logging.info("Health Check: Cache reloaded successfully")
                    except Exception as e:
                        logging.error(f"Health Check: Failed to reload cache - {e}")
                else:
                    logging.info(f"Health Check: Cache healthy - {len(cached_df)} records")

                # Check disk space (basic check)
                cache_file_size = os.path.getsize(ArchivesConfig.CACHE_FILE) if os.path.exists(ArchivesConfig.CACHE_FILE) else 0
                logging.info(f"Health Check: Cache file size - {cache_file_size / (1024*1024):.2f} MB")

                # Sleep until next health check
                time.sleep(ArchivesConfig.HEALTH_CHECK_INTERVAL)

            except Exception as e:
                logging.error(f"Error in health monitor: {e}")
                time.sleep(60)  # Sleep 1 minute before retrying

    # Start health monitor in background thread
    health_thread = threading.Thread(target=health_monitor_worker, daemon=True)
    health_thread.start()
    logging.info(f"Health monitor started (check every {ArchivesConfig.HEALTH_CHECK_INTERVAL}s)")

if __name__ == '__main__':
    # Record start time for uptime tracking
    app.start_time = time.time()

    logging.info("=" * 80)
    logging.info(f"STARTING {ArchivesConfig.APP_NAME} v{ArchivesConfig.APP_VERSION}")
    logging.info("PRODUCTION MODE - 24/7 Operation")
    logging.info("=" * 80)
    logging.info(f"Cache file: {ArchivesConfig.CACHE_FILE}")
    logging.info(f"Daily refresh: {ArchivesConfig.DAILY_REFRESH_HOUR}:00 AM")
    logging.info(f"Health checks: Every {ArchivesConfig.HEALTH_CHECK_INTERVAL}s")
    logging.info(f"Session timeout: {ArchivesConfig.SESSION_TIMEOUT_HOURS} hours")

    try:
        # Load cache immediately for maximum speed
        logging.info("Loading initial cache...")
        ensure_cache_ready()

        # Start background services
        logging.info("Starting daily refresh scheduler...")
        schedule_daily_refresh()

        logging.info("Starting health monitor...")
        start_health_monitor()

        logging.info("All background services started successfully")
        logging.info("=" * 80)
        logging.info("Server URLs:")
        logging.info("   - http://127.0.0.1:8080")
        logging.info("   - http://localhost:8080")
        logging.info("   - Health Check: http://127.0.0.1:8080/health")
        logging.info(f"Password: {ArchivesConfig.ACCESS_PASSWORD}")
        logging.info("=" * 80)

        # Start Flask app in production mode
        app.run(
            host='0.0.0.0',
            port=8080,
            debug=False,  # Production mode
            use_reloader=False,  # Disable reloader for 24/7 operation
            threaded=True  # Enable threading for better performance
        )

    except Exception as e:
        logging.error(f"Failed to start application: {e}")
        import traceback
        logging.error(traceback.format_exc())
        raise
