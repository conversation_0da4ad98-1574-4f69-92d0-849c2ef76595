#!/usr/bin/env python3
"""
Test what routes exist
"""

import requests

def test_routes():
    """Test what routes exist"""
    base_url = "http://127.0.0.1:8080"
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Test various routes
        routes_to_test = [
            "/",
            "/cache/status", 
            "/cache/refresh",
            "/refresh-cache",
            "/force-refresh-google-sheets",
            "/test"
        ]
        
        print("\nTesting routes:")
        for route in routes_to_test:
            try:
                if route == "/":
                    response = session.get(f"{base_url}{route}")
                else:
                    response = session.post(f"{base_url}{route}")
                print(f"  {route}: {response.status_code}")
            except Exception as e:
                print(f"  {route}: ERROR - {e}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_routes()
