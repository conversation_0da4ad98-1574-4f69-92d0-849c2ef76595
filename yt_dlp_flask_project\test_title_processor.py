#!/usr/bin/env python3
"""
Test the title processor logic independently
"""

import re

class UniversalTitleProcessor:
    """Universal title processor for flexible matching and filename generation - Test Version"""
    
    def __init__(self):
        # Stop words for query matching (not hyphenation)
        self.stop_words = {'of', 'to', 'and', 'a', 'an', 'the', 'in', 'on', 'at', 'for', 'by', 'with'}
        # Special replacements for consistency
        self.special_replacements = {
            "26/11": "26-Nov",
            "&": " and "
        }
        # Words to keep in query matching even if stop words
        self.keep_words = {'is', 'are', 'you', 'your'}

    def _clean_title(self, title: str) -> str:
        """Clean title by removing hashtags, normalizing punctuation, and handling possessives."""
        try:
            clean_title = title.strip()
            if not clean_title:
                return ""
            # Remove hashtags
            clean_title = re.sub(r'#[^\s]+', '', clean_title)
            # Remove unwanted punctuation, keep alphanumeric, spaces, ', &, /, |
            clean_title = re.sub(r'[^\w\s\'&/|]', '', clean_title)
            # Handle possessives dynamically (e.g., World's → Worlds)
            clean_title = re.sub(r"(\w+)'s\b", r"\1s", clean_title, flags=re.IGNORECASE)
            # Apply special replacements
            for old, new in self.special_replacements.items():
                clean_title = clean_title.replace(old, new)
            # Normalize separators
            clean_title = clean_title.replace('|', ' ')
            # Remove [FULL TALK] or similar markers
            clean_title = re.sub(r'\s*(FULL TALK|FULL)\b', '', clean_title, flags=re.IGNORECASE)
            return ' '.join(clean_title.split())  # Remove extra spaces
        except Exception as e:
            print(f"Error cleaning title '{title}': {e}")
            return title

    def _tokenize_and_filter(self, title: str):
        """Tokenize title and return significant words for matching and all words for hyphenation."""
        clean_title = self._clean_title(title)
        if not clean_title:
            return [], []
        words = [word.strip() for word in clean_title.split() if word.strip()]
        significant_words = []
        
        for word in words:
            # Keep numbers, proper nouns, critical words, or non-stop words for matching
            if (re.match(r'^\d+$', word) or 
                word[0].isupper() or 
                word.lower() in self.keep_words or 
                word.lower() not in self.stop_words):
                significant_words.append(word)
        
        return significant_words, words

    def _create_hyphenated_term(self, words: list, extra_title: str = None) -> str:
        """Create hyphenated term from all words or extra title if provided."""
        if extra_title:
            extra_clean = self._clean_title(extra_title)
            hyphenated_words = [word.strip() for word in extra_clean.split() if word.strip()]
        else:
            hyphenated_words = words.copy()
        
        if not hyphenated_words:
            return ""
        
        # Standardize case: Title case for non-stop words, lowercase for stop words
        for i, word in enumerate(hyphenated_words):
            if word.lower() in self.stop_words and word.lower() not in self.keep_words:
                hyphenated_words[i] = word.lower()
            else:
                hyphenated_words[i] = word.title()
        
        return '-'.join(hyphenated_words)

def test_title_processor():
    """Test the title processor with the specific queries"""
    
    print("🔧 TESTING TITLE PROCESSOR LOGIC")
    print("=" * 60)
    
    processor = UniversalTitleProcessor()
    
    test_cases = [
        {
            'title': 'Living Without Regrets',
            'expected_hyphenated': 'Living-Without-Regrets'
        },
        {
            'title': 'An Ambiance of Grace',
            'expected_hyphenated': 'An-Ambiance-of-Grace'
        },
        {
            'title': 'Engineer Yourself for Success',
            'expected_hyphenated': 'Engineer-Yourself-for-Success'
        },
        {
            'title': 'The World\'s Biggest Crisis',
            'expected_hyphenated': 'The-Worlds-Biggest-Crisis'
        },
        {
            'title': 'Can You Conquer Death?',
            'expected_hyphenated': 'Can-You-Conquer-Death'
        },
        {
            'title': 'Do You Imbibe or Expend?',
            'expected_hyphenated': 'Do-You-Imbibe-or-Expend'
        },
        {
            'title': 'How to Stop Fear',
            'expected_hyphenated': 'How-to-Stop-Fear'
        },
        {
            'title': 'Uncertainties Are Great!',
            'expected_hyphenated': 'Uncertainties-Are-Great'
        },
        {
            'title': 'Overcoming Obesity',
            'expected_hyphenated': 'Overcoming-Obesity'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        title = test_case['title']
        expected = test_case['expected_hyphenated']
        
        print(f"\n🧪 TEST {i}: '{title}'")
        
        # Clean the title
        cleaned = processor._clean_title(title)
        print(f"   Cleaned: '{cleaned}'")
        
        # Tokenize and filter
        significant_words, all_words = processor._tokenize_and_filter(title)
        print(f"   Significant words: {significant_words}")
        print(f"   All words: {all_words}")
        
        # Create hyphenated term
        hyphenated = processor._create_hyphenated_term(all_words)
        print(f"   Hyphenated: '{hyphenated}'")
        print(f"   Expected: '{expected}'")
        
        if hyphenated.lower() == expected.lower():
            print(f"   ✅ MATCH!")
        else:
            print(f"   ❌ MISMATCH!")

def test_filename_matching():
    """Test if the hyphenated terms would match actual filenames"""
    
    print(f"\n🎯 TESTING FILENAME MATCHING")
    print("=" * 60)
    
    processor = UniversalTitleProcessor()
    
    # Sample filenames from the cache
    sample_filenames = [
        'Insta-Reels_Living-Without-Regrets_04-Dec-2024_English_01Min-05Secs_Stems',
        'Insta-Reels_An-Ambiance-Of-Grace_23-Nov-2024_English_01Min-13Secs_Stems',
        'Insta-Reels_The-Worlds-Biggest-Crisis_25-Nov-2024_English_01Min_Stems',
        'Insta-Reels_Can-You-Conquer-Death_02-Nov-2024_English_53Secs_Stems',
        'Insta-Reels_Do-You-Imbibe-Or-Expend_02-Nov-2024_English_01Min_Stems',
        'Insta-Reels_How-To-Stop-Fear_09-Oct-2024_English_50Secs_Stems',
        'Insta-Reels_Overcoming-Obesity_19-Nov-2024_English_50Secs_Stems'
    ]
    
    test_queries = [
        'Living Without Regrets',
        'An Ambiance of Grace',
        'The World\'s Biggest Crisis',
        'Can You Conquer Death?',
        'Do You Imbibe or Expend?',
        'How to Stop Fear',
        'Overcoming Obesity'
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        
        # Process query
        significant_words, all_words = processor._tokenize_and_filter(query)
        hyphenated = processor._create_hyphenated_term(all_words)
        
        print(f"   Processed: '{hyphenated}'")
        
        # Check which filenames match
        matches = []
        for filename in sample_filenames:
            filename_parts = filename.split('_')
            if len(filename_parts) >= 2:
                title_part = filename_parts[1]
                if hyphenated.lower() == title_part.lower():
                    matches.append(filename)
                    print(f"   ✅ EXACT MATCH: {filename}")
                elif hyphenated.lower() in title_part.lower():
                    matches.append(filename)
                    print(f"   ✅ PARTIAL MATCH: {filename}")
        
        if not matches:
            print(f"   ❌ NO MATCHES FOUND")

if __name__ == "__main__":
    print("🚀 TITLE PROCESSOR TEST")
    print("Testing the title processing logic independently")
    
    # Test title processor
    test_title_processor()
    
    # Test filename matching
    test_filename_matching()
    
    print("\n" + "=" * 60)
    print("🎯 TITLE PROCESSOR TEST COMPLETE")
    print("=" * 60)
    
    print("💡 This test verifies the title processing logic works correctly")
    print("🔧 If this works, the issue is in the Flask integration")
    
    print("\n🚀 Title processor test complete!")
