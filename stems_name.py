import tkinter as tk
from tkinter import messagebox, ttk, filedialog
import yt_dlp
import pandas as pd
import re
import pyperclip
import time
import threading
import webbrowser
from PIL import Image, ImageTk
import os
import json
import logging
from datetime import datetime
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Setup logging
logging.basicConfig(
    filename='error.log',
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Google Sheets API setup
SCOPES = ['https://www.googleapis.com/auth/spreadsheets.readonly']
CREDENTIALS_PATH = r'D:\Dashboard\Stems Finder\credentials.json'
TOKEN_PATH = r'D:\Dashboard\Stems Finder\token.json'
SHEET_ID = '1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE'

# Check for required dependencies
def check_dependencies():
    missing = []
    for module in ['yt_dlp', 'pandas', 'fuzzywuzzy', 'pyperclip', 'PIL', 'google_auth_oauthlib', 'googleapiclient']:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    if missing:
        error_msg = f"Missing dependencies: {', '.join(missing)}. Please install them using:\n pip install {' '.join(missing)}"
        logging.error(error_msg)
        messagebox.showerror("Dependency Error", error_msg)
        root.quit()

# Function to authenticate with Google Sheets API
def get_sheets_service():
    creds = None
    if os.path.exists(TOKEN_PATH):
        creds = Credentials.from_authorized_user_file(TOKEN_PATH, SCOPES)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            if not os.path.exists(CREDENTIALS_PATH):
                messagebox.showerror("Error", f"Credentials file not found at {CREDENTIALS_PATH}. Please place credentials.json in the directory.")
                logging.error(f"Credentials file not found at {CREDENTIALS_PATH}")
                return None
            flow = InstalledAppFlow.from_client_secrets_file(CREDENTIALS_PATH, SCOPES)
            creds = flow.run_local_server(port=0)
            with open(TOKEN_PATH, 'w') as token:
                token.write(creds.to_json())
    try:
        service = build('sheets', 'v4', credentials=creds)
        return service
    except Exception as e:
        logging.error(f"Error creating Google Sheets service: {str(e)}")
        messagebox.showerror("Error", f"Error creating Google Sheets service: {e}")
        return None

# Function to clean the text
def smart_clean(text):
    if not isinstance(text, str):
        return ""
    text = text.lower()
    text = re.sub(r'[_\-]', ' ', text)
    text = re.sub(r'\d{1,2}[a-z]{0,2}[- ]?(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)[a-z]*[- ]?\d{2,4}', '', text)
    text = re.sub(r'(sharings|stems|english|hindi|tamil|mins|secs|\d{2,4})', '', text)
    text = re.sub(r'[^a-z\s]', '', text)
    return text.strip()

# Function to extract keywords from text
def extract_keywords(text):
    text = smart_clean(text)
    return set(text.split())

# Function to format seconds into minutes and seconds
def format_seconds(hms_string, log_invalid=True):
    try:
        if pd.isna(hms_string) or hms_string is None:
            return "??m ??s", 0
        hms_string = str(hms_string).strip()
        if hms_string.replace('.', '', 1).isdigit():
            total_seconds = int(float(hms_string))
        else:
            parts = hms_string.split(':')
            if len(parts) == 2:
                m, s = map(int, parts)
                total_seconds = m * 60 + s
            elif len(parts) == 3:
                h, m, s = map(int, parts)
                total_seconds = h * 3600 + m * 60 + s
            else:
                raise ValueError("Invalid duration format")
        return f"{total_seconds // 60}m {total_seconds % 60}s", total_seconds
    except Exception as e:
        if log_invalid:
            logging.error(f"Invalid duration format: {hms_string} - {str(e)}")
        return "??m ??s", 0

# Function to get video info from YouTube
def get_video_info(url):
    try:
        ydl_opts = {
            'quiet': True,
            'forcejson': True,
            'noplaylist': True,
            'nocheckcertificate': True
        }
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info_dict = ydl.extract_info(url, download=False)
            title = info_dict.get('title', None)
            duration = info_dict.get('duration', 0)
            duration_str = f"{duration // 60}m {duration % 60}s"
            return title, duration_str, duration
    except Exception as e:
        logging.error(f"Error fetching video info: {str(e)}")
        messagebox.showerror("Error", f"Error fetching video info: {e}")
        return None, None, 0

# Function to load data from Google Sheets
csv_cache = None
def load_csv(sheet_name=None):
    global csv_cache
    if csv_cache is not None and sheet_name is None:
        return csv_cache
    sheets = [
        "Edited Main Sheet",
        "Social Media Catalog(SG)",
        "Social Media Catalog(IF)",
        "Social Media Catalog(IG)",
        "Social Media Catalog(CP)",
        "Copy Social Media Catalog(SG)",
        "Copy Social Media Catalog(IF)",
        "Copy Social Media Catalog(IG)"
    ]
    
    if sheet_name:
        sheets = [sheet_name]  # Only process the specified sheet if provided
    
    service = get_sheets_service()
    if not service:
        return None

    for sheet in sheets:
        try:
            # Get the spreadsheet data
            result = service.spreadsheets().values().get(
                spreadsheetId=SHEET_ID,
                range=f"{sheet}!A:Z"
            ).execute()
            values = result.get('values', [])
            if not values:
                logging.warning(f"Sheet '{sheet}' is empty. Skipping.")
                continue
            
            # Convert to DataFrame
            df = pd.DataFrame(values[1:], columns=values[0])
            
            if 'filename' not in df.columns:
                logging.warning(f"Sheet '{sheet}' does not contain 'filename' column. Skipping.")
                continue
                
            if 'duration' in df.columns:
                invalid_durations = []
                def clean_duration(val):
                    try:
                        if pd.isna(val):
                            return "00:00:00"
                        val_str = str(val).strip()
                        if val_str.replace('.', '', 1).isdigit():
                            return str(int(float(val_str)))
                        parts = val_str.split(':')
                        if len(parts) in [2, 3] and all(part.isdigit() for part in parts):
                            return val_str
                        raise ValueError("Invalid duration")
                    except Exception:
                        invalid_durations.append((val, val_str))
                        return "00:00:00"
                df['duration'] = df['duration'].apply(clean_duration)
                if invalid_durations:
                    logging.warning(f"Found {len(invalid_durations)} invalid durations in sheet '{sheet}': {invalid_durations}")
                    root.after(0, lambda: messagebox.showwarning("Warning", f"Found {len(invalid_durations)} invalid durations in sheet '{sheet}'. Replaced with '00:00:00'. Check error.log for details."))
            if 'drive_link' in df.columns:
                df['drive_link'] = df['drive_link'].astype(str).replace('nan', '')
            if sheet_name:
                csv_cache = df  # Cache the specific sheet if requested
            return df
        except HttpError as e:
            logging.error(f"Error loading sheet '{sheet}': {str(e)}")
            if sheet_name:
                messagebox.showerror("Error", f"Error loading sheet '{sheet}': {e}")
            continue
    if not sheet_name:
        logging.error("No valid sheets found with required columns.")
        messagebox.showerror("Error", "No valid sheets found with required columns.")
        return None

# Function to validate CSV
def validate_csv():
    report = []
    sheets = [
        "Edited Main Sheet",
        "Social Media Catalog(SG)",
        "Social Media Catalog(IF)",
        "Social Media Catalog(IG)",
        "Social Media Catalog(CP)",
        "Copy Social Media Catalog(SG)",
        "Copy Social Media Catalog(IF)",
        "Copy Social Media Catalog(IG)"
    ]
    for sheet in sheets:
        df = load_csv(sheet_name=sheet)
        if df is None:
            continue
        for idx, row in df.iterrows():
            filename = row.get('filename', '')
            duration = row.get('duration', '')
            file_duration_fmt, _ = format_seconds(duration, log_invalid=False)
            if file_duration_fmt == "??m ??s" and str(duration) != "00:00:00":
                report.append((sheet, filename, duration))
    return report

# Function to process batch URLs
def process_batch_urls(urls, top_n=5):
    all_matches = []
    for url in urls:
        url = url.strip()
        if not url or url == "Enter YouTube URL here":
            continue
        if not (url.startswith("https://www.youtube.com/") or url.startswith("https://youtu.be/")):
            logging.warning(f"Invalid URL skipped: {url}")
            continue
        video_title, yt_duration, yt_duration_seconds = get_video_info(url)
        if not video_title:
            continue
        matches = match_filename(video_title, top_n=top_n, yt_duration_seconds=yt_duration_seconds)
        all_matches.append((url, video_title, yt_duration, matches))
    return all_matches

# Function to match filenames from Google Sheets
def match_filename(video_title, top_n=5, yt_duration_seconds=0):
    from fuzzywuzzy import fuzz
    sheets = [
        "Edited Main Sheet",
        "Social Media Catalog(SG)",
        "Social Media Catalog(IF)",
        "Social Media Catalog(IG)",
        "Social Media Catalog(CP)",
        "Copy Social Media Catalog(SG)",
        "Copy Social Media Catalog(IF)",
        "Copy Social Media Catalog(IG)"
    ]
    
    video_clean = smart_clean(video_title)
    video_keywords = extract_keywords(video_title)
    match_results = []
    invalid_durations = []

    for sheet in sheets:
        df = load_csv(sheet_name=sheet)
        if df is None:
            continue
        if 'filename' not in df.columns or 'duration' not in df.columns or 'drive_link' not in df.columns:
            logging.warning(f"Sheet '{sheet}' missing required columns. Skipping.")
            continue

        filenames = df['filename'].fillna('').tolist()
        durations = df['duration'].fillna("00:00:00").tolist()
        drive_links = df['drive_link'].fillna('').tolist()

        for filename, file_duration, drive_link in zip(filenames, durations, drive_links):
            file_clean = smart_clean(filename)
            file_keywords = extract_keywords(filename)
            keyword_overlap = len(video_keywords.intersection(file_keywords))
            token_score = fuzz.token_set_ratio(video_clean, file_clean)
            file_duration_fmt, file_seconds = format_seconds(file_duration, log_invalid=False)
            if file_duration_fmt == "??m ??s" and file_duration != "00:00:00":
                invalid_durations.append((sheet, filename, file_duration))
            duration_diff = abs(yt_duration_seconds - file_seconds)
            duration_score = max(0, 100 - (duration_diff * 5))
            combined_score = token_score + (keyword_overlap * 5) + duration_score
            match_results.append((f"{sheet}: {filename}", combined_score, file_duration, drive_link))

        if match_results:  # If matches are found in this sheet, stop searching
            break

    if invalid_durations:
        logging.warning(f"Invalid durations in match_filename: {invalid_durations}")
        root.after(0, lambda: messagebox.showwarning("Warning", f"Found {len(invalid_durations)} invalid durations in sheets. Replaced with '??m ??s'. Check error.log for details."))

    match_results.sort(key=lambda x: x[1], reverse=True)
    return match_results[:top_n]

# Function to truncate long text for display
def truncate_text(text, max_length=30):
    if not isinstance(text, str):
        text = str(text) if text is not None else ""
    return text if len(text) <= max_length else text[:max_length-3] + "..."

# Theme management
class Theme:
    def __init__(self):
        self.is_dark = False
        self.light_colors = {
            'bg': '#f5f5f5',
            'fg': '#333333',
            'entry_bg': '#ffffff',
            'button_bg': '#4CAF50',
            'button_fg': '#ffffff',
            'hover_bg': '#45a049',
            'result_bg': '#e0e0e0',
            'frame_bg': '#ffffff',
            'table_bg': '#ffffff',
            'table_alt': '#f0f0f0'
        }
        self.dark_colors = {
            'bg': '#2d2d2d',
            'fg': '#e0e0e0',
            'entry_bg': '#3c3c3c',
            'button_bg': '#4CAF50',
            'button_fg': '#ffffff',
            'hover_bg': '#45a049',
            'result_bg': '#4a4a4a',
            'frame_bg': '#3c3c3c',
            'table_bg': '#3c3c3c',
            'table_alt': '#4a4a4a'
        }

    def get_colors(self):
        return self.dark_colors if self.is_dark else self.light_colors

    def toggle(self):
        self.is_dark = not self.is_dark
        apply_theme()

# Settings management
class Settings:
    def __init__(self):
        self.filename = 'settings.json'
        self.defaults = {
            'font_size': 'medium',
            'theme': 'light',
            'result_limit': 5,
            'csv_source': 'https://docs.google.com/spreadsheets/d/1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE/export?format=csv'
        }
        self.settings = self.load_settings()

    def load_settings(self):
        try:
            if os.path.exists(self.filename):
                with open(self.filename, 'r') as f:
                    return json.load(f)
            return self.defaults
        except Exception as e:
            logging.error(f"Error loading settings: {str(e)}")
            return self.defaults

    def save_settings(self):
        try:
            with open(self.filename, 'w') as f:
                json.dump(self.settings, f, indent=4)
        except Exception as e:
            logging.error(f"Error saving settings: {str(e)}")

    def get_font_size(self):
        sizes = {'small': 12, 'medium': 14, 'large': 16}
        return sizes.get(self.settings['font_size'], 14)

    def get_result_limit(self):
        return self.settings['result_limit']

    def get_csv_source(self):
        return self.settings['csv_source']

    def update(self, key, value):
        self.settings[key] = value
        self.save_settings()

# Apply theme to widgets
def apply_theme():
    colors = theme.get_colors()
    font_size = settings.get_font_size()
    root.configure(bg=colors['bg'])
    main_frame.configure(bg=colors['bg'])
    url_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    url_entry.configure(bg=colors['entry_bg'], fg=colors['fg'], font=("Roboto", font_size))
    match_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size))
    search_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    search_entry.configure(bg=colors['entry_bg'], fg=colors['fg'], font=("Roboto", font_size))
    search_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size))
    ocd_vp_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    ocd_vp_entry.configure(bg=colors['entry_bg'], fg=colors['fg'], font=("Roboto", font_size))
    ocd_vp_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size))
    video_id_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    video_id_entry.configure(bg=colors['entry_bg'], fg=colors['fg'], font=("Roboto", font_size))
    video_id_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size))
    theme_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size-2))
    settings_button.configure(bg=colors['button_bg'], fg=colors['button_fg'], activebackground=colors['hover_bg'], font=("Roboto", font_size-2))
    score_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size))
    result_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size+2, 'bold'))
    timer_label.configure(bg=colors['bg'], fg=colors['fg'], font=("Roboto", font_size-2))
    canvas.configure(bg=colors['frame_bg'])
    tree.configure(style="Custom.Treeview")
    style.configure("Custom.Treeview", background=colors['table_bg'], foreground=colors['fg'], font=("Roboto", font_size-2))
    style.configure("Custom.Treeview.Heading", background=colors['table_bg'], foreground=colors['fg'], font=("Roboto", font_size, 'bold'))

# Load icons
def load_icon(filename, size=(20, 20)):
    try:
        path = os.path.join("icons", filename)
        if os.path.exists(path):
            img = Image.open(path).resize(size, Image.LANCZOS)
            return ImageTk.PhotoImage(img)
    except Exception as e:
        logging.error(f"Error loading icon {filename}: {str(e)}")
    return None

# Settings panel
def open_settings():
    dialog = tk.Toplevel(root)
    dialog.title("Settings")
    dialog.geometry("400x450")
    dialog.transient(root)
    dialog.grab_set()
    dialog.configure(bg=theme.get_colors()['bg'])

    tk.Label(dialog, text="Settings", font=("Roboto", 16, 'bold'), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(pady=10)

    tk.Label(dialog, text="Font Size:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    font_var = tk.StringVar(value=settings.settings['font_size'])
    font_menu = ttk.Combobox(dialog, textvariable=font_var, values=['small', 'medium', 'large'], state='readonly', font=("Roboto", 12))
    font_menu.pack(fill="x", padx=20, pady=5)

    tk.Label(dialog, text="Theme:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    theme_var = tk.StringVar(value=settings.settings['theme'])
    theme_menu = ttk.Combobox(dialog, textvariable=theme_var, values=['light', 'dark'], state='readonly', font=("Roboto", 12))
    theme_menu.pack(fill="x", padx=20, pady=5)

    tk.Label(dialog, text="Result Limit:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    limit_var = tk.IntVar(value=settings.settings['result_limit'])
    limit_spin = tk.Spinbox(dialog, from_=1, to=10, textvariable=limit_var, font=("Roboto", 12))
    limit_spin.pack(fill="x", padx=20, pady=5)

    tk.Label(dialog, text="CSV Source:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    csv_var = tk.StringVar(value=settings.settings['csv_source'])
    csv_entry = tk.Entry(dialog, textvariable=csv_var, font=("Roboto", 12))
    csv_entry.pack(fill="x", padx=20, pady=5)
    tk.Button(dialog, text="Browse", command=lambda: csv_var.set(tk.filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])), font=("Roboto", 12)).pack(anchor="w", padx=20)

    tk.Label(dialog, text="Batch URL File:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    def load_urls():
        file_path = tk.filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    urls = f.read()
                url_entry.delete(0, tk.END)
                url_entry.insert(0, urls)
                url_entry.config(foreground=theme.get_colors()['fg'])
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load URLs: {e}")
    tk.Button(dialog, text="Load URLs", command=load_urls, font=("Roboto", 12)).pack(anchor="w", padx=20)

    tk.Label(dialog, text="CSV Validation:", font=("Roboto", 12), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg']).pack(anchor="w", padx=20)
    def generate_report():
        report = validate_csv()
        if not report:
            messagebox.showinfo("Validation", "No invalid durations found in sheets.")
            return
        report_file = os.path.join("D:\\Dashboard\\Stems Finder", f"csv_validation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        with open(report_file, 'w') as f:
            f.write("CSV Validation Report\n")
            f.write(f"Generated: {datetime.now()}\n")
            f.write(f"Sheet ID: {SHEET_ID}\n\n")
            f.write("Invalid Durations:\n")
            for sheet, filename, duration in report:
                f.write(f"Sheet: {sheet}, Filename: {filename}, Duration: {duration}\n")
        messagebox.showinfo("Validation", f"Found {len(report)} invalid durations. Report saved to {report_file}.")
    tk.Button(dialog, text="Generate Report", command=generate_report, font=("Roboto", 12)).pack(anchor="w", padx=20)

    def save():
        settings.update('font_size', font_var.get())
        settings.update('theme', theme_var.get())
        settings.update('result_limit', limit_var.get())
        settings.update('csv_source', csv_var.get())
        global csv_cache
        csv_cache = None
        if theme_var.get() == 'dark' and not theme.is_dark:
            theme.toggle()
        elif theme_var.get() == 'light' and theme.is_dark:
            theme.toggle()
        apply_theme()
        dialog.destroy()

    btn_frame = tk.Frame(dialog, bg=theme.get_colors()['bg'])
    btn_frame.pack(pady=20)
    tk.Button(btn_frame, text="Save", command=save, font=("Roboto", 12), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat").pack(side="left", padx=5)
    tk.Button(btn_frame, text="Cancel", command=dialog.destroy, font=("Roboto", 12), bg='#d32f2f', fg='#ffffff', activebackground='#b71c1c', relief="flat").pack(side="left", padx=5)

# Function to search by OCD/VP number
def search_by_ocd_vp():
    ocd_vp = ocd_vp_entry.get().strip()
    if not ocd_vp or ocd_vp == "Enter OCD/VP number":
        messagebox.showwarning("Input", "Please enter an OCD/VP number.")
        ocd_vp_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
        root.after(2000, lambda: ocd_vp_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
        return

    def run_task():
        start_time = time.time()
        sheets = [
            "Edited Main Sheet",
            "Social Media Catalog(SG)",
            "Social Media Catalog(IF)",
            "Social Media Catalog(IG)",
            "Social Media Catalog(CP)",
            "Copy Social Media Catalog(SG)",
            "Copy Social Media Catalog(IF)",
            "Copy Social Media Catalog(IG)"
        ]
        matches = pd.DataFrame()
        for sheet in sheets:
            df = load_csv(sheet_name=sheet)
            if df is None:
                continue
            if 'filename' not in df.columns or 'duration' not in df.columns or 'ocd_vp' not in df.columns or 'drive_link' not in df.columns:
                logging.warning(f"Sheet '{sheet}' missing required columns. Skipping.")
                continue
            sheet_matches = df[df['ocd_vp'].astype(str).str.strip().str.lower() == ocd_vp.lower()]
            if not sheet_matches.empty:
                matches = sheet_matches
                break

        root.after(0, lambda: animate_progress(100 if not matches.empty else 0))
        root.after(0, lambda: score_label.configure(text=f"Best Match Score: {100 if not matches.empty else 0}%"))

        root.after(0, lambda: tree.delete(*tree.get_children()))
        invalid_durations = []
        for idx, row in matches.iterrows():
            filename = row['filename']
            file_duration = row['duration']
            drive_link = row['drive_link']
            file_duration_fmt, _ = format_seconds(file_duration, log_invalid=False)
            if file_duration_fmt == "??m ??s" and file_duration != "00:00:00":
                invalid_durations.append((sheet, filename, file_duration))
            tag = 'even' if idx % 2 == 0 else 'odd'
            tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link)), tags=(tag, filename, drive_link))

        if invalid_durations:
            logging.warning(f"Invalid durations in search_by_ocd_vp: {invalid_durations}")
            root.after(0, lambda: messagebox.showwarning("Warning", f"Found {len(invalid_durations)} invalid durations in sheets. Replaced with '??m ??s'. Check error.log for details."))

        end_time = time.time()
        elapsed_time = round(end_time - start_time, 2)
        root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

    threading.Thread(target=run_task, daemon=True).start()

# Function to search by Video ID
def search_by_video_id():
    video_id = video_id_entry.get().strip()
    if not video_id or video_id == "Enter Video ID":
        messagebox.showwarning("Input", "Please enter a Video ID.")
        video_id_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
        root.after(2000, lambda: video_id_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
        return

    def run_task():
        start_time = time.time()
        sheets = [
            "Edited Main Sheet",
            "Social Media Catalog(SG)",
            "Social Media Catalog(IF)",
            "Social Media Catalog(IG)",
            "Social Media Catalog(CP)",
            "Copy Social Media Catalog(SG)",
            "Copy Social Media Catalog(IF)",
            "Copy Social Media Catalog(IG)"
        ]
        matches = pd.DataFrame()
        for sheet in sheets:
            df = load_csv(sheet_name=sheet)
            if df is None:
                continue
            if 'filename' not in df.columns or 'duration' not in df.columns or 'video_id' not in df.columns or 'drive_link' not in df.columns:
                logging.warning(f"Sheet '{sheet}' missing required columns. Skipping.")
                continue
            sheet_matches = df[df['video_id'].astype(str).str.strip().str.lower() == video_id.lower()]
            if not sheet_matches.empty:
                matches = sheet_matches
                break

        root.after(0, lambda: animate_progress(100 if not matches.empty else 0))
        root.after(0, lambda: score_label.configure(text=f"Best Match Score: {100 if not matches.empty else 0}%"))

        root.after(0, lambda: tree.delete(*tree.get_children()))
        invalid_durations = []
        for idx, row in matches.iterrows():
            filename = row['filename']
            file_duration = row['duration']
            drive_link = row['drive_link']
            file_duration_fmt, _ = format_seconds(file_duration, log_invalid=False)
            if file_duration_fmt == "??m ??s" and file_duration != "00:00:00":
                invalid_durations.append((sheet, filename, file_duration))
            tag = 'even' if idx % 2 == 0 else 'odd'
            tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link)), tags=(tag, filename, drive_link))

        if invalid_durations:
            logging.warning(f"Invalid durations in search_by_video_id: {invalid_durations}")
            root.after(0, lambda: messagebox.showwarning("Warning", f"Found {len(invalid_durations)} invalid durations in sheets. Replaced with '??m ??s'. Check error.log for details."))

        end_time = time.time()
        elapsed_time = round(end_time - start_time, 2)
        root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

    threading.Thread(target=run_task, daemon=True).start()

# Function to search by title
def search_by_title():
    search_term = search_entry.get().strip()
    if not search_term or search_term == "Enter title or keyword":
        messagebox.showwarning("Input", "Please enter a search term.")
        search_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
        root.after(2000, lambda: search_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
        return

    def run_task():
        start_time = time.time()
        top_matches = match_filename(search_term, top_n=settings.get_result_limit())

        root.after(0, lambda: tree.delete(*tree.get_children()))
        for idx, (filename, score, file_duration, drive_link) in enumerate(top_matches):
            file_duration_fmt, _ = format_seconds(file_duration, log_invalid=False)
            tag = 'even' if idx % 2 == 0 else 'odd'
            tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link)), tags=(tag, filename, drive_link))

        end_time = time.time()
        elapsed_time = round(end_time - start_time, 2)
        root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

    threading.Thread(target=run_task, daemon=True).start()

# Function to handle match click event
def on_match_click():
    url_input = url_entry.get().strip()
    if not url_input or url_input == "Enter YouTube URL here":
        messagebox.showwarning("Input", "Please paste a valid YouTube URL.")
        url_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
        root.after(2000, lambda: url_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
        return

    urls = url_input.split('\n')
    if len(urls) > 1:
        def run_task():
            start_time = time.time()
            top_n = settings.get_result_limit()
            all_matches = process_batch_urls(urls, top_n=top_n)

            root.after(0, lambda: tree.delete(*tree.get_children()))
            for url, video_title, yt_duration, matches in all_matches:
                root.after(0, lambda: result_label.configure(text=f"🎥 Last Processed: {video_title}\n🕒 YT Duration: {yt_duration}"))
                yt_duration_seconds = sum(int(x) * 60 ** i for i, x in enumerate(reversed(yt_duration.replace('m', '').replace('s', '').split())) if x.strip())
                for idx, (filename, score, file_duration, drive_link) in enumerate(matches):
                    file_duration_fmt, file_duration_seconds = format_seconds(file_duration, log_invalid=False)
                    duration_diff = abs(yt_duration_seconds - file_duration_seconds)
                    color = "green" if duration_diff <= 2 else "#90EE90" if duration_diff <= 7 else "red"
                    tag = 'even' if idx % 2 == 0 else 'odd'
                    tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link), yt_duration), tags=(tag, filename, drive_link, color))
                root.after(0, lambda: animate_progress(matches[0][1] if matches else 0))
                root.after(0, lambda: score_label.configure(text=f"Best Match Score: {matches[0][1] if matches else 0}%"))

            end_time = time.time()
            elapsed_time = round(end_time - start_time, 2)
            root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

        threading.Thread(target=run_task, daemon=True).start()
    else:
        url = urls[0]
        if not (url.startswith("https://www.youtube.com/") or url.startswith("https://youtu.be/")):
            messagebox.showwarning("Input", "Please enter a valid YouTube URL (e.g., https://www.youtube.com/watch?v=...).")
            url_entry.configure(highlightbackground='red', highlightcolor='red', highlightthickness=2)
            root.after(2000, lambda: url_entry.configure(highlightbackground='gray', highlightcolor='gray', highlightthickness=1))
            return

        def run_task():
            start_time = time.time()
            video_title, yt_duration, yt_duration_seconds = get_video_info(url)
            if not video_title:
                return

            root.after(0, lambda: result_label.configure(text=f"🎥 Title: {video_title}\n🕒 YT Duration: {yt_duration}"))
            top_matches = match_filename(video_title, top_n=settings.get_result_limit(), yt_duration_seconds=yt_duration_seconds)

            root.after(0, lambda: animate_progress(top_matches[0][1] if top_matches else 0))
            root.after(0, lambda: score_label.configure(text=f"Best Match Score: {top_matches[0][1] if top_matches else 0}%"))

            root.after(0, lambda: tree.delete(*tree.get_children()))
            for idx, (filename, score, file_duration, drive_link) in enumerate(top_matches):
                file_duration_fmt, file_duration_seconds = format_seconds(file_duration, log_invalid=False)
                duration_diff = abs(yt_duration_seconds - file_duration_seconds)
                color = "green" if duration_diff <= 2 else "#90EE90" if duration_diff <= 7 else "red"
                tag = 'even' if idx % 2 == 0 else 'odd'
                tree.insert("", "end", values=(filename, file_duration_fmt, truncate_text(drive_link), yt_duration), tags=(tag, filename, drive_link, color))

            end_time = time.time()
            elapsed_time = round(end_time - start_time, 2)
            root.after(0, lambda: timer_label.configure(text=f"Task completed in: {elapsed_time} seconds"))

        threading.Thread(target=run_task, daemon=True).start()

# Function to handle match selection
def on_match_select(event):
    item = tree.selection()
    if not item:
        return
    tags = tree.item(item, "tags")
    if 'error' in tags:
        return
    filename = tags[1]
    drive_link = tags[2]

    dialog = tk.Toplevel(root)
    dialog.title("Copy Selection")
    dialog.geometry("400x250")
    dialog.transient(root)
    dialog.grab_set()
    dialog.configure(bg=theme.get_colors()['bg'])

    tk.Label(dialog, text="What would you like to copy?", font=("Roboto", 16), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'], image=copy_icon, compound="left").pack(pady=20)

    def copy_filename():
        pyperclip.copy(filename)
        messagebox.showinfo("Copied", f"Copied: {truncate_text(filename, 50)}")
        dialog.destroy()

    def copy_drive_link():
        pyperclip.copy(drive_link)
        messagebox.showinfo("Copied", f"Copied: {truncate_text(drive_link, 50)}")
        dialog.destroy()

    def cancel():
        dialog.destroy()

    btn_frame = tk.Frame(dialog, bg=theme.get_colors()['bg'])
    btn_frame.pack(pady=20)
    tk.Button(btn_frame, text="Copy Filename", command=copy_filename, font=("Roboto", 12), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", image=copy_icon, compound="left").pack(side="left", padx=5)
    tk.Button(btn_frame, text="Copy Drive Link", command=copy_drive_link, font=("Roboto", 12), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", image=link_icon, compound="left").pack(side="left", padx=5)
    tk.Button(btn_frame, text="Cancel", command=cancel, font=("Roboto", 12), bg='#d32f2f', fg='#ffffff', activebackground='#b71c1c', relief="flat").pack(side="left", padx=5)

    dialog.bind('<Return>', lambda e: copy_filename())
    dialog.bind('d', lambda e: copy_drive_link())
    dialog.bind('<Escape>', lambda e: cancel())

    dialog.update_idletasks()
    x = root.winfo_x() + (root.winfo_width() - dialog.winfo_width()) // 2
    y = root.winfo_y() + (root.winfo_height() - dialog.winfo_height()) // 2
    dialog.geometry(f"+{x}+{y}")

# Function to handle drive link click
def on_drive_link_click(event):
    item = tree.selection()
    if not item:
        return
    tags = tree.item(item, "tags")
    if 'error' in tags:
        return
    drive_link = tags[2]
    webbrowser.open(drive_link)

# Function to animate progress bar
def animate_progress(target_value, current=0, steps=20, delay=50):
    if current >= target_value:
        score_bar.configure(value=target_value)
        return
    increment = (target_value - current) / steps
    score_bar.configure(value=current + increment)
    root.after(delay, lambda: animate_progress(target_value, current + increment, steps, delay))

# Function to sort table by column
def treeview_sort_column(tree, col, reverse):
    data = [(tree.set(child, col), child) for child in tree.get_children()]
    try:
        data.sort(key=lambda x: float(x[0]) if x[0].replace('.', '', 1).isdigit() else x[0], reverse=reverse)
    except:
        data.sort(reverse=reverse)
    for index, (_, child) in enumerate(data):
        tree.move(child, '', index)
    tree.heading(col, command=lambda: treeview_sort_column(tree, col, not reverse))

# ---------------------- GUI SETUP ----------------------

root = tk.Tk()
root.title("🎬 YouTube Title to Stems Matcher")
root.geometry("1200x900")
root.minsize(800, 600)

theme = Theme()
settings = Settings()

# Load icons
search_icon = load_icon("search.png") or tk.PhotoImage(width=20, height=20)
youtube_icon = load_icon("youtube.png") or tk.PhotoImage(width=20, height=20)
link_icon = load_icon("link.png") or tk.PhotoImage(width=20, height=20)
settings_icon = load_icon("settings.png") or tk.PhotoImage(width=20, height=20)
copy_icon = load_icon("copy.png") or tk.PhotoImage(width=20, height=20)

# Configure grid weights for resizing
root.grid_rowconfigure(0, weight=1)
root.grid_columnconfigure(0, weight=1)

# Check dependencies
check_dependencies()

# Main frame with padding
main_frame = tk.Frame(root, bg=theme.get_colors()['bg'], padx=20, pady=20)
main_frame.grid(row=0, column=0, sticky="nsew")
main_frame.grid_rowconfigure(11, weight=1)
main_frame.grid_columnconfigure(0, weight=1)

# Placeholder handling for entries
def setup_entry(entry, placeholder):
    entry.insert(0, placeholder)
    entry.config(foreground='grey')
    def on_focus_in(event):
        if entry.get() == placeholder:
            entry.delete(0, tk.END)
            entry.config(foreground=theme.get_colors()['fg'])
    def on_focus_out(event):
        if not entry.get():
            entry.insert(0, placeholder)
            entry.config(foreground='grey')
    entry.bind("<FocusIn>", on_focus_in)
    entry.bind("<FocusOut>", on_focus_out)

# Paste YouTube URL section
url_label = tk.Label(main_frame, text="🔗 Paste YouTube URL:", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'], image=youtube_icon, compound="left")
url_label.grid(row=0, column=0, sticky="w", pady=(0, 5))

url_entry = tk.Entry(main_frame, width=60, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['entry_bg'], fg=theme.get_colors()['fg'], relief="flat", highlightthickness=1, highlightbackground='gray')
url_entry.grid(row=1, column=0, sticky="ew", pady=5)
setup_entry(url_entry, "Enter YouTube URL here")
url_entry.bind("<Return>", lambda e: on_match_click())

match_button = tk.Button(main_frame, text="Match Filename", command=on_match_click, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=search_icon, compound="left")
match_button.grid(row=2, column=0, pady=10)
match_button.bind("<Enter>", lambda e: match_button.configure(bg=theme.get_colors()['hover_bg']))
match_button.bind("<Leave>", lambda e: match_button.configure(bg=theme.get_colors()['button_bg']))

# Search by title/keyword section
search_label = tk.Label(main_frame, text="📝 Search by Title/Keyword:", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
search_label.grid(row=3, column=0, sticky="w", pady=(10, 5))

search_entry = tk.Entry(main_frame, width=60, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['entry_bg'], fg=theme.get_colors()['fg'], relief="flat", highlightthickness=1, highlightbackground='gray')
search_entry.grid(row=4, column=0, sticky="ew", pady=5)
setup_entry(search_entry, "Enter title or keyword")
search_entry.bind("<Return>", lambda e: search_by_title())

search_button = tk.Button(main_frame, text="Search", command=search_by_title, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=search_icon, compound="left")
search_button.grid(row=5, column=0, pady=10)
search_button.bind("<Enter>", lambda e: search_button.configure(bg=theme.get_colors()['hover_bg']))
search_button.bind("<Leave>", lambda e: search_button.configure(bg=theme.get_colors()['button_bg']))

# Search by OCD/VP number section
ocd_vp_label = tk.Label(main_frame, text="🔢 Search by OCD/VP Number:", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
ocd_vp_label.grid(row=6, column=0, sticky="w", pady=(10, 5))

ocd_vp_entry = tk.Entry(main_frame, width=60, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['entry_bg'], fg=theme.get_colors()['fg'], relief="flat", highlightthickness=1, highlightbackground='gray')
ocd_vp_entry.grid(row=7, column=0, sticky="ew", pady=5)
setup_entry(ocd_vp_entry, "Enter OCD/VP number")
ocd_vp_entry.bind("<Return>", lambda e: search_by_ocd_vp())

ocd_vp_button = tk.Button(main_frame, text="Search OCD/VP", command=search_by_ocd_vp, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=search_icon, compound="left")
ocd_vp_button.grid(row=8, column=0, pady=10)
ocd_vp_button.bind("<Enter>", lambda e: ocd_vp_button.configure(bg=theme.get_colors()['hover_bg']))
ocd_vp_button.bind("<Leave>", lambda e: ocd_vp_button.configure(bg=theme.get_colors()['button_bg']))

# Search by Video ID section
video_id_label = tk.Label(main_frame, text="🎥 Search by Video ID:", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
video_id_label.grid(row=9, column=0, sticky="w", pady=(10, 5))

video_id_entry = tk.Entry(main_frame, width=60, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['entry_bg'], fg=theme.get_colors()['fg'], relief="flat", highlightthickness=1, highlightbackground='gray')
video_id_entry.grid(row=10, column=0, sticky="ew", pady=5)
setup_entry(video_id_entry, "Enter Video ID")
video_id_entry.bind("<Return>", lambda e: search_by_video_id())

video_id_button = tk.Button(main_frame, text="Search Video ID", command=search_by_video_id, font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=search_icon, compound="left")
video_id_button.grid(row=11, column=0, pady=10)
video_id_button.bind("<Enter>", lambda e: video_id_button.configure(bg=theme.get_colors()['hover_bg']))
video_id_button.bind("<Leave>", lambda e: video_id_button.configure(bg=theme.get_colors()['button_bg']))

# Theme and settings buttons
button_frame = tk.Frame(main_frame, bg=theme.get_colors()['bg'])
button_frame.grid(row=12, column=0, sticky="e", pady=10)
theme_button = tk.Button(button_frame, text="🌙 Toggle Theme", command=theme.toggle, font=("Roboto", settings.get_font_size()-2), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5)
theme_button.pack(side="left", padx=5)
theme_button.bind("<Enter>", lambda e: theme_button.configure(bg=theme.get_colors()['hover_bg']))
theme_button.bind("<Leave>", lambda e: theme_button.configure(bg=theme.get_colors()['button_bg']))

settings_button = tk.Button(button_frame, text="⚙️ Settings", command=open_settings, font=("Roboto", settings.get_font_size()-2), bg=theme.get_colors()['button_bg'], fg=theme.get_colors()['button_fg'], activebackground=theme.get_colors()['hover_bg'], relief="flat", padx=10, pady=5, image=settings_icon, compound="left")
settings_button.pack(side="left", padx=5)
settings_button.bind("<Enter>", lambda e: settings_button.configure(bg=theme.get_colors()['hover_bg']))
settings_button.bind("<Leave>", lambda e: settings_button.configure(bg=theme.get_colors()['button_bg']))

# Match Score section
score_label = tk.Label(main_frame, text="Best Match Score: 0%", font=("Roboto", settings.get_font_size()), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
score_label.grid(row=13, column=0, pady=5)

score_bar = ttk.Progressbar(main_frame, orient="horizontal", length=500, mode="determinate", maximum=100)
score_bar.grid(row=14, column=0, pady=5)

# Result and timer section
result_label = tk.Label(main_frame, text="YT Title and Duration will appear here.", wraplength=700, justify="center", font=("Roboto", settings.get_font_size()+2, 'bold'), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
result_label.grid(row=15, column=0, pady=10)

# Table view for results
style = ttk.Style()
style.configure("Custom.Treeview", rowheight=40)
style.configure("Custom.Treeview.Heading", font=("Roboto", settings.get_font_size(), 'bold'))
canvas = tk.Canvas(main_frame, bg=theme.get_colors()['frame_bg'], highlightthickness=1, highlightbackground='#cccccc')
scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
tree = ttk.Treeview(canvas, columns=("Filename", "Duration", "Drive Link", "YT Duration"), show="headings", style="Custom.Treeview")

tree.heading("Filename", text="Filename", command=lambda: treeview_sort_column(tree, "Filename", False))
tree.heading("Duration", text="Duration", command=lambda: treeview_sort_column(tree, "Duration", False))
tree.heading("Drive Link", text="Drive Link", command=lambda: treeview_sort_column(tree, "Drive Link", False))
tree.heading("YT Duration", text="YT Duration", command=lambda: treeview_sort_column(tree, "YT Duration", False))

tree.column("Filename", width=400)
tree.column("Duration", width=100)
tree.column("Drive Link", width=200)
tree.column("YT Duration", width=100)

tree.tag_configure('even', background=theme.get_colors()['table_bg'])
tree.tag_configure('odd', background=theme.get_colors()['table_alt'])
tree.tag_configure('error', background='#ffcccc', foreground='#ff0000')

canvas.create_window((0, 0), window=tree, anchor="nw")
canvas.configure(yscrollcommand=scrollbar.set)

canvas.grid(row=16, column=0, sticky="nsew", pady=10)
scrollbar.grid(row=16, column=1, sticky="ns")

def update_scrollregion(event=None):
    canvas.configure(scrollregion=canvas.bbox("all"))

tree.bind("<Configure>", update_scrollregion)
tree.bind("<<TreeviewSelect>>", on_match_select)
tree.bind("<Double-1>", on_drive_link_click)

timer_label = tk.Label(main_frame, text="Task completed in: 0 seconds", font=("Roboto", settings.get_font_size()-2), bg=theme.get_colors()['bg'], fg=theme.get_colors()['fg'])
timer_label.grid(row=17, column=0, pady=5)

# Apply initial theme
apply_theme()

# Start the main loop
root.mainloop()