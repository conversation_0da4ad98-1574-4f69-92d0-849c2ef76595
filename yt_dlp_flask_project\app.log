2025-06-30 20:56:34,315 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 20:56:34,317 - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 20:57:07,078 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:07] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 20:57:07,370 - ERROR - Exception on /login [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\app.py", line 165, in login
    return google.authorize_redirect(redirect_uri, state=session['oauth_state'])
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\flask_client\apps.py", line 49, in authorize_redirect
    rv = self.create_authorization_url(redirect_uri, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\base_client\sync_app.py", line 341, in create_authorization_url
    metadata = self.load_server_metadata()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\base_client\sync_app.py", line 327, in load_server_metadata
    resp.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://accounts.google.com/.well-known/openid_configuration
2025-06-30 20:57:07,526 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:07] "[35m[1mGET /login HTTP/1.1[0m" 500 -
2025-06-30 20:57:07,841 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:07] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-30 20:57:18,220 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:18] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 20:57:18,392 - ERROR - Exception on /login [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\app.py", line 165, in login
    return google.authorize_redirect(redirect_uri, state=session['oauth_state'])
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\flask_client\apps.py", line 49, in authorize_redirect
    rv = self.create_authorization_url(redirect_uri, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\base_client\sync_app.py", line 341, in create_authorization_url
    metadata = self.load_server_metadata()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\base_client\sync_app.py", line 327, in load_server_metadata
    resp.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://accounts.google.com/.well-known/openid_configuration
2025-06-30 20:57:18,400 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:18] "[35m[1mGET /login HTTP/1.1[0m" 500 -
2025-06-30 20:59:07,251 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 20:59:07,251 - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 20:59:23,449 - INFO - 127.0.0.1 - - [30/Jun/2025 20:59:23] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 20:59:23,684 - INFO - 127.0.0.1 - - [30/Jun/2025 20:59:23] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 20:59:47,342 - INFO - 127.0.0.1 - - [30/Jun/2025 20:59:47] "GET /auth/callback?state=PS01iummwK3YWo6qqieNuRlNce0MSn&code=4/0AVMBsJgt-y2hwvgMORzkdYk2zcfDnxAMqzZGlCDKz4aYHGjgHi9dr2CM9-SkeLfcqxr-Lw&scope=email+profile+https://www.googleapis.com/auth/userinfo.profile+https://www.googleapis.com/auth/userinfo.email+openid&authuser=0&hd=sadhguru.org&prompt=consent HTTP/1.1" 200 -
2025-06-30 21:00:07,611 - INFO - 127.0.0.1 - - [30/Jun/2025 21:00:07] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:00:49,230 - INFO - 127.0.0.1 - - [30/Jun/2025 21:00:49] "GET /auth/callback?state=iwNjE9XlwJaxvhKCqeFVu24h8dZff7&code=4/0AVMBsJg5eMduhDKLGCMU68iyFOi0Ct_Mtd_UscwPWsFFnZKqoWqK2nI-Gn56s7A-N54gBg&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+openid+https://www.googleapis.com/auth/userinfo.profile&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1" 200 -
2025-06-30 21:03:13,325 - INFO - 127.0.0.1 - - [30/Jun/2025 21:03:13] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:08:23,356 - INFO - 127.0.0.1 - - [30/Jun/2025 21:08:23] "GET /auth/callback?state=GlUCH2eUhvAGNZrL7xs6FL2qfYxv3i&code=4/0AVMBsJhNIU9WkYxqcDS8uCnxR3QzLTl8_fXU2yep1SpNLWKYCeXzXmZMJCmCPwadH62tgg&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+https://www.googleapis.com/auth/userinfo.profile+openid&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1" 200 -
2025-06-30 21:08:26,086 - INFO - 127.0.0.1 - - [30/Jun/2025 21:08:26] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:12:36,780 - INFO - 127.0.0.1 - - [30/Jun/2025 21:12:36] "GET /auth/callback?state=AuBVzqoGTGJ6RJ2ZGWOOsMw5mKgCSl&code=4/0AVMBsJi-hFkRpeGIx8MRk0JxizLSzEUlQ1I3SkwsqJvmeAcCf56hxi4qAr_69T4TCB8CxQ&scope=email+profile+openid+https://www.googleapis.com/auth/userinfo.profile+https://www.googleapis.com/auth/userinfo.email&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1" 200 -
2025-06-30 21:12:38,571 - INFO - 127.0.0.1 - - [30/Jun/2025 21:12:38] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:13:14,054 - INFO - 127.0.0.1 - - [30/Jun/2025 21:13:14] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 21:13:14,062 - INFO - 127.0.0.1 - - [30/Jun/2025 21:13:14] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:13:17,588 - INFO - 127.0.0.1 - - [30/Jun/2025 21:13:17] "GET /auth/callback?state=O6PJKzZw6RK5kYhKpJwK7vucPWDM4n&code=4/0AVMBsJjiWqqziBMuY7jArZwKRBfA5cddvfk7rw7m4NdvKIJ2K13Zg7rjdXO7m3_uFbwIHQ&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+https://www.googleapis.com/auth/userinfo.profile+openid&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1" 200 -
2025-06-30 21:13:25,252 - INFO - 127.0.0.1 - - [30/Jun/2025 21:13:25] "[32mGET /login HTTP/1.1[0m" 302 -
