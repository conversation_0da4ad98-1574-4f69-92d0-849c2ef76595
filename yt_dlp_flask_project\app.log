2025-06-30 20:56:34,315 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 20:56:34,317 - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 20:57:07,078 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:07] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 20:57:07,370 - ERROR - Exception on /login [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\app.py", line 165, in login
    return google.authorize_redirect(redirect_uri, state=session['oauth_state'])
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\flask_client\apps.py", line 49, in authorize_redirect
    rv = self.create_authorization_url(redirect_uri, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\base_client\sync_app.py", line 341, in create_authorization_url
    metadata = self.load_server_metadata()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\base_client\sync_app.py", line 327, in load_server_metadata
    resp.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://accounts.google.com/.well-known/openid_configuration
2025-06-30 20:57:07,526 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:07] "[35m[1mGET /login HTTP/1.1[0m" 500 -
2025-06-30 20:57:07,841 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:07] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-30 20:57:18,220 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:18] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 20:57:18,392 - ERROR - Exception on /login [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "D:\Dashboard\Stems Finder Flask\yt_dlp_flask_project\app.py", line 165, in login
    return google.authorize_redirect(redirect_uri, state=session['oauth_state'])
           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\flask_client\apps.py", line 49, in authorize_redirect
    rv = self.create_authorization_url(redirect_uri, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\base_client\sync_app.py", line 341, in create_authorization_url
    metadata = self.load_server_metadata()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\authlib\integrations\base_client\sync_app.py", line 327, in load_server_metadata
    resp.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\requests\models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error: Not Found for url: https://accounts.google.com/.well-known/openid_configuration
2025-06-30 20:57:18,400 - INFO - 127.0.0.1 - - [30/Jun/2025 20:57:18] "[35m[1mGET /login HTTP/1.1[0m" 500 -
2025-06-30 20:59:07,251 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-30 20:59:07,251 - INFO - [33mPress CTRL+C to quit[0m
2025-06-30 20:59:23,449 - INFO - 127.0.0.1 - - [30/Jun/2025 20:59:23] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 20:59:23,684 - INFO - 127.0.0.1 - - [30/Jun/2025 20:59:23] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 20:59:47,342 - INFO - 127.0.0.1 - - [30/Jun/2025 20:59:47] "GET /auth/callback?state=PS01iummwK3YWo6qqieNuRlNce0MSn&code=4/0AVMBsJgt-y2hwvgMORzkdYk2zcfDnxAMqzZGlCDKz4aYHGjgHi9dr2CM9-SkeLfcqxr-Lw&scope=email+profile+https://www.googleapis.com/auth/userinfo.profile+https://www.googleapis.com/auth/userinfo.email+openid&authuser=0&hd=sadhguru.org&prompt=consent HTTP/1.1" 200 -
2025-06-30 21:00:07,611 - INFO - 127.0.0.1 - - [30/Jun/2025 21:00:07] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:00:49,230 - INFO - 127.0.0.1 - - [30/Jun/2025 21:00:49] "GET /auth/callback?state=iwNjE9XlwJaxvhKCqeFVu24h8dZff7&code=4/0AVMBsJg5eMduhDKLGCMU68iyFOi0Ct_Mtd_UscwPWsFFnZKqoWqK2nI-Gn56s7A-N54gBg&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+openid+https://www.googleapis.com/auth/userinfo.profile&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1" 200 -
2025-06-30 21:03:13,325 - INFO - 127.0.0.1 - - [30/Jun/2025 21:03:13] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:08:23,356 - INFO - 127.0.0.1 - - [30/Jun/2025 21:08:23] "GET /auth/callback?state=GlUCH2eUhvAGNZrL7xs6FL2qfYxv3i&code=4/0AVMBsJhNIU9WkYxqcDS8uCnxR3QzLTl8_fXU2yep1SpNLWKYCeXzXmZMJCmCPwadH62tgg&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+https://www.googleapis.com/auth/userinfo.profile+openid&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1" 200 -
2025-06-30 21:08:26,086 - INFO - 127.0.0.1 - - [30/Jun/2025 21:08:26] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:12:36,780 - INFO - 127.0.0.1 - - [30/Jun/2025 21:12:36] "GET /auth/callback?state=AuBVzqoGTGJ6RJ2ZGWOOsMw5mKgCSl&code=4/0AVMBsJi-hFkRpeGIx8MRk0JxizLSzEUlQ1I3SkwsqJvmeAcCf56hxi4qAr_69T4TCB8CxQ&scope=email+profile+openid+https://www.googleapis.com/auth/userinfo.profile+https://www.googleapis.com/auth/userinfo.email&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1" 200 -
2025-06-30 21:12:38,571 - INFO - 127.0.0.1 - - [30/Jun/2025 21:12:38] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:13:14,054 - INFO - 127.0.0.1 - - [30/Jun/2025 21:13:14] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-30 21:13:14,062 - INFO - 127.0.0.1 - - [30/Jun/2025 21:13:14] "[32mGET /login HTTP/1.1[0m" 302 -
2025-06-30 21:13:17,588 - INFO - 127.0.0.1 - - [30/Jun/2025 21:13:17] "GET /auth/callback?state=O6PJKzZw6RK5kYhKpJwK7vucPWDM4n&code=4/0AVMBsJjiWqqziBMuY7jArZwKRBfA5cddvfk7rw7m4NdvKIJ2K13Zg7rjdXO7m3_uFbwIHQ&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+https://www.googleapis.com/auth/userinfo.profile+openid&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1" 200 -
2025-06-30 21:13:25,252 - INFO - 127.0.0.1 - - [30/Jun/2025 21:13:25] "[32mGET /login HTTP/1.1[0m" 302 -
2025-07-01 09:45:16,944 - INFO - Loaded 7 allowed emails from ACL
2025-07-01 09:45:17,000 - INFO - Cache loaded: 21838 rows
2025-07-01 09:45:17,040 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-01 09:45:17,044 - INFO - [33mPress CTRL+C to quit[0m
2025-07-01 09:45:34,280 - INFO - 127.0.0.1 - - [01/Jul/2025 09:45:34] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-01 09:45:34,297 - INFO - 127.0.0.1 - - [01/Jul/2025 09:45:34] "[32mGET /login HTTP/1.1[0m" 302 -
2025-07-01 09:45:39,841 - INFO - 127.0.0.1 - - [01/Jul/2025 09:45:39] "[32mGET /auth/callback?state=EiaIszXBH2egfv7L0I0cjOsuAPN7oK&code=4/0AVMBsJjdYCCaEspV0d1YzshYZMm6n6SQJZFRrELcCwdE-AQl6ERagCJRhOBg6sUR_7a1uA&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+https://www.googleapis.com/auth/userinfo.profile+openid&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1[0m" 302 -
2025-07-01 09:45:39,891 - INFO - 127.0.0.1 - - [01/Jul/2025 09:45:39] "GET / HTTP/1.1" 200 -
2025-07-01 09:45:58,805 - INFO - 127.0.0.1 - - [01/Jul/2025 09:45:58] "POST /search HTTP/1.1" 200 -
2025-07-01 09:46:36,643 - INFO - 127.0.0.1 - - [01/Jul/2025 09:46:36] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-07-01 09:46:36,652 - INFO - 127.0.0.1 - - [01/Jul/2025 09:46:36] "[32mGET /login HTTP/1.1[0m" 302 -
2025-07-01 09:46:43,307 - INFO - 127.0.0.1 - - [01/Jul/2025 09:46:43] "[32mGET /auth/callback?state=LIMUIXbfd0S5jYbjxnrclQvK1BHeeC&code=4/0AVMBsJiZyBM1B4EFS7hGNc7XAFQo8T0wHYyG3xKQrq5oU-CcFi5zXmi8LiWIbGGFicH98w&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+https://www.googleapis.com/auth/userinfo.profile+openid&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1[0m" 302 -
2025-07-01 09:46:43,314 - INFO - 127.0.0.1 - - [01/Jul/2025 09:46:43] "GET / HTTP/1.1" 200 -
2025-07-01 09:47:25,028 - INFO - 127.0.0.1 - - [01/Jul/2025 09:47:25] "POST /search HTTP/1.1" 200 -
2025-07-01 10:01:27,236 - INFO - Loaded 7 allowed emails from ACL
2025-07-01 10:01:27,292 - INFO - Cache loaded: 21838 rows
2025-07-01 10:01:27,293 - INFO - Automatic cache refresh scheduled for 03:00 daily
2025-07-01 10:01:27,323 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-01 10:01:27,323 - INFO - [33mPress CTRL+C to quit[0m
2025-07-01 10:02:22,518 - INFO - 127.0.0.1 - - [01/Jul/2025 10:02:22] "GET / HTTP/1.1" 200 -
2025-07-01 10:03:43,962 - INFO - Enhanced search for: 'CQlgTAxgL-J' across 21,838 rows
2025-07-01 10:03:45,585 - INFO - Enhanced search completed: 1 unique matches
2025-07-01 10:03:45,619 - INFO - 127.0.0.1 - - [01/Jul/2025 10:03:45] "POST /search HTTP/1.1" 200 -
2025-07-01 10:06:27,250 - INFO - Enhanced search for: 'Shiva' across 21,838 rows
2025-07-01 10:06:27,331 - INFO - Enhanced search completed: 276 unique matches
2025-07-01 10:06:27,344 - INFO - 127.0.0.1 - - [01/Jul/2025 10:06:27] "POST /search HTTP/1.1" 200 -
2025-07-01 10:06:34,999 - INFO - Enhanced search for: 'Shiva shakti' across 21,838 rows
2025-07-01 10:06:36,592 - INFO - Enhanced search completed: 4 unique matches
2025-07-01 10:06:36,612 - INFO - 127.0.0.1 - - [01/Jul/2025 10:06:36] "POST /search HTTP/1.1" 200 -
2025-07-01 10:06:58,608 - INFO - Enhanced search for: 'Sadhguru's Vlog Pt3 - Going Incognito' across 21,838 rows
2025-07-01 10:07:00,401 - INFO - Enhanced search completed: 0 unique matches
2025-07-01 10:07:00,420 - INFO - 127.0.0.1 - - [01/Jul/2025 10:07:00] "POST /search HTTP/1.1" 200 -
2025-07-01 10:07:50,943 - INFO - Enhanced search for: 'The Beauty Of Motherhood' across 21,838 rows
2025-07-01 10:07:52,649 - INFO - Enhanced search completed: 2 unique matches
2025-07-01 10:07:52,667 - INFO - 127.0.0.1 - - [01/Jul/2025 10:07:52] "POST /search HTTP/1.1" 200 -
2025-07-01 10:08:25,290 - INFO - Enhanced search for: 'Sadhguru On Webseries Adolescence' across 21,838 rows
2025-07-01 10:08:27,109 - INFO - Enhanced search completed: 3 unique matches
2025-07-01 10:08:27,129 - INFO - 127.0.0.1 - - [01/Jul/2025 10:08:27] "POST /search HTTP/1.1" 200 -
2025-07-01 10:08:56,560 - INFO - Enhanced search for: '691amj4D4Hs' across 21,838 rows
2025-07-01 10:08:58,266 - INFO - Enhanced search completed: 1 unique matches
2025-07-01 10:08:58,281 - INFO - 127.0.0.1 - - [01/Jul/2025 10:08:58] "POST /search HTTP/1.1" 200 -
2025-07-01 10:09:01,145 - INFO - Manual cache refresh triggered by user
2025-07-01 10:09:01,146 - INFO - Building cache from Google Sheets...
2025-07-01 10:09:01,146 - INFO - Progress: 0% - Initializing cache refresh...
2025-07-01 10:09:01,147 - INFO - Progress: 10% - Downloading Edited Main Sheet (1/8)
2025-07-01 10:09:01,147 - INFO - [1/8] Downloading Edited Main Sheet
2025-07-01 10:09:05,238 - ERROR - Error downloading Edited Main Sheet: the header row in the worksheet contains duplicates: ['']To manually set the header row, use the `expected_headers` parameter of `get_all_records()`
2025-07-01 10:09:05,254 - WARNING - No data from Edited Main Sheet
2025-07-01 10:09:05,255 - INFO - Progress: 20% - Downloading Social Media Catalog(SG) (2/8)
2025-07-01 10:09:05,255 - INFO - [2/8] Downloading Social Media Catalog(SG)
2025-07-01 10:09:07,028 - ERROR - Error downloading Social Media Catalog(SG): 'filename'
2025-07-01 10:09:07,031 - WARNING - No data from Social Media Catalog(SG)
2025-07-01 10:09:07,031 - INFO - Progress: 30% - Downloading Social Media Catalog(IF) (3/8)
2025-07-01 10:09:07,031 - INFO - [3/8] Downloading Social Media Catalog(IF)
2025-07-01 10:09:08,419 - ERROR - Error downloading Social Media Catalog(IF): 'filename'
2025-07-01 10:09:08,421 - WARNING - No data from Social Media Catalog(IF)
2025-07-01 10:09:08,421 - INFO - Progress: 40% - Downloading Social Media Catalog(IG) (4/8)
2025-07-01 10:09:08,421 - INFO - [4/8] Downloading Social Media Catalog(IG)
2025-07-01 10:09:10,335 - ERROR - Error downloading Social Media Catalog(IG): 'filename'
2025-07-01 10:09:10,337 - WARNING - No data from Social Media Catalog(IG)
2025-07-01 10:09:10,338 - INFO - Progress: 50% - Downloading Social Media Catalog(CP) (5/8)
2025-07-01 10:09:10,339 - INFO - [5/8] Downloading Social Media Catalog(CP)
2025-07-01 10:09:11,800 - ERROR - Error downloading Social Media Catalog(CP): 'filename'
2025-07-01 10:09:11,801 - WARNING - No data from Social Media Catalog(CP)
2025-07-01 10:09:11,802 - INFO - Progress: 60% - Downloading Copy Social Media Catalog(SG) (6/8)
2025-07-01 10:09:11,802 - INFO - [6/8] Downloading Copy Social Media Catalog(SG)
2025-07-01 10:09:13,488 - ERROR - Error downloading Copy Social Media Catalog(SG): 'filename'
2025-07-01 10:09:13,490 - WARNING - No data from Copy Social Media Catalog(SG)
2025-07-01 10:09:13,490 - INFO - Progress: 70% - Downloading Copy Social Media Catalog(IF) (7/8)
2025-07-01 10:09:13,491 - INFO - [7/8] Downloading Copy Social Media Catalog(IF)
2025-07-01 10:09:14,838 - ERROR - Error downloading Copy Social Media Catalog(IF): 'filename'
2025-07-01 10:09:14,839 - WARNING - No data from Copy Social Media Catalog(IF)
2025-07-01 10:09:14,840 - INFO - Progress: 80% - Downloading Copy Social Media Catalog(IG) (8/8)
2025-07-01 10:09:14,840 - INFO - [8/8] Downloading Copy Social Media Catalog(IG)
2025-07-01 10:09:16,682 - ERROR - Error downloading Copy Social Media Catalog(IG): 'filename'
2025-07-01 10:09:16,685 - WARNING - No data from Copy Social Media Catalog(IG)
2025-07-01 10:09:16,685 - INFO - Progress: 85% - Processing and merging data...
2025-07-01 10:09:16,685 - ERROR - FAILED! No data downloaded
2025-07-01 10:09:16,691 - INFO - 127.0.0.1 - - [01/Jul/2025 10:09:16] "[35m[1mPOST /cache/refresh HTTP/1.1[0m" 500 -
2025-07-01 10:09:36,208 - INFO - Enhanced search for: 'OCD-2773' across 21,838 rows
2025-07-01 10:09:37,756 - INFO - Enhanced search completed: 543 unique matches
2025-07-01 10:09:37,778 - INFO - 127.0.0.1 - - [01/Jul/2025 10:09:37] "POST /search HTTP/1.1" 200 -
2025-07-01 10:11:15,053 - INFO - Enhanced search for: 'OCD-2773' across 21,838 rows
2025-07-01 10:11:16,658 - INFO - Enhanced search completed: 543 unique matches
2025-07-01 10:11:16,677 - INFO - 127.0.0.1 - - [01/Jul/2025 10:11:16] "POST /search HTTP/1.1" 200 -
2025-07-01 10:11:33,549 - INFO - 127.0.0.1 - - [01/Jul/2025 10:11:33] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-07-01 10:11:33,574 - INFO - 127.0.0.1 - - [01/Jul/2025 10:11:33] "[32mGET /login HTTP/1.1[0m" 302 -
2025-07-01 10:13:48,711 - INFO - Loaded 7 allowed emails from ACL
2025-07-01 10:13:48,764 - INFO - Cache loaded: 21838 rows
2025-07-01 10:13:48,766 - INFO - Automatic cache refresh scheduled for 03:00 daily
2025-07-01 10:13:48,797 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-01 10:13:48,798 - INFO - [33mPress CTRL+C to quit[0m
2025-07-01 10:14:03,036 - INFO - 127.0.0.1 - - [01/Jul/2025 10:14:03] "[32mGET / HTTP/1.1[0m" 302 -
2025-07-01 10:14:03,052 - INFO - 127.0.0.1 - - [01/Jul/2025 10:14:03] "[32mGET /login HTTP/1.1[0m" 302 -
2025-07-01 10:14:07,135 - INFO - 127.0.0.1 - - [01/Jul/2025 10:14:07] "[32mGET /auth/callback?state=CDk5wCI9ihC0foCmSs993tTA2HQvHZ&code=4/0AVMBsJgRh6Ito9YdNnnARwHxcWxMLa6BHTA0GlKUwsMS0P0DkEM5q4LKmr8B3vQ0J3OcJw&scope=email+profile+openid+https://www.googleapis.com/auth/userinfo.email+https://www.googleapis.com/auth/userinfo.profile&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1[0m" 302 -
2025-07-01 10:14:07,151 - INFO - 127.0.0.1 - - [01/Jul/2025 10:14:07] "GET / HTTP/1.1" 200 -
2025-07-01 10:14:21,537 - INFO - Manual cache refresh triggered by user
2025-07-01 10:14:21,539 - INFO - Building cache from Google Sheets...
2025-07-01 10:14:21,539 - INFO - Progress: 0% - Initializing cache refresh...
2025-07-01 10:14:21,540 - INFO - Progress: 10% - Downloading Edited Main Sheet (1/8)
2025-07-01 10:14:21,541 - INFO - [1/8] Downloading Edited Main Sheet
2025-07-01 10:14:24,801 - ERROR - Error downloading Edited Main Sheet: the header row in the worksheet contains duplicates: ['']To manually set the header row, use the `expected_headers` parameter of `get_all_records()`
2025-07-01 10:14:24,809 - WARNING - No data from Edited Main Sheet
2025-07-01 10:14:24,815 - INFO - Progress: 20% - Downloading Social Media Catalog(SG) (2/8)
2025-07-01 10:14:24,816 - INFO - [2/8] Downloading Social Media Catalog(SG)
2025-07-01 10:14:26,501 - ERROR - Error downloading Social Media Catalog(SG): 'filename'
2025-07-01 10:14:26,508 - WARNING - No data from Social Media Catalog(SG)
2025-07-01 10:14:26,512 - INFO - Progress: 30% - Downloading Social Media Catalog(IF) (3/8)
2025-07-01 10:14:26,516 - INFO - [3/8] Downloading Social Media Catalog(IF)
2025-07-01 10:14:27,970 - ERROR - Error downloading Social Media Catalog(IF): 'filename'
2025-07-01 10:14:27,971 - WARNING - No data from Social Media Catalog(IF)
2025-07-01 10:14:27,972 - INFO - Progress: 40% - Downloading Social Media Catalog(IG) (4/8)
2025-07-01 10:14:27,973 - INFO - [4/8] Downloading Social Media Catalog(IG)
2025-07-01 10:14:29,810 - ERROR - Error downloading Social Media Catalog(IG): 'filename'
2025-07-01 10:14:29,813 - WARNING - No data from Social Media Catalog(IG)
2025-07-01 10:14:29,813 - INFO - Progress: 50% - Downloading Social Media Catalog(CP) (5/8)
2025-07-01 10:14:29,814 - INFO - [5/8] Downloading Social Media Catalog(CP)
2025-07-01 10:14:31,198 - ERROR - Error downloading Social Media Catalog(CP): 'filename'
2025-07-01 10:14:31,199 - WARNING - No data from Social Media Catalog(CP)
2025-07-01 10:14:31,200 - INFO - Progress: 60% - Downloading Copy Social Media Catalog(SG) (6/8)
2025-07-01 10:14:31,201 - INFO - [6/8] Downloading Copy Social Media Catalog(SG)
2025-07-01 10:14:32,920 - ERROR - Error downloading Copy Social Media Catalog(SG): 'filename'
2025-07-01 10:14:32,950 - WARNING - No data from Copy Social Media Catalog(SG)
2025-07-01 10:14:32,953 - INFO - Progress: 70% - Downloading Copy Social Media Catalog(IF) (7/8)
2025-07-01 10:14:32,955 - INFO - [7/8] Downloading Copy Social Media Catalog(IF)
2025-07-01 10:14:34,475 - ERROR - Error downloading Copy Social Media Catalog(IF): 'filename'
2025-07-01 10:14:34,479 - WARNING - No data from Copy Social Media Catalog(IF)
2025-07-01 10:14:34,480 - INFO - Progress: 80% - Downloading Copy Social Media Catalog(IG) (8/8)
2025-07-01 10:14:34,481 - INFO - [8/8] Downloading Copy Social Media Catalog(IG)
2025-07-01 10:16:06,622 - INFO - Loaded 7 allowed emails from ACL
2025-07-01 10:16:06,682 - INFO - Cache loaded: 21838 rows
2025-07-01 10:16:06,685 - INFO - Automatic cache refresh scheduled for 03:00 daily
2025-07-01 10:16:06,716 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-01 10:16:06,717 - INFO - [33mPress CTRL+C to quit[0m
2025-07-01 10:16:25,752 - INFO - 127.0.0.1 - - [01/Jul/2025 10:16:25] "GET / HTTP/1.1" 200 -
2025-07-01 10:20:55,086 - INFO - Manual cache refresh triggered by user
2025-07-01 10:20:55,087 - INFO - Building cache from Google Sheets...
2025-07-01 10:20:55,088 - INFO - Progress: 0% - Initializing cache refresh...
2025-07-01 10:20:55,088 - INFO - Progress: 10% - Downloading Edited Main Sheet (1/8)
2025-07-01 10:20:55,089 - INFO - [1/8] Downloading Edited Main Sheet
2025-07-01 10:20:59,173 - ERROR - Error downloading Edited Main Sheet: the header row in the worksheet contains duplicates: ['']To manually set the header row, use the `expected_headers` parameter of `get_all_records()`
2025-07-01 10:20:59,177 - WARNING - No data from Edited Main Sheet
2025-07-01 10:20:59,178 - INFO - Progress: 20% - Downloading Social Media Catalog(SG) (2/8)
2025-07-01 10:20:59,178 - INFO - [2/8] Downloading Social Media Catalog(SG)
2025-07-01 10:21:01,145 - ERROR - Error downloading Social Media Catalog(SG): 'filename'
2025-07-01 10:21:01,147 - WARNING - No data from Social Media Catalog(SG)
2025-07-01 10:21:01,148 - INFO - Progress: 30% - Downloading Social Media Catalog(IF) (3/8)
2025-07-01 10:21:01,148 - INFO - [3/8] Downloading Social Media Catalog(IF)
2025-07-01 10:21:02,549 - ERROR - Error downloading Social Media Catalog(IF): 'filename'
2025-07-01 10:21:02,550 - WARNING - No data from Social Media Catalog(IF)
2025-07-01 10:21:02,551 - INFO - Progress: 40% - Downloading Social Media Catalog(IG) (4/8)
2025-07-01 10:21:02,551 - INFO - [4/8] Downloading Social Media Catalog(IG)
2025-07-01 10:21:04,230 - INFO - 127.0.0.1 - - [01/Jul/2025 10:21:04] "GET / HTTP/1.1" 200 -
2025-07-01 10:21:04,608 - ERROR - Error downloading Social Media Catalog(IG): 'filename'
2025-07-01 10:21:04,611 - WARNING - No data from Social Media Catalog(IG)
2025-07-01 10:21:04,611 - INFO - Progress: 50% - Downloading Social Media Catalog(CP) (5/8)
2025-07-01 10:21:04,611 - INFO - [5/8] Downloading Social Media Catalog(CP)
2025-07-01 10:21:05,798 - INFO - 127.0.0.1 - - [01/Jul/2025 10:21:05] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-07-01 10:21:05,814 - INFO - 127.0.0.1 - - [01/Jul/2025 10:21:05] "[32mGET /login HTTP/1.1[0m" 302 -
2025-07-01 10:21:05,986 - ERROR - Error downloading Social Media Catalog(CP): 'filename'
2025-07-01 10:21:05,987 - WARNING - No data from Social Media Catalog(CP)
2025-07-01 10:21:05,988 - INFO - Progress: 60% - Downloading Copy Social Media Catalog(SG) (6/8)
2025-07-01 10:21:05,988 - INFO - [6/8] Downloading Copy Social Media Catalog(SG)
2025-07-01 10:21:07,842 - ERROR - Error downloading Copy Social Media Catalog(SG): 'filename'
2025-07-01 10:21:07,844 - WARNING - No data from Copy Social Media Catalog(SG)
2025-07-01 10:21:07,845 - INFO - Progress: 70% - Downloading Copy Social Media Catalog(IF) (7/8)
2025-07-01 10:21:07,845 - INFO - [7/8] Downloading Copy Social Media Catalog(IF)
2025-07-01 10:21:09,224 - INFO - 127.0.0.1 - - [01/Jul/2025 10:21:09] "[32mGET /auth/callback?state=HvgMYLJDSNpf1CyMLpJtqAruvJBiK5&code=4/0AVMBsJguxTZ39yZaAtO627K1u6Dba3tz3PxlbqoHZwJy-f_gj7XrLsJgXjPc5VA8Nwx6-g&scope=email+profile+https://www.googleapis.com/auth/userinfo.email+openid+https://www.googleapis.com/auth/userinfo.profile&authuser=0&hd=sadhguru.org&prompt=none HTTP/1.1[0m" 302 -
2025-07-01 10:21:09,254 - ERROR - Error downloading Copy Social Media Catalog(IF): 'filename'
2025-07-01 10:21:09,255 - INFO - 127.0.0.1 - - [01/Jul/2025 10:21:09] "GET / HTTP/1.1" 200 -
2025-07-01 10:21:09,256 - WARNING - No data from Copy Social Media Catalog(IF)
2025-07-01 10:21:09,257 - INFO - Progress: 80% - Downloading Copy Social Media Catalog(IG) (8/8)
2025-07-01 10:21:09,257 - INFO - [8/8] Downloading Copy Social Media Catalog(IG)
2025-07-01 10:21:10,997 - ERROR - Error downloading Copy Social Media Catalog(IG): 'filename'
2025-07-01 10:21:10,999 - WARNING - No data from Copy Social Media Catalog(IG)
2025-07-01 10:21:11,000 - INFO - Progress: 85% - Processing and merging data...
2025-07-01 10:21:11,000 - ERROR - FAILED! No data downloaded
2025-07-01 10:21:11,001 - INFO - 127.0.0.1 - - [01/Jul/2025 10:21:11] "[35m[1mPOST /cache/refresh HTTP/1.1[0m" 500 -
2025-07-01 10:21:11,340 - INFO - Manual cache refresh triggered by user
2025-07-01 10:21:11,341 - INFO - Building cache from Google Sheets...
2025-07-01 10:21:11,341 - INFO - Progress: 0% - Initializing cache refresh...
2025-07-01 10:21:11,342 - INFO - Progress: 10% - Downloading Edited Main Sheet (1/8)
2025-07-01 10:21:11,343 - INFO - [1/8] Downloading Edited Main Sheet
2025-07-01 10:21:13,507 - ERROR - Error downloading Edited Main Sheet: the header row in the worksheet contains duplicates: ['']To manually set the header row, use the `expected_headers` parameter of `get_all_records()`
2025-07-01 10:21:13,509 - WARNING - No data from Edited Main Sheet
2025-07-01 10:21:13,510 - INFO - Progress: 20% - Downloading Social Media Catalog(SG) (2/8)
2025-07-01 10:21:13,510 - INFO - [2/8] Downloading Social Media Catalog(SG)
2025-07-01 10:21:15,316 - ERROR - Error downloading Social Media Catalog(SG): 'filename'
2025-07-01 10:21:15,318 - WARNING - No data from Social Media Catalog(SG)
2025-07-01 10:21:15,318 - INFO - Progress: 30% - Downloading Social Media Catalog(IF) (3/8)
2025-07-01 10:21:15,318 - INFO - [3/8] Downloading Social Media Catalog(IF)
2025-07-01 10:21:16,772 - ERROR - Error downloading Social Media Catalog(IF): 'filename'
2025-07-01 10:21:16,773 - WARNING - No data from Social Media Catalog(IF)
2025-07-01 10:21:16,774 - INFO - Progress: 40% - Downloading Social Media Catalog(IG) (4/8)
2025-07-01 10:21:16,774 - INFO - [4/8] Downloading Social Media Catalog(IG)
2025-07-01 10:21:18,799 - ERROR - Error downloading Social Media Catalog(IG): 'filename'
2025-07-01 10:21:18,801 - WARNING - No data from Social Media Catalog(IG)
2025-07-01 10:21:18,801 - INFO - Progress: 50% - Downloading Social Media Catalog(CP) (5/8)
2025-07-01 10:21:18,802 - INFO - [5/8] Downloading Social Media Catalog(CP)
2025-07-01 10:21:20,159 - ERROR - Error downloading Social Media Catalog(CP): 'filename'
2025-07-01 10:21:20,160 - WARNING - No data from Social Media Catalog(CP)
2025-07-01 10:21:20,161 - INFO - Progress: 60% - Downloading Copy Social Media Catalog(SG) (6/8)
2025-07-01 10:21:20,162 - INFO - [6/8] Downloading Copy Social Media Catalog(SG)
2025-07-01 10:21:21,907 - ERROR - Error downloading Copy Social Media Catalog(SG): 'filename'
2025-07-01 10:21:21,909 - WARNING - No data from Copy Social Media Catalog(SG)
2025-07-01 10:21:21,909 - INFO - Progress: 70% - Downloading Copy Social Media Catalog(IF) (7/8)
2025-07-01 10:21:21,910 - INFO - [7/8] Downloading Copy Social Media Catalog(IF)
2025-07-01 10:21:23,347 - ERROR - Error downloading Copy Social Media Catalog(IF): 'filename'
2025-07-01 10:21:23,349 - WARNING - No data from Copy Social Media Catalog(IF)
2025-07-01 10:21:23,349 - INFO - Progress: 80% - Downloading Copy Social Media Catalog(IG) (8/8)
2025-07-01 10:21:23,350 - INFO - [8/8] Downloading Copy Social Media Catalog(IG)
2025-07-01 10:21:25,336 - ERROR - Error downloading Copy Social Media Catalog(IG): 'filename'
2025-07-01 10:21:25,339 - WARNING - No data from Copy Social Media Catalog(IG)
2025-07-01 10:21:25,339 - INFO - Progress: 85% - Processing and merging data...
2025-07-01 10:21:25,339 - ERROR - FAILED! No data downloaded
2025-07-01 10:21:25,340 - INFO - 127.0.0.1 - - [01/Jul/2025 10:21:25] "[35m[1mPOST /cache/refresh HTTP/1.1[0m" 500 -
2025-07-01 10:21:44,151 - INFO - Enhanced search for: 'CQlgTAxgL-J' across 21,838 rows
2025-07-01 10:21:45,791 - INFO - Enhanced search completed: 1 unique matches
2025-07-01 10:21:45,806 - INFO - 127.0.0.1 - - [01/Jul/2025 10:21:45] "POST /search HTTP/1.1" 200 -
2025-07-01 10:22:19,612 - INFO - Enhanced search for: 'OCD-17887' across 21,838 rows
2025-07-01 10:22:21,131 - INFO - Enhanced search completed: 605 unique matches
2025-07-01 10:22:21,142 - INFO - 127.0.0.1 - - [01/Jul/2025 10:22:21] "POST /search HTTP/1.1" 200 -
2025-07-01 10:23:21,457 - INFO - Enhanced search for: 'OCD-14590' across 21,838 rows
2025-07-01 10:23:22,953 - INFO - Enhanced search completed: 1007 unique matches
2025-07-01 10:23:22,965 - INFO - 127.0.0.1 - - [01/Jul/2025 10:23:22] "POST /search HTTP/1.1" 200 -
2025-07-01 10:23:54,732 - INFO - Enhanced search for: '"DKpOQq1z7GU XpmPY7eS12g"' across 21,838 rows
2025-07-01 10:23:56,405 - INFO - Enhanced search completed: 1 unique matches
2025-07-01 10:23:56,416 - INFO - 127.0.0.1 - - [01/Jul/2025 10:23:56] "POST /search HTTP/1.1" 200 -
2025-07-01 10:24:36,030 - INFO - Enhanced search for: 'DFE-gm3TjHE' across 21,838 rows
2025-07-01 10:24:37,611 - INFO - Enhanced search completed: 1 unique matches
2025-07-01 10:24:37,621 - INFO - 127.0.0.1 - - [01/Jul/2025 10:24:37] "POST /search HTTP/1.1" 200 -
2025-07-01 10:25:07,558 - INFO - Enhanced search for: 'oWBTMp35RfA' across 21,838 rows
2025-07-01 10:25:09,059 - INFO - Enhanced search completed: 1 unique matches
2025-07-01 10:25:09,072 - INFO - 127.0.0.1 - - [01/Jul/2025 10:25:09] "POST /search HTTP/1.1" 200 -
2025-07-01 10:25:26,710 - INFO - Enhanced search for: '3amX-jVo4-U' across 21,838 rows
2025-07-01 10:25:28,352 - INFO - Enhanced search completed: 1 unique matches
2025-07-01 10:25:28,362 - INFO - 127.0.0.1 - - [01/Jul/2025 10:25:28] "POST /search HTTP/1.1" 200 -
2025-07-01 10:25:56,005 - INFO - Enhanced search for: 'Kgowgm1KeZ4' across 21,838 rows
2025-07-01 10:25:57,532 - INFO - Enhanced search completed: 2 unique matches
2025-07-01 10:25:57,543 - INFO - 127.0.0.1 - - [01/Jul/2025 10:25:57] "POST /search HTTP/1.1" 200 -
2025-07-01 10:31:18,714 - INFO - Loaded 7 allowed emails from ACL
2025-07-01 10:31:18,832 - INFO - Cache loaded: 21838 rows
2025-07-01 10:31:18,843 - INFO - Automatic cache refresh scheduled for 03:00 daily
2025-07-01 10:31:18,889 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-01 10:31:18,890 - INFO - [33mPress CTRL+C to quit[0m
2025-07-01 10:31:41,021 - INFO - 127.0.0.1 - - [01/Jul/2025 10:31:41] "GET / HTTP/1.1" 200 -
2025-07-01 10:32:40,813 - INFO - Loaded 7 allowed emails from ACL
2025-07-01 10:32:40,865 - INFO - Cache loaded: 21838 rows
2025-07-01 10:32:40,866 - INFO - Automatic cache refresh scheduled for 03:00 daily
2025-07-01 10:32:40,897 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-07-01 10:32:40,897 - INFO - [33mPress CTRL+C to quit[0m
2025-07-01 10:33:09,377 - INFO - 127.0.0.1 - - [01/Jul/2025 10:33:09] "GET / HTTP/1.1" 200 -
2025-07-01 10:35:18,310 - INFO - Manual cache refresh triggered by user
2025-07-01 10:35:18,311 - INFO - Building cache from Google Sheets...
2025-07-01 10:35:18,311 - INFO - Progress: 0% - Initializing cache refresh...
2025-07-01 10:35:18,311 - INFO - Progress: 10% - Downloading Edited Main Sheet (1/8)
2025-07-01 10:35:18,312 - INFO - [1/8] Downloading Edited Main Sheet
2025-07-01 10:35:22,480 - ERROR - Error downloading Edited Main Sheet: the header row in the worksheet contains duplicates: ['']To manually set the header row, use the `expected_headers` parameter of `get_all_records()`
2025-07-01 10:35:22,483 - WARNING - No data from Edited Main Sheet
2025-07-01 10:35:22,483 - INFO - Progress: 20% - Downloading Social Media Catalog(SG) (2/8)
2025-07-01 10:35:22,484 - INFO - [2/8] Downloading Social Media Catalog(SG)
2025-07-01 10:35:24,323 - ERROR - Error downloading Social Media Catalog(SG): 'filename'
2025-07-01 10:35:24,326 - WARNING - No data from Social Media Catalog(SG)
2025-07-01 10:35:24,326 - INFO - Progress: 30% - Downloading Social Media Catalog(IF) (3/8)
2025-07-01 10:35:24,326 - INFO - [3/8] Downloading Social Media Catalog(IF)
2025-07-01 10:35:25,724 - ERROR - Error downloading Social Media Catalog(IF): 'filename'
2025-07-01 10:35:25,725 - WARNING - No data from Social Media Catalog(IF)
2025-07-01 10:35:25,725 - INFO - Progress: 40% - Downloading Social Media Catalog(IG) (4/8)
2025-07-01 10:35:25,726 - INFO - [4/8] Downloading Social Media Catalog(IG)
2025-07-01 10:35:28,067 - ERROR - Error downloading Social Media Catalog(IG): 'filename'
2025-07-01 10:35:28,070 - WARNING - No data from Social Media Catalog(IG)
2025-07-01 10:35:28,070 - INFO - Progress: 50% - Downloading Social Media Catalog(CP) (5/8)
2025-07-01 10:35:28,070 - INFO - [5/8] Downloading Social Media Catalog(CP)
2025-07-01 10:35:29,579 - ERROR - Error downloading Social Media Catalog(CP): 'filename'
2025-07-01 10:35:29,580 - WARNING - No data from Social Media Catalog(CP)
2025-07-01 10:35:29,581 - INFO - Progress: 60% - Downloading Copy Social Media Catalog(SG) (6/8)
2025-07-01 10:35:29,581 - INFO - [6/8] Downloading Copy Social Media Catalog(SG)
2025-07-01 10:35:31,690 - ERROR - Error downloading Copy Social Media Catalog(SG): 'filename'
2025-07-01 10:35:31,692 - WARNING - No data from Copy Social Media Catalog(SG)
2025-07-01 10:35:31,692 - INFO - Progress: 70% - Downloading Copy Social Media Catalog(IF) (7/8)
2025-07-01 10:35:31,693 - INFO - [7/8] Downloading Copy Social Media Catalog(IF)
2025-07-01 10:35:33,021 - ERROR - Error downloading Copy Social Media Catalog(IF): 'filename'
2025-07-01 10:35:33,022 - WARNING - No data from Copy Social Media Catalog(IF)
2025-07-01 10:35:33,023 - INFO - Progress: 80% - Downloading Copy Social Media Catalog(IG) (8/8)
2025-07-01 10:35:33,023 - INFO - [8/8] Downloading Copy Social Media Catalog(IG)
2025-07-01 10:35:34,952 - ERROR - Error downloading Copy Social Media Catalog(IG): 'filename'
2025-07-01 10:35:34,954 - WARNING - No data from Copy Social Media Catalog(IG)
2025-07-01 10:35:34,954 - INFO - Progress: 85% - Processing and merging data...
2025-07-01 10:35:34,955 - ERROR - FAILED! No data downloaded
2025-07-01 10:35:34,955 - INFO - 127.0.0.1 - - [01/Jul/2025 10:35:34] "[35m[1mPOST /cache/refresh HTTP/1.1[0m" 500 -
