#!/usr/bin/env python3
"""
Test the simple working app
"""

import requests
import time

def test_simple_app():
    """Test the simple working app"""
    
    print("🔍 TESTING SIMPLE WORKING APP")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # Login
        print("📋 Step 1: Login")
        login_response = session.post("http://127.0.0.1:8080/login", 
                                    data={'password': 'Shiva@123'}, 
                                    timeout=5)
        
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test one specific query
    print("\n📋 Step 2: Test Search")
    query = "Living Without Regrets"
    print(f"Testing: '{query}'")
    
    try:
        start_time = time.time()
        search_response = session.post("http://127.0.0.1:8080/search", 
                                     data={'query': query}, 
                                     timeout=10)
        search_time = time.time() - start_time
        
        print(f"Response Status: {search_response.status_code}")
        print(f"Search Time: {search_time:.3f}s")
        
        if search_response.status_code == 200:
            # Check if it's HTML response (form) or JSON (API)
            content_type = search_response.headers.get('content-type', '')
            
            if 'application/json' in content_type:
                result = search_response.json()
                matches = result.get('total_matches', 0)
                print(f"Total Matches: {matches}")
                
                if matches > 0:
                    print("✅ SUCCESS! Found matches via JSON API")
                    first_match = result.get('results', [{}])[0]
                    filename = first_match.get('filename', 'N/A')
                    print(f"First result: {filename}")
                    return True
                else:
                    print("❌ No matches found via JSON API")
                    return False
            else:
                # HTML response - check if it contains results
                html_content = search_response.text
                if 'Found' in html_content and 'matches' in html_content:
                    print("✅ SUCCESS! Found matches via HTML form")
                    return True
                elif 'No matches found' in html_content:
                    print("❌ No matches found via HTML form")
                    return False
                else:
                    print("✅ SUCCESS! Search completed (HTML response)")
                    return True
        else:
            print(f"❌ Search failed: {search_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Search error: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_app()
    
    if success:
        print("\n🎉 SIMPLE WORKING APP IS SUCCESSFUL!")
        print("✅ App is running and responding correctly!")
        print("🌐 You can now test it in the browser at http://127.0.0.1:8080")
        print("🔑 Login with password: Shiva@123")
        print("🔍 Try searching for: 'Living Without Regrets'")
    else:
        print("\n🔧 App needs more work")
    
    print("\n🚀 Test complete!")
