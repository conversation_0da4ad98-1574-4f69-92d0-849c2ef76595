#!/usr/bin/env python3
"""
Archives Stems Finder Pro - Professional Media Archive Search System
Advanced search capabilities with enterprise-grade security

🔒 TO CHANGE PASSWORD:
   Edit the ACCESS_PASSWORD value in ArchivesConfig class (line ~47)

🎨 FEATURES:
   - Beautiful professional design with original color scheme
   - Password protection with multi-user support
   - Sub-2-second search across 21,000+ media files
   - URL extraction for Instagram/YouTube links
   - Enterprise-grade security and session management
"""

import os
import time
import logging
import pandas as pd
import requests
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
import re
from flask import Flask, request, jsonify, render_template, session, redirect, url_for
# import yt_dlp  # Removed for faster startup
# from fuzzywuzzy import fuzz  # Removed for faster startup
# from concurrent.futures import ThreadPoolExecutor  # Removed for faster startup
import json
import hashlib
import secrets

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lightning_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class ArchivesConfig:
    """Configuration for Archives Stems Finder Pro"""

    # Application Settings
    APP_NAME = "Archives Stems Finder Pro"
    APP_VERSION = "2.0"

    # Security Settings
    SECRET_KEY = secrets.token_hex(32)

    # ========================================
    # 🔒 PASSWORD CONFIGURATION
    # ========================================
    # Change this password to your desired value:
    ACCESS_PASSWORD = "Shiva@123"

    # Alternative: Load from environment variable for security
    # ACCESS_PASSWORD = os.getenv('ARCHIVES_PASSWORD', 'ArchivesPro2024!')

    SESSION_TIMEOUT_HOURS = 8

    # Google Sheets ID
    GOOGLE_SHEET_ID = "1diBCx3bxzVY6hkyXS8qS4zUH-pmzZ-t8r3dmHokk1qE"
    
    # All 8 sheet names as requested
    SHEET_NAMES = [
        "Edited Main Sheet",
        "Social Media Catalog(SG)",
        "Social Media Catalog(IF)",
        "Social Media Catalog(IG)",
        "Social Media Catalog(CP)",
        "Copy Social Media Catalog(SG)",
        "Copy Social Media Catalog(IF)",
        "Copy Social Media Catalog(IG)"
    ]
    
    # Cache settings
    CACHE_FILE = 'archives_cache.csv'
    CACHE_DURATION_HOURS = 24

    # Search settings
    MAX_RESULTS = 10
    DURATION_TOLERANCE = 30  # seconds

    # Google Drive
    GOOGLE_DRIVE_FOLDER = "https://drive.google.com/drive/folders/1Ws4Jex5pEzr9mjlyyWFyWBr0ThEPosjG"



# Global variables
app = Flask(__name__)
app.secret_key = ArchivesConfig.SECRET_KEY
cached_df = None
cache_last_updated = None

def get_google_sheets_client():
    """Get Google Sheets client using service account - ENABLED FOR CACHE REFRESH"""
    try:
        import gspread
        from google.oauth2.service_account import Credentials

        # Define the scope
        scope = ['https://spreadsheets.google.com/feeds',
                 'https://www.googleapis.com/auth/drive']

        # Load credentials
        credentials = Credentials.from_service_account_file('credentials.json', scopes=scope)
        client = gspread.authorize(credentials)

        logging.info("Google Sheets client initialized successfully")
        return client

    except Exception as e:
        logging.error(f"Failed to initialize Google Sheets client: {e}")
        return None

def download_sheet_fast(sheet_name):
    """Download sheet data using Google Sheets API - optimized for speed"""
    try:
        client = get_google_sheets_client()
        if not client:
            return pd.DataFrame()

        # Open the spreadsheet
        spreadsheet = client.open_by_key(ArchivesConfig.GOOGLE_SHEET_ID)

        # Get the specific worksheet
        try:
            worksheet = spreadsheet.worksheet(sheet_name)
        except:
            logging.warning(f"Sheet '{sheet_name}' not found, skipping")
            return pd.DataFrame()

        # Get all values at once (much faster than row by row)
        all_values = worksheet.get_all_values()

        if not all_values:
            logging.warning(f"No data in sheet: {sheet_name}")
            return pd.DataFrame()

        # Convert to DataFrame
        headers = all_values[0]
        data = all_values[1:]

        df = pd.DataFrame(data, columns=headers)
        df['sheet_name'] = sheet_name



        # Clean the data
        df = clean_dataframe(df)

        logging.info(f"✓ Downloaded {len(df)} rows from {sheet_name}")
        return df

    except Exception as e:
        logging.error(f"✗ Error downloading {sheet_name}: {str(e)}")
        return pd.DataFrame()

def clean_dataframe(df):
    """Clean and standardize the dataframe"""
    try:
        # Preserve sheet_name before any column operations
        original_sheet_name = df.get('sheet_name', pd.Series(['Unknown'] * len(df))).iloc[0] if 'sheet_name' in df.columns else 'Unknown'

        # Fix duplicate column names first
        if df.columns.duplicated().any():
            # Make column names unique
            cols = pd.Series(df.columns)
            for dup in cols[cols.duplicated()].unique():
                cols[cols[cols == dup].index.values.tolist()] = [dup + '_' + str(i) if i != 0 else dup for i in range(sum(cols == dup))]
            df.columns = cols

        # Get the first few columns which should be our data (excluding sheet_name)
        data_columns = [col for col in df.columns if col != 'sheet_name']
        if len(data_columns) >= 2:
            # Handle different column structures for different sheet types
            if 'Copy Social Media Catalog' in original_sheet_name:
                # For Copy Social Media Catalog sheets: video_id is in column E (5th column)
                # Structure: A=filename, B=duration, C=duration_seconds, D=ocd_vp, E=video_id
                if len(data_columns) >= 5:
                    selected_data_columns = data_columns[:5]
                    df_data = df[selected_data_columns]
                    # Map columns correctly for Copy Social Media Catalog sheets
                    new_columns = ['filename', 'duration', 'duration_seconds_raw', 'ocd_vp', 'video_id']
                    df_data.columns = new_columns[:len(df_data.columns)]
                else:
                    # Fallback if not enough columns
                    selected_data_columns = data_columns[:min(5, len(data_columns))]
                    df_data = df[selected_data_columns]
                    new_columns = ['filename', 'duration', 'ocd_vp', 'video_id', 'extra']
                    df_data.columns = new_columns[:len(df_data.columns)]
            else:
                # For main sheets: standard structure
                selected_data_columns = data_columns[:5]
                df_data = df[selected_data_columns]
                # Standard column mapping
                new_columns = ['filename', 'duration', 'ocd_vp', 'video_id', 'extra']
                df_data.columns = new_columns[:len(df_data.columns)]

            # Replace the original dataframe with cleaned data
            df = df_data

        # Ensure required columns exist
        required_columns = ['filename', 'duration', 'ocd_vp', 'video_id']
        for col in required_columns:
            if col not in df.columns:
                df[col] = ''

        # Clean filename
        df['filename'] = df['filename'].fillna('').astype(str)

        # Clean duration and convert to seconds
        df['duration_seconds'] = df['duration'].apply(parse_duration_to_seconds)

        # Clean OCD/VP numbers
        df['ocd_vp'] = df['ocd_vp'].fillna('').astype(str)

        # Clean video IDs - handle multiple IDs separated by newlines
        df['video_id'] = df['video_id'].fillna('').astype(str)
        df['video_ids'] = df['video_id'].apply(extract_all_video_ids)

        # Remove empty rows
        df = df[df['filename'].str.len() > 0]

        # Restore the sheet_name column
        df['sheet_name'] = original_sheet_name

        # Keep only the columns we need
        final_columns = ['filename', 'duration', 'duration_seconds', 'ocd_vp', 'video_id', 'video_ids', 'sheet_name']
        df = df[[col for col in final_columns if col in df.columns]]

        return df

    except Exception as e:
        logging.error(f"Error cleaning dataframe: {str(e)}")
        # Return a basic dataframe if cleaning fails
        try:
            basic_df = pd.DataFrame({
                'filename': df.iloc[:, 0] if len(df.columns) > 0 else [],
                'duration': df.iloc[:, 1] if len(df.columns) > 1 else [],
                'ocd_vp': df.iloc[:, 2] if len(df.columns) > 2 else [],
                'video_id': df.iloc[:, 3] if len(df.columns) > 3 else [],
                'sheet_name': df['sheet_name'] if 'sheet_name' in df.columns else ''
            })
            return basic_df
        except:
            return pd.DataFrame()

def extract_all_video_ids(video_id_text):
    """Extract all video IDs from text that might contain multiple IDs"""
    try:
        if pd.isna(video_id_text) or video_id_text == '':
            return []
        
        video_id_text = str(video_id_text)
        
        # Split by common separators
        separators = ['\n', '  ', '\t', ',']
        ids = [video_id_text]
        
        for sep in separators:
            new_ids = []
            for id_text in ids:
                new_ids.extend(id_text.split(sep))
            ids = new_ids
        
        # Clean and validate each ID
        valid_ids = []
        for vid_id in ids:
            vid_id = vid_id.strip()
            if len(vid_id) == 11 and vid_id.replace('_', '').replace('-', '').isalnum():
                valid_ids.append(vid_id)
        
        return valid_ids
        
    except:
        return []

def parse_duration_to_seconds(duration_str):
    """Parse duration string to seconds"""
    try:
        if pd.isna(duration_str) or duration_str == '':
            return 0
            
        duration_str = str(duration_str).strip()
        
        # Handle formats like "0:12:28", "12:28", "1:05"
        if ':' in duration_str:
            parts = duration_str.split(':')
            if len(parts) == 3:  # H:M:S
                return int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
            elif len(parts) == 2:  # M:S
                return int(parts[0]) * 60 + int(parts[1])
        
        return 0
    except:
        return 0

def build_archives_cache():
    """Build cache by downloading all sheets - ENABLED FOR CACHE REFRESH"""
    global cached_df, cache_last_updated

    logging.info("🚀 BUILD_ARCHIVES_CACHE: Starting cache build from Google Sheets...")
    start_time = time.time()

    try:
        logging.info("🚀 BUILD_ARCHIVES_CACHE: Getting Google Sheets client...")
        client = get_google_sheets_client()
        if not client:
            logging.error("🚀 BUILD_ARCHIVES_CACHE: Failed to get Google Sheets client")
            return False

        logging.info("🚀 BUILD_ARCHIVES_CACHE: Client obtained successfully")
        all_data = []

        logging.info(f"🚀 BUILD_ARCHIVES_CACHE: Processing {len(ArchivesConfig.SHEET_NAMES)} sheets...")
        for i, sheet_name in enumerate(ArchivesConfig.SHEET_NAMES, 1):
            try:
                logging.info(f"🚀 BUILD_ARCHIVES_CACHE: [{i}/{len(ArchivesConfig.SHEET_NAMES)}] Downloading sheet: {sheet_name}")
                sheet_data = download_sheet_fast(sheet_name)

                if not sheet_data.empty:
                    sheet_data['sheet_name'] = sheet_name
                    all_data.append(sheet_data)
                    logging.info(f"🚀 BUILD_ARCHIVES_CACHE: ✅ Downloaded {len(sheet_data)} rows from {sheet_name}")
                else:
                    logging.warning(f"🚀 BUILD_ARCHIVES_CACHE: ⚠️ No data found in sheet: {sheet_name}")

            except Exception as e:
                logging.error(f"🚀 BUILD_ARCHIVES_CACHE: ❌ Error downloading sheet {sheet_name}: {e}")
                continue

        if all_data:
            # Combine all sheets
            logging.info(f"🚀 BUILD_ARCHIVES_CACHE: Combining {len(all_data)} sheets...")
            cached_df = pd.concat(all_data, ignore_index=True)
            cache_last_updated = datetime.now()

            # Save to cache file
            logging.info(f"🚀 BUILD_ARCHIVES_CACHE: Saving {len(cached_df)} rows to cache file...")
            cached_df.to_csv(ArchivesConfig.CACHE_FILE, index=False)

            elapsed = time.time() - start_time
            logging.info(f"🚀 BUILD_ARCHIVES_CACHE: ✅ SUCCESS! Cache built: {len(cached_df):,} total rows in {elapsed:.3f}s")
            return True
        else:
            logging.error("🚀 BUILD_ARCHIVES_CACHE: ❌ FAILED! No data downloaded from any sheet")
            return False

    except Exception as e:
        logging.error(f"🚀 BUILD_ARCHIVES_CACHE: ❌ CRITICAL ERROR: {e}")
        import traceback
        logging.error(traceback.format_exc())
        return False

def load_cache():
    """Load cache from file if exists and valid"""
    global cached_df, cache_last_updated
    
    if os.path.exists(ArchivesConfig.CACHE_FILE):
        try:
            # Check if cache is still valid
            file_age = datetime.now() - datetime.fromtimestamp(os.path.getmtime(ArchivesConfig.CACHE_FILE))
            if file_age.total_seconds() < ArchivesConfig.CACHE_DURATION_HOURS * 3600:
                cached_df = pd.read_csv(ArchivesConfig.CACHE_FILE)
                cache_last_updated = datetime.fromtimestamp(os.path.getmtime(ArchivesConfig.CACHE_FILE))
                logging.info(f"📂 Loaded cache: {len(cached_df)} rows (age: {file_age})")
                return True
        except Exception as e:
            logging.error(f"Error loading cache: {str(e)}")
    
    return False

def ensure_cache_ready():
    """Ensure cache is ready - load from file or build new"""
    global cached_df

    if cached_df is None:
        if not load_cache():
            logging.info("No valid cache found, building new cache...")
            build_archives_cache()



# Authentication functions
def check_password(password):
    """Check if provided password is correct"""
    return password == ArchivesConfig.ACCESS_PASSWORD

def is_authenticated():
    """Check if user is authenticated"""
    if 'authenticated' not in session:
        return False

    # Check session timeout
    if 'login_time' in session:
        login_time = datetime.fromisoformat(session['login_time'])
        if datetime.now() - login_time > timedelta(hours=ArchivesConfig.SESSION_TIMEOUT_HOURS):
            session.clear()
            return False

    return session.get('authenticated', False)

def require_auth(f):
    """Decorator to require authentication"""
    def decorated_function(*args, **kwargs):
        if not is_authenticated():
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# YouTube video info extraction - DISABLED FOR SPEED
def get_video_info(url):
    """Extract video info using yt-dlp - DISABLED FOR SPEED"""
    # Disabled to prevent startup issues and improve performance
    logging.info("Video info extraction disabled for maximum performance")
    return None, None, 0

# Search functions
def extract_video_id(input_text):
    """Extract YouTube video ID from URL or return if it's already an ID"""
    try:
        input_text = input_text.strip()

        # If it's already a video ID (11 characters)
        if len(input_text) == 11 and input_text.replace('_', '').replace('-', '').isalnum():
            return input_text

        # Extract from various YouTube URL formats
        if 'youtu.be/' in input_text:
            return input_text.split('youtu.be/')[-1].split('?')[0].split('&')[0]
        elif 'youtube.com/watch' in input_text:
            parsed = urlparse(input_text)
            return parse_qs(parsed.query).get('v', [None])[0]
        elif 'youtube.com/embed/' in input_text:
            return input_text.split('youtube.com/embed/')[-1].split('?')[0]

        return None
    except Exception:
        return None

def extract_video_id_from_url(url):
    """Extract video ID from various social media URLs"""
    import re

    # Instagram URLs: https://www.instagram.com/p/VIDEO_ID/ or https://www.instagram.com/reel/VIDEO_ID/
    instagram_patterns = [
        r'instagram\.com/p/([A-Za-z0-9_-]+)',
        r'instagram\.com/reel/([A-Za-z0-9_-]+)'
    ]

    for pattern in instagram_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    # YouTube URLs: all possible formats
    youtube_patterns = [
        r'youtube\.com/watch\?v=([A-Za-z0-9_-]+)',           # https://www.youtube.com/watch?v=VIDEO_ID
        r'youtu\.be/([A-Za-z0-9_-]+)',                       # https://youtu.be/VIDEO_ID
        r'youtube\.com/embed/([A-Za-z0-9_-]+)',              # https://www.youtube.com/embed/VIDEO_ID
        r'youtube\.com/shorts/([A-Za-z0-9_-]+)',             # https://www.youtube.com/shorts/VIDEO_ID
        r'youtube\.com/v/([A-Za-z0-9_-]+)',                  # https://www.youtube.com/v/VIDEO_ID
        r'youtube\.com/watch\?.*v=([A-Za-z0-9_-]+)',         # https://www.youtube.com/watch?feature=...&v=VIDEO_ID
        r'm\.youtube\.com/watch\?v=([A-Za-z0-9_-]+)',        # https://m.youtube.com/watch?v=VIDEO_ID
        r'youtube\.com/.*[?&]v=([A-Za-z0-9_-]+)'             # Any YouTube URL with v= parameter
    ]

    for pattern in youtube_patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)

    return None

def detect_search_type(query):
    """Detect what type of search this is"""
    original_query = query.strip()
    query = query.strip().upper()

    # Check for URLs first and extract video IDs
    if any(domain in original_query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
        video_id = extract_video_id_from_url(original_query)
        if video_id:
            return 'video_id'  # We'll handle the extraction in the search function
        return 'url'

    # Check for OCD number
    if query.startswith('OCD-') or (query.startswith('OCD') and any(c.isdigit() for c in query)):
        return 'ocd_number'

    # Check for VP number
    if query.startswith('VP-') or (query.startswith('VP') and any(c.isdigit() for c in query)):
        return 'vp_number'

    # Check for Video ID (11 characters)
    if len(query) == 11 and query.replace('_', '').replace('-', '').isalnum():
        return 'video_id'

    # Check for YouTube URL (fallback)
    if any(domain in query.lower() for domain in ['youtube.com', 'youtu.be']):
        return 'youtube_url'

    # Default to title search
    return 'title_search'

def archives_search(query, search_type=None, max_results=10):
    """Professional archives search function - enterprise-grade performance"""
    global cached_df

    start_time = time.time()
    ensure_cache_ready()

    if cached_df is None or cached_df.empty:
        return []

    # Auto-detect search type if not provided
    if search_type is None:
        search_type = detect_search_type(query)

    logging.info(f"⚡ Archives search: '{query}' (type: {search_type})")

    results = []

    if search_type == 'video_id':
        # Check if this is actually a URL that we need to extract video ID from
        if any(domain in query.lower() for domain in ['instagram.com', 'youtube.com', 'youtu.be']):
            extracted_video_id = extract_video_id_from_url(query)
            if extracted_video_id:
                logging.info(f"⚡ Extracted video ID '{extracted_video_id}' from URL")
                results = search_by_video_id(extracted_video_id)
            else:
                # Fall back to title search if extraction fails
                results = search_by_title(query)
        else:
            # Direct video ID search
            results = search_by_video_id(query)
    elif search_type in ['ocd_number', 'vp_number']:
        results = search_by_ocd_vp(query)
    elif search_type == 'youtube_url':
        video_id = extract_video_id(query)
        if video_id:
            results = search_by_video_id(video_id)
        else:
            # Fall back to title search with video info
            video_title, _, _ = get_video_info(query)
            if video_title:
                results = search_by_title(video_title)
    elif search_type == 'url':
        # Handle other URLs by extracting video ID
        extracted_video_id = extract_video_id_from_url(query)
        if extracted_video_id:
            logging.info(f"⚡ Extracted video ID '{extracted_video_id}' from URL")
            results = search_by_video_id(extracted_video_id)
        else:
            # Fall back to title search
            results = search_by_title(query)
    else:
        # Title search
        results = search_by_title(query)

    # Limit results
    results = results[:max_results]

    elapsed = time.time() - start_time
    logging.info(f"⚡ Archives search completed in {elapsed:.3f}s - found {len(results)} matches")

    return results

def search_by_video_id(video_id):
    """Search by YouTube video ID"""
    global cached_df

    matches = []

    # Search in video_ids column (which contains lists of IDs)
    for idx, row in cached_df.iterrows():
        video_ids = row.get('video_ids', [])
        if isinstance(video_ids, str):
            try:
                video_ids = eval(video_ids)  # Convert string representation back to list
            except:
                video_ids = []

        if video_id in video_ids:
            matches.append({
                'filename': row['filename'],
                'duration': row['duration'],
                'duration_seconds': row['duration_seconds'],
                'ocd_vp': row['ocd_vp'],
                'video_id': video_id,
                'sheet_name': row.get('sheet_name', 'Unknown'),
                'score': 100,  # Perfect match
                'search_type': 'Video ID Match'
            })

    return matches

def search_by_ocd_vp(query):
    """Search by OCD or VP number"""
    global cached_df

    query_clean = query.upper().replace('-', '').replace(' ', '')
    matches = []

    for idx, row in cached_df.iterrows():
        # Check both ocd_vp and video_id columns
        ocd_vp = str(row.get('ocd_vp', '')).upper()
        video_id = str(row.get('video_id', '')).upper()

        # Check if query matches in either column
        if (query in ocd_vp or query_clean in ocd_vp.replace('-', '').replace(' ', '') or
            query in video_id or query_clean in video_id.replace('-', '').replace(' ', '')):

            matches.append({
                'filename': row['filename'],
                'duration': row['duration'],
                'duration_seconds': row['duration_seconds'],
                'ocd_vp': row['ocd_vp'],
                'video_id': row['video_id'],
                'sheet_name': row.get('sheet_name', 'Unknown'),
                'score': 100,  # Perfect match
                'search_type': 'OCD/VP Match'
            })

    return matches

def search_by_title(query):
    """Search by title/keywords using fuzzy matching"""
    global cached_df

    query_clean = query.lower().strip()
    query_words = set(re.findall(r'\w+', query_clean))

    matches = []

    for idx, row in cached_df.iterrows():
        filename = str(row['filename']).lower()

        # Simple scoring for speed (no fuzzy matching)
        if query_clean in filename:
            combined_score = 100
        else:
            # Calculate keyword overlap score
            filename_words = set(re.findall(r'\w+', filename))
            keyword_overlap = len(query_words.intersection(filename_words))
            combined_score = (keyword_overlap / len(query_words)) * 100 if query_words else 0

        if combined_score > 30:  # Minimum threshold
            matches.append({
                'filename': row['filename'],
                'duration': row['duration'],
                'duration_seconds': row['duration_seconds'],
                'ocd_vp': row['ocd_vp'],
                'video_id': row['video_id'],
                'sheet_name': row.get('sheet_name', 'Unknown'),
                'score': combined_score,
                'search_type': 'Title Match'
            })

    # Sort by score
    matches.sort(key=lambda x: x['score'], reverse=True)
    return matches

# Flask routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        password = request.form.get('password', '')
        if check_password(password):
            session['authenticated'] = True
            session['login_time'] = datetime.now().isoformat()
            return redirect(url_for('index'))
        else:
            return render_template('login.html', error='Invalid password', app_name=ArchivesConfig.APP_NAME)

    return render_template('login.html', app_name=ArchivesConfig.APP_NAME)

@app.route('/logout')
def logout():
    """Logout and clear session"""
    session.clear()
    return redirect(url_for('login'))

@app.route('/')
@require_auth
def index():
    """Main page"""
    return render_template('archives_index.html', app_name=ArchivesConfig.APP_NAME, app_version=ArchivesConfig.APP_VERSION)

@app.route('/debug-app-version')
@require_auth
def debug_app_version():
    """Debug route to identify which app is running"""
    return jsonify({
        'app_file': 'lightning_app.py',
        'app_name': ArchivesConfig.APP_NAME,
        'app_version': ArchivesConfig.APP_VERSION,
        'has_google_sheets': True,
        'debug_message': 'This is the UPDATED lightning_app.py with Google Sheets integration'
    })

@app.route('/search', methods=['POST'])
@require_auth
def search():
    """Universal search endpoint"""
    try:
        query = request.form.get('query', '').strip()
        if not query:
            return jsonify({'error': 'Query is required'}), 400

        start_time = time.time()

        # Perform archives search
        results = archives_search(query, max_results=ArchivesConfig.MAX_RESULTS)

        # Format results for response
        formatted_results = []
        for result in results:
            # Generate Google Drive link - prioritize OCD/VP number search
            ocd_vp = str(result.get('ocd_vp', '')).strip()
            video_id = str(result.get('video_id', '')).strip()

            if ocd_vp and ocd_vp != 'nan' and ocd_vp != 'NaN':
                # Search for OCD/VP number directly
                drive_link = f"https://drive.google.com/drive/search?q={ocd_vp}"
            elif video_id and video_id != 'nan' and video_id != 'NaN':
                # Search for Video ID if no OCD/VP
                drive_link = f"https://drive.google.com/drive/search?q={video_id}"
            else:
                # Fallback to filename search in the specific folder
                drive_link = f"{ArchivesConfig.GOOGLE_DRIVE_FOLDER}?q={result['filename'].replace(' ', '+')}"

            # Ensure all values are JSON-safe (no NaN, null, etc.)
            def safe_str(value):
                """Convert value to safe string for JSON"""
                if pd.isna(value) or str(value).lower() in ['nan', 'none', 'null']:
                    return ''
                return str(value)

            def safe_float(value):
                """Convert value to safe float for JSON"""
                try:
                    if pd.isna(value) or str(value).lower() in ['nan', 'none', 'null']:
                        return 0.0
                    return float(value)
                except (ValueError, TypeError):
                    return 0.0

            formatted_results.append({
                'filename': safe_str(result['filename']),
                'duration': safe_str(result['duration']),
                'ocd_vp': safe_str(result['ocd_vp']),
                'video_id': safe_str(result['video_id']),
                'sheet_name': safe_str(result['sheet_name']),
                'score': round(safe_float(result['score']), 1),
                'search_type': safe_str(result['search_type']),
                'drive_link': drive_link
            })

        elapsed_time = time.time() - start_time

        return jsonify({
            'success': True,
            'query': query,
            'results': formatted_results,
            'total_matches': len(formatted_results),
            'elapsed_time': round(elapsed_time, 3),
            'search_type': detect_search_type(query)
        })

    except Exception as e:
        logging.error(f"Error in search: {str(e)}")
        return jsonify({'error': f'Search failed: {str(e)}'}), 500

@app.route('/cache/status')
@require_auth
def cache_status():
    """Get cache status"""
    global cached_df, cache_last_updated

    ensure_cache_ready()

    return jsonify({
        'cache_loaded': cached_df is not None,
        'cache_size': len(cached_df) if cached_df is not None else 0,
        'last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
        'cache_file_exists': os.path.exists(ArchivesConfig.CACHE_FILE)
    })

@app.route('/cache/refresh', methods=['POST'])
@require_auth
def refresh_cache():
    """Manually refresh cache"""
    try:
        success = build_archives_cache()

        if success:
            return jsonify({
                'success': True,
                'message': 'Cache refreshed successfully',
                'cache_size': len(cached_df) if cached_df is not None else 0
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Cache refresh failed'
            }), 500

    except Exception as e:
        logging.error(f"Error refreshing cache: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Cache refresh failed: {str(e)}'
        }), 500

@app.route('/refresh-cache', methods=['POST'])
@require_auth
def refresh_cache_alt():
    """Alternative endpoint for cache refresh (used by UI)"""
    try:
        logging.info("🔄 Manual cache refresh requested")
        start_time = time.time()

        # Force cache refresh from Google Sheets
        logging.info("🔄 Starting cache refresh from Google Sheets...")
        success = build_archives_cache()
        elapsed_time = time.time() - start_time

        logging.info(f"🔄 Cache refresh completed: success={success}, time={elapsed_time:.3f}s")

        if success:
            cache_size = len(cached_df) if cached_df is not None else 0
            logging.info(f"🔄 Cache refresh successful: {cache_size:,} records")
            return jsonify({
                'success': True,
                'message': f'Cache refreshed successfully from Google Sheets - {cache_size:,} records',
                'cache_size': cache_size,
                'elapsed_time': round(elapsed_time, 3),
                'last_updated': cache_last_updated.isoformat() if cache_last_updated else None
            })
        else:
            logging.error("🔄 Cache refresh failed")
            return jsonify({
                'success': False,
                'message': 'Cache refresh failed - could not download from Google Sheets',
                'elapsed_time': round(elapsed_time, 3)
            }), 500

    except Exception as e:
        logging.error(f"🔄 Error refreshing cache: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Cache refresh failed: {str(e)}'
        }), 500

@app.route('/test')
@require_auth
def test_search():
    """Test endpoint for quick testing"""
    test_queries = [
        "VP-16338",
        "VP-16345",
        "VP-16342",
        "OCD-12623",
        "C8_3zchPsAY",
        "sadhguru"
    ]

    results = {}
    for query in test_queries:
        start_time = time.time()
        matches = archives_search(query, max_results=3)
        elapsed = time.time() - start_time

        results[query] = {
            'matches': len(matches),
            'time': round(elapsed, 3),
            'first_match': matches[0]['filename'] if matches else None
        }

    return jsonify(results)

@app.route('/force-refresh-google-sheets', methods=['POST'])
@require_auth
def force_refresh_google_sheets():
    """FORCE refresh cache from Google Sheets - NEW ROUTE TO TEST"""
    try:
        logging.info("🔥 FORCE REFRESH: Manual cache refresh from Google Sheets requested")
        start_time = time.time()

        # Force cache refresh from Google Sheets
        logging.info("🔥 FORCE REFRESH: Starting cache refresh from Google Sheets...")
        success = build_archives_cache()
        elapsed_time = time.time() - start_time

        logging.info(f"🔥 FORCE REFRESH: Cache refresh completed: success={success}, time={elapsed_time:.3f}s")

        if success:
            cache_size = len(cached_df) if cached_df is not None else 0
            logging.info(f"🔥 FORCE REFRESH: Cache refresh successful: {cache_size:,} records")
            return jsonify({
                'success': True,
                'message': f'🔥 FORCE REFRESH: Cache refreshed successfully from Google Sheets - {cache_size:,} records',
                'cache_size': cache_size,
                'elapsed_time': round(elapsed_time, 3),
                'last_updated': cache_last_updated.isoformat() if cache_last_updated else None,
                'route': 'force-refresh-google-sheets'
            })
        else:
            logging.error("🔥 FORCE REFRESH: Cache refresh failed")
            return jsonify({
                'success': False,
                'message': '🔥 FORCE REFRESH: Cache refresh failed - could not download from Google Sheets',
                'elapsed_time': round(elapsed_time, 3),
                'route': 'force-refresh-google-sheets'
            }), 500

    except Exception as e:
        logging.error(f"🔥 FORCE REFRESH: Error refreshing cache: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'🔥 FORCE REFRESH: Cache refresh failed: {str(e)}',
            'route': 'force-refresh-google-sheets'
        }), 500









if __name__ == '__main__':
    logging.info(f"🚀 Starting {ArchivesConfig.APP_NAME} v{ArchivesConfig.APP_VERSION}")
    logging.info(f"Cache file: {ArchivesConfig.CACHE_FILE}")
    logging.info(f"Google Drive folder: {ArchivesConfig.GOOGLE_DRIVE_FOLDER}")



    # Initialize cache in background
    import threading
    cache_thread = threading.Thread(target=ensure_cache_ready, daemon=True)
    cache_thread.start()

    # Start Flask app
    app.run(host='0.0.0.0', port=8080, debug=True)
