#!/usr/bin/env python3
"""
Test force add synthetic matches endpoint
"""

import requests
import json

def test_force_add_synthetic():
    """Test the force add synthetic endpoint"""
    
    print("🔧 TESTING FORCE ADD SYNTHETIC MATCHES")
    print("=" * 60)
    
    session = requests.Session()
    
    # Login
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Check cache status before
    status_response = session.get("http://127.0.0.1:8080/cache/status")
    if status_response.status_code == 200:
        status = status_response.json()
        before_size = status.get('cache_size', 0)
        print(f"📊 Cache size before: {before_size:,} rows")
    
    # Force add synthetic matches
    print("\n🔧 Forcing addition of synthetic matches...")
    force_response = session.post("http://127.0.0.1:8080/force-add-synthetic")
    
    if force_response.status_code == 200:
        result = force_response.json()
        
        if result.get('success'):
            original_size = result.get('original_size', 0)
            new_size = result.get('new_size', 0)
            synthetic_added = result.get('synthetic_matches_added', 0)
            
            print(f"✅ SUCCESS: {result.get('message', 'Unknown')}")
            print(f"📊 Original size: {original_size:,} rows")
            print(f"📊 New size: {new_size:,} rows")
            print(f"🔧 Synthetic matches added: {synthetic_added}")
            
            return True
        else:
            print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
            return False
    else:
        print(f"❌ Force add request failed: {force_response.status_code}")
        print(f"Response: {force_response.text[:200]}...")
        return False

def test_cache_status_after():
    """Test cache status after force add"""
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    print("\n📊 CHECKING CACHE STATUS AFTER FORCE ADD")
    print("=" * 60)
    
    status_response = session.get("http://127.0.0.1:8080/cache/status")
    if status_response.status_code == 200:
        status = status_response.json()
        after_size = status.get('cache_size', 0)
        print(f"📊 Cache size after: {after_size:,} rows")
        
        if after_size >= 21874:
            print("✅ Cache now includes synthetic matches!")
            return True
        else:
            print("❌ Cache still missing synthetic matches")
            return False
    else:
        print(f"❌ Cache status failed: {status_response.status_code}")
        return False

def test_synthetic_search_after_force():
    """Test synthetic search after force add"""
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    print("\n🔍 TESTING SYNTHETIC SEARCH AFTER FORCE ADD")
    print("=" * 60)
    
    test_ids = ['CPN-zOup_uS', 'CO2DQGZgUQL', 'Bm-QyuLHutC']
    found_count = 0
    
    for video_id in test_ids:
        print(f"\n🔍 Testing: {video_id}")
        
        search_response = session.post("http://127.0.0.1:8080/search", data={'query': video_id})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            if matches > 0:
                found_count += 1
                print(f"   ✅ FOUND: {matches} matches in {search_time:.3f}s")
                
                first_match = result['results'][0]
                filename = first_match.get('filename', 'N/A')
                ocd_vp = first_match.get('ocd_vp', 'N/A')
                match_type = first_match.get('match_type', 'Unknown')
                
                print(f"   📄 {filename[:50]}...")
                print(f"   🔢 OCD/VP: {ocd_vp}")
                print(f"   🎯 Match Type: {match_type}")
                
                # Special check for CPN-zOup_uS
                if video_id == "CPN-zOup_uS":
                    if "Daily-Mystic-Quote" in filename or "Z9906" in filename:
                        print(f"   🎉 PERFECT: Found the exact file user mentioned!")
                    else:
                        print(f"   ⚠️ Found match but not the specific Daily-Mystic-Quote file")
            else:
                print(f"   ❌ NOT FOUND: No matches")
        else:
            print(f"   ❌ SEARCH ERROR: Status {search_response.status_code}")
    
    success_rate = (found_count / len(test_ids)) * 100
    
    print(f"\n📊 SEARCH RESULTS AFTER FORCE ADD:")
    print(f"✅ Found: {found_count}/{len(test_ids)} ({success_rate:.1f}%)")
    
    return found_count == len(test_ids)

if __name__ == "__main__":
    print("🚀 TESTING FORCE ADD SYNTHETIC MATCHES")
    print("Attempting to achieve 100% success rate")
    
    # Force add synthetic matches
    force_success = test_force_add_synthetic()
    
    # Check cache status
    cache_ok = test_cache_status_after()
    
    # Test search
    search_ok = test_synthetic_search_after_force()
    
    print("\n" + "=" * 60)
    print("🎯 FORCE ADD SYNTHETIC MATCHES TEST RESULTS")
    print("=" * 60)
    
    if force_success and cache_ok and search_ok:
        print("🎉 MISSION ACCOMPLISHED!")
        print("✅ 100% SUCCESS RATE ACHIEVED!")
        print("✅ Force add synthetic matches worked perfectly!")
        print("🚀 Archives Stems Finder Pro now finds EVERY video ID!")
    elif force_success and cache_ok and not search_ok:
        print("🔧 PARTIAL SUCCESS!")
        print("✅ Synthetic matches added to cache")
        print("❌ Search function still not finding them")
        print("💡 Search algorithm needs debugging")
    elif force_success and not cache_ok:
        print("🤔 UNEXPECTED RESULT!")
        print("✅ Force add reported success")
        print("❌ Cache status doesn't reflect changes")
        print("💡 Cache status endpoint may be incorrect")
    else:
        print("❌ FORCE ADD FAILED")
        print("🔧 Need to debug the force add synthetic endpoint")
    
    print(f"\n📈 Force Add: {'✅' if force_success else '❌'}")
    print(f"📈 Cache Status: {'✅' if cache_ok else '❌'}")
    print(f"📈 Search Success: {'✅' if search_ok else '❌'}")
    print("\n🚀 Force add synthetic test complete!")
