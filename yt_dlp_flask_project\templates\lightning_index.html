<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ Lightning Fast Stems Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1200px;
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .search-section {
            padding: 2rem;
        }
        .search-box {
            position: relative;
            margin-bottom: 2rem;
        }
        .search-input {
            border: 3px solid #667eea;
            border-radius: 50px;
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }
        .search-input:focus {
            border-color: #ff6b6b;
            box-shadow: 0 0 0 0.2rem rgba(255, 107, 107, 0.25);
            outline: none;
        }
        .search-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 1rem;
            width: 100%;
        }
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .stats-bar {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        .stat-item {
            text-align: center;
            margin: 0.5rem;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .result-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease;
        }
        .result-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .score-badge {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .search-type-badge {
            background: #28a745;
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        .drive-link {
            background: #4285f4;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        .drive-link:hover {
            background: #3367d6;
            color: white;
            transform: translateY(-1px);
        }
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .test-buttons {
            margin-top: 1rem;
        }
        .test-btn {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 0.5rem 1rem;
            margin: 0.2rem;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bolt me-3"></i>Lightning Fast Stems Finder</h1>
            <p class="mb-0">Search 16,000+ stems in under 1 second!</p>
        </div>

        <div class="search-section">
            <!-- Stats Bar -->
            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-number" id="cacheSize">Loading...</div>
                    <div class="stat-label">Total Stems</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="searchTime">0.000s</div>
                    <div class="stat-label">Last Search Time</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="cacheStatus">Checking...</div>
                    <div class="stat-label">Cache Status</div>
                </div>
                <div class="stat-item">
                    <button id="refreshBtn" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Search Box -->
            <div class="search-box">
                <form id="searchForm">
                    <input type="text" id="searchInput" class="search-input" 
                           placeholder="Enter YouTube URL, Video ID (C8_3zchPsAY), VP/OCD number (VP-16338, OCD-12623), or search terms..." 
                           required>
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search me-2"></i>Lightning Search
                    </button>
                </form>
            </div>

            <!-- Quick Test Buttons -->
            <div class="test-buttons">
                <h6>Quick Test:</h6>
                <button class="test-btn" onclick="testSearch('VP-16338')">VP-16338</button>
                <button class="test-btn" onclick="testSearch('VP-16345')">VP-16345</button>
                <button class="test-btn" onclick="testSearch('VP-16342')">VP-16342</button>
                <button class="test-btn" onclick="testSearch('OCD-12623')">OCD-12623</button>
                <button class="test-btn" onclick="testSearch('C8_3zchPsAY')">C8_3zchPsAY</button>
                <button class="test-btn" onclick="testSearch('sadhguru')">Sadhguru</button>
            </div>

            <!-- Loading -->
            <div id="loading" class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Searching...</span>
                </div>
                <p class="mt-2">Lightning search in progress...</p>
            </div>

            <!-- Results -->
            <div id="results"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load cache status on page load
        async function loadCacheStatus() {
            try {
                const response = await fetch('/cache/status');
                const data = await response.json();
                
                document.getElementById('cacheSize').textContent = 
                    data.cache_size ? data.cache_size.toLocaleString() : 'Loading...';
                
                document.getElementById('cacheStatus').textContent = 
                    data.cache_loaded ? 'Ready' : 'Loading...';
                
            } catch (error) {
                document.getElementById('cacheStatus').textContent = 'Error';
            }
        }

        // Perform search
        async function performSearch(query) {
            const startTime = performance.now();
            
            try {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results').innerHTML = '';
                
                const formData = new FormData();
                formData.append('query', query);
                
                const response = await fetch('/search', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data);
                    document.getElementById('searchTime').textContent = data.elapsed_time + 's';
                } else {
                    displayError(data.error || 'Search failed');
                }
                
            } catch (error) {
                displayError('Network error: ' + error.message);
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        // Display search results
        function displayResults(data) {
            const resultsDiv = document.getElementById('results');
            
            if (data.results.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-search me-2"></i>
                        No results found for "${data.query}". Try different keywords.
                    </div>
                `;
                return;
            }

            let html = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    Found ${data.total_matches} matches for "${data.query}" in ${data.elapsed_time}s
                    <span class="badge bg-primary ms-2">${data.search_type}</span>
                </div>
            `;

            data.results.forEach(result => {
                html += `
                    <div class="result-card">
                        <div class="result-header">
                            <div>
                                <span class="score-badge">Score: ${result.score}</span>
                                <span class="search-type-badge ms-2">${result.search_type}</span>
                            </div>
                            <small class="text-muted">${result.sheet_name}</small>
                        </div>
                        <h6 class="mb-2">
                            <i class="fas fa-file-video me-2 text-primary"></i>
                            ${result.filename}
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>Duration: ${result.duration}<br>
                                    <i class="fas fa-hashtag me-1"></i>VP/OCD: ${result.ocd_vp || 'N/A'}<br>
                                    <i class="fab fa-youtube me-1"></i>Video ID: ${result.video_id || 'N/A'}
                                </small>
                            </div>
                            <div class="col-md-6 text-end">
                                <a href="${result.drive_link}" target="_blank" class="drive-link">
                                    <i class="fab fa-google-drive me-1"></i>Open in Drive
                                </a>
                            </div>
                        </div>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;
        }

        // Display error
        function displayError(message) {
            document.getElementById('results').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }

        // Test search function
        function testSearch(query) {
            document.getElementById('searchInput').value = query;
            performSearch(query);
        }

        // Refresh cache
        async function refreshCache() {
            const btn = document.getElementById('refreshBtn');
            const originalText = btn.innerHTML;
            
            try {
                btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
                btn.disabled = true;
                
                const response = await fetch('/cache/refresh', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    loadCacheStatus();
                    alert('Cache refreshed successfully!');
                } else {
                    alert('Cache refresh failed: ' + data.message);
                }
            } catch (error) {
                alert('Cache refresh failed: ' + error.message);
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // Event listeners
        document.getElementById('searchForm').addEventListener('submit', (e) => {
            e.preventDefault();
            const query = document.getElementById('searchInput').value.trim();
            if (query) {
                performSearch(query);
            }
        });

        document.getElementById('refreshBtn').addEventListener('click', refreshCache);

        // Load cache status on page load
        document.addEventListener('DOMContentLoaded', loadCacheStatus);
        
        // Auto-refresh cache status every 30 seconds
        setInterval(loadCacheStatus, 30000);
    </script>
</body>
</html>
