#!/usr/bin/env python3
"""
Test search functionality with specific video IDs
"""

import requests
import time

def test_search_queries():
    """Test the search functionality with specific queries"""
    
    # Test queries from user
    test_queries = [
        'aEQsJ2LVKbM',
        'V9krqoYDI_U',
        'DgQQqwIwfj0',
        '3amX-jVo4-U',
        'LNQvSjQYA0I',
        'VP-37160',
        'GUxzcqfo568',
        'CQlgTAxgL-J'
    ]
    
    print("🔍 Testing Search Functionality")
    print("=" * 60)
    
    session = requests.Session()
    
    # Note: This test assumes you're already logged in via browser
    # In a real test, you'd need to handle OAuth login programmatically
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 TEST {i}: '{query}'")
        
        try:
            start_time = time.time()
            
            # Make search request
            response = session.post(
                "http://127.0.0.1:5000/search",
                data={'query': query},
                timeout=10
            )
            
            search_time = time.time() - start_time
            
            print(f"   Response Status: {response.status_code}")
            print(f"   Search Time: {search_time:.3f}s")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    matches = result.get('total_matches', 0)
                    
                    print(f"   Total Matches: {matches}")
                    
                    if matches > 0:
                        print(f"   ✅ SUCCESS! Found {matches} matches")
                        
                        # Show first match details
                        first_match = result.get('results', [{}])[0]
                        filename = first_match.get('filename', 'N/A')
                        match_type = first_match.get('match_type', 'Unknown')
                        score = first_match.get('score', 0)
                        
                        print(f"   📄 Best match: {filename[:60]}...")
                        print(f"   🎯 Match type: {match_type}")
                        print(f"   📊 Score: {score}")
                    else:
                        print(f"   ❌ No matches found")
                        
                except ValueError:
                    # Not JSON response, might be HTML (redirect to login)
                    if 'login' in response.text.lower():
                        print(f"   ⚠️ Redirected to login (authentication required)")
                    else:
                        print(f"   ⚠️ Non-JSON response received")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📊 SEARCH TEST COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    test_search_queries()
