#!/usr/bin/env python3
"""
Comprehensive test for all video IDs provided by user
Tests the enhanced search function to ensure 100% success rate
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:8080"
PASSWORD = "Shiva@123"

# All video IDs provided by user
TEST_VIDEO_IDS = [
    "CPN-zOup_uS",  # User mentioned this should find "Stems not available Z9906_Daily-Mystic-Quote-17-May-2021_English_06Mins-04Secs_Consolidated"
    "CO2DQGZgUQL",
    "CO0NM2AIvRx",
    "COzgvYcgBAx",
    "COw40WfBzWZ",
    "COxhRudAD4k",
    "COuaS9mBRjR",
    "COvKclMopkO",
    "Bm-QyuLHutC",
    "Bm7zt7_nWN4",
    "BkxrLGZHN1r",
    "Bi9PLrYH3Ef",
    "rbYdXbEVm6E",
    "ChTnwpkCMhg",
    "KX_pnMG-4RE",
    "IH23o77OZXw",
    "FBYoZ-FgC84",
    "g7SvHaSzz9A",
    "5Rr13rAlifM",
    "-tLR-BztVKI",
    "8oSuwfAfWh4",
    "W2qSmUq3YQk",
    "yBJqCW6bxsY",
    "f7ZrEl04CLk",
    "wllIeFb2xpU",
    "TMTvUh-E-Oo",
    "mC33zUuLdMU",
    "1zCYZFEllZQ",
    "houc7dj9Y-0",
    "3grLtuUx1gc",
    "VdJJgnLTno8",
    "syMyFY6vwEs",
    "F0j6FAIQ_IA",
    "H4qQ7MHACbw",
    "dTI0jB1GCYI",
    "jOxzOkA86O0",
    "RG0hb8naZf0",
    "ijabgcHXsZE",
    "1QKqMyGFz5M",
    "blAKA2YEiJQ",
    "WKGTcuv5vls",
    "AHS1c_vqjxI",
    "BtPrmLLHtKY",
    "TMHhylNs-3Q",
    "0b6tOHpLnIY",
    "W8L48CJAygQ",
    "UfNzZpLpWhg",
    "Ks18jXRi1G8",
    "UOzRAYIf__E",
    "BsVuwuch1js",
    "BsLT2p5BFQw",
    "BumFp0jhSbK",
    "BugVxFThLvt",
    "BuV5xqPh4kx",
    "BuOTPOaBi1B",
    "B2WW7kCgSoy",
    "BuG4rp4hVFl",
    "Kgowgm1KeZ4",
    "DEB_7N2TnjS",
    "DD_Oz2TTLrA",
    "DANaqGDu1hn"
]

def test_video_id_search():
    """Test search functionality for all provided video IDs"""
    
    session = requests.Session()
    
    print("🔍 COMPREHENSIVE VIDEO ID SEARCH TEST")
    print("=" * 60)
    
    # Login first
    login_response = session.post(f"{BASE_URL}/login", data={'password': PASSWORD})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    print(f"📊 Testing {len(TEST_VIDEO_IDS)} video IDs...")
    print("-" * 60)
    
    found_count = 0
    not_found_count = 0
    total_results = 0
    
    for i, video_id in enumerate(TEST_VIDEO_IDS, 1):
        print(f"\n{i:2d}. Testing: {video_id}")
        
        # Search for the video ID
        search_response = session.post(f"{BASE_URL}/search", data={'query': video_id})
        
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            search_time = result.get('search_time', 0)
            
            if matches > 0:
                found_count += 1
                total_results += matches
                print(f"    ✅ FOUND: {matches} matches in {search_time:.3f}s")
                
                # Show details of first few matches
                results = result.get('results', [])
                for j, match in enumerate(results[:3]):  # Show first 3 matches
                    filename = match.get('filename', 'N/A')
                    ocd_vp = match.get('ocd_vp', 'N/A')
                    sheet = match.get('sheet_name', 'N/A')
                    score = match.get('score', 0)
                    match_type = match.get('match_type', 'Unknown')
                    
                    print(f"       {j+1}. {filename[:60]}...")
                    print(f"          OCD/VP: {ocd_vp} | Sheet: {sheet} | Score: {score} | Type: {match_type}")
                
                if matches > 3:
                    print(f"       ... and {matches - 3} more matches")
                    
            else:
                not_found_count += 1
                print(f"    ❌ NOT FOUND: No matches")
        else:
            not_found_count += 1
            print(f"    ❌ SEARCH ERROR: Status {search_response.status_code}")
        
        # Small delay to avoid overwhelming the server
        time.sleep(0.1)
    
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 60)
    print(f"✅ Found: {found_count}/{len(TEST_VIDEO_IDS)} video IDs ({found_count/len(TEST_VIDEO_IDS)*100:.1f}%)")
    print(f"❌ Not Found: {not_found_count}/{len(TEST_VIDEO_IDS)} video IDs")
    print(f"📈 Total Results: {total_results} matches across all searches")
    print(f"📊 Average Results per Found ID: {total_results/max(found_count, 1):.1f}")
    
    if found_count == len(TEST_VIDEO_IDS):
        print("\n🎉 SUCCESS: 100% of video IDs found!")
        print("✅ Search function is working perfectly!")
    else:
        print(f"\n⚠️ PARTIAL SUCCESS: {found_count/len(TEST_VIDEO_IDS)*100:.1f}% found")
        print("🔧 Some video IDs may need investigation")
        
        # List the not found IDs
        print("\n❌ Video IDs not found:")
        session_test = requests.Session()
        session_test.post(f"{BASE_URL}/login", data={'password': PASSWORD})
        
        for video_id in TEST_VIDEO_IDS:
            search_response = session_test.post(f"{BASE_URL}/search", data={'query': video_id})
            if search_response.status_code == 200:
                result = search_response.json()
                if result.get('total_matches', 0) == 0:
                    print(f"   • {video_id}")
    
    return found_count == len(TEST_VIDEO_IDS)

def test_specific_examples():
    """Test specific examples mentioned by user"""
    
    session = requests.Session()
    session.post(f"{BASE_URL}/login", data={'password': PASSWORD})
    
    print("\n🎯 TESTING SPECIFIC USER EXAMPLES")
    print("=" * 60)
    
    # Test the specific example mentioned by user
    print("1. Testing CPN-zOup_uS (should find Daily-Mystic-Quote)")
    search_response = session.post(f"{BASE_URL}/search", data={'query': 'CPN-zOup_uS'})
    
    if search_response.status_code == 200:
        result = search_response.json()
        matches = result.get('total_matches', 0)
        
        if matches > 0:
            print(f"   ✅ Found {matches} matches")
            results = result.get('results', [])
            for match in results:
                filename = match.get('filename', '')
                if 'Daily-Mystic-Quote' in filename or 'Z9906' in filename:
                    print(f"   🎯 CORRECT MATCH: {filename}")
                    print(f"      OCD/VP: {match.get('ocd_vp', 'N/A')}")
                    print(f"      Sheet: {match.get('sheet_name', 'N/A')}")
                    break
        else:
            print("   ❌ No matches found")
    
    print("\n2. Testing other high-priority examples...")
    priority_tests = ['CO2DQGZgUQL', 'Bm-QyuLHutC', 'rbYdXbEVm6E', 'H4qQ7MHACbw']
    
    for video_id in priority_tests:
        search_response = session.post(f"{BASE_URL}/search", data={'query': video_id})
        if search_response.status_code == 200:
            result = search_response.json()
            matches = result.get('total_matches', 0)
            print(f"   {video_id}: {'✅' if matches > 0 else '❌'} {matches} matches")

def main():
    """Run comprehensive video ID tests"""
    print("🚀 STARTING COMPREHENSIVE VIDEO ID TESTING")
    print("Testing enhanced search function with all provided video IDs")
    print("Goal: Achieve 100% success rate for all video IDs")
    
    # Test all video IDs
    success = test_video_id_search()
    
    # Test specific examples
    test_specific_examples()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 MISSION ACCOMPLISHED!")
        print("✅ All video IDs found successfully!")
        print("🚀 Search function is working at 100% efficiency!")
    else:
        print("🔧 NEEDS IMPROVEMENT")
        print("⚠️ Some video IDs still not found")
        print("💡 May need further search algorithm enhancement")
    
    return success

if __name__ == "__main__":
    main()
