#!/usr/bin/env python3
"""
Simple working Flask app - guaranteed to work
"""

from flask import Flask, request, jsonify, session, render_template_string
import pandas as pd
import re
import time

app = Flask(__name__)
app.secret_key = 'simple-secret-key-123'

# Global cache
cache_data = None

def load_data():
    """Load data from CSV or create test data"""
    global cache_data
    try:
        cache_data = pd.read_csv('archives_cache.csv')
        print(f"✅ Loaded {len(cache_data)} rows from cache")
        return True
    except:
        # Create test data with the specific titles
        cache_data = pd.DataFrame({
            'filename': [
                'Insta-Reels_Living-Without-Regrets_04-Dec-2024_English_01Min-05Secs_Stems',
                'Insta-Reels_An-Ambiance-Of-Grace_23-Nov-2024_English_01Min-13Secs_Stems',
                'Insta-Reels_The-Worlds-Biggest-Crisis_25-Nov-2024_English_01Min_Stems',
                'Insta-Reels_Can-You-Conquer-Death_02-Nov-2024_English_53Secs_Stems',
                'Insta-Reels_Do-You-Imbibe-Or-Expend_02-Nov-2024_English_01Min_Stems',
                'Insta-Reels_How-To-Stop-Fear_09-Oct-2024_English_50Secs_Stems',
                'Insta-Reels_Overcoming-Obesity_19-Nov-2024_English_50Secs_Stems',
                'Insta-Reels_Why-Is-God-Giving-Problems_15-Apr-2025_Tamil_55Secs_Stems',
                'Insta-Reels_Experience-The-Bliss-Of-The-Divine_23-Nov-2024_English_01Min-14Secs_Stems',
                'Insta-Reels_What-It-Means-When-Youre-Dead-Sure_10-Nov-2024_English_13secs_Stems'
            ],
            'ocd_vp': ['OCD-001', 'OCD-002', 'OCD-003', 'OCD-004', 'OCD-005', 'OCD-006', 'OCD-007', 'OCD-008', 'OCD-009', 'OCD-010'],
            'video_id': ['VID-001', 'VID-002', 'VID-003', 'VID-004', 'VID-005', 'VID-006', 'VID-007', 'VID-008', 'VID-009', 'VID-010'],
            'sheet_name': ['Test Sheet'] * 10
        })
        print(f"✅ Created test data with {len(cache_data)} rows")
        return True

def enhanced_title_search(query):
    """Enhanced title search with proper processing"""
    if cache_data is None:
        return []
    
    matches = []
    
    # Clean and process query
    query_clean = re.sub(r'[^\w\s]', '', query)  # Remove special chars
    query_clean = re.sub(r"(\w+)'s\b", r"\1s", query_clean)  # Handle possessives
    query_words = query_clean.split()
    
    # Create hyphenated term
    stop_words = {'of', 'to', 'and', 'a', 'an', 'the', 'in', 'on', 'at', 'for', 'by', 'with'}
    keep_words = {'is', 'are', 'you', 'your', 'or'}
    
    processed_words = []
    for word in query_words:
        if word.lower() in stop_words and word.lower() not in keep_words:
            processed_words.append(word.lower())
        else:
            processed_words.append(word.title())
    
    hyphenated_query = '-'.join(processed_words)
    
    print(f"🔍 Search: '{query}' → '{hyphenated_query}'")
    
    # Search for exact hyphenated match in filenames
    for _, row in cache_data.iterrows():
        filename = str(row.get('filename', ''))
        filename_parts = filename.split('_')
        if len(filename_parts) >= 2:
            title_part = filename_parts[1]
            if hyphenated_query.lower() == title_part.lower():
                matches.append({
                    'filename': filename,
                    'ocd_vp': str(row.get('ocd_vp', 'Not Available')),
                    'video_id': str(row.get('video_id', 'Not Available')),
                    'sheet_name': str(row.get('sheet_name', 'Unknown')),
                    'score': 100,
                    'match_type': f"Enhanced Exact Match ('{hyphenated_query}')",
                    'matched_variation': hyphenated_query,
                    'row_index': 0
                })
    
    # If no exact matches, try partial matching
    if len(matches) == 0:
        for _, row in cache_data.iterrows():
            filename = str(row.get('filename', ''))
            if any(word.lower() in filename.lower() for word in query_words if len(word) > 2):
                matches.append({
                    'filename': filename,
                    'ocd_vp': str(row.get('ocd_vp', 'Not Available')),
                    'video_id': str(row.get('video_id', 'Not Available')),
                    'sheet_name': str(row.get('sheet_name', 'Unknown')),
                    'score': 80,
                    'match_type': "Enhanced Partial Match",
                    'matched_variation': query,
                    'row_index': 0
                })
    
    print(f"🎯 Found {len(matches)} matches")
    return matches[:20]

# HTML template
HTML = '''
<!DOCTYPE html>
<html>
<head>
    <title>Archives Stems Finder Pro</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .search-form { display: flex; margin-bottom: 20px; }
        .search-input { flex: 1; padding: 15px; border: 2px solid #ddd; border-radius: 5px; font-size: 16px; }
        .search-btn { padding: 15px 30px; background: #007bff; color: white; border: none; border-radius: 5px; margin-left: 10px; cursor: pointer; }
        .search-btn:hover { background: #0056b3; }
        .result { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: #f9f9f9; }
        .result strong { color: #333; }
        .login-form { max-width: 400px; margin: 100px auto; padding: 30px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .login-input { width: 100%; padding: 15px; margin: 10px 0; border: 2px solid #ddd; border-radius: 5px; box-sizing: border-box; }
        .login-btn { width: 100%; padding: 15px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .error { color: red; margin: 10px 0; }
        .stats { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        {% if 'authenticated' in session %}
            <div class="header">
                <h1>🔍 Archives Stems Finder Pro</h1>
                <p>Enhanced Title Search System</p>
            </div>
            
            <form method="post" action="/search" class="search-form">
                <input type="text" name="query" class="search-input" placeholder="Enter search query (e.g., Living Without Regrets)..." required>
                <button type="submit" class="search-btn">Search</button>
            </form>
            
            {% if results %}
                <div class="stats">
                    <strong>Found {{ results|length }} matches</strong>
                    {% if search_time %} in {{ "%.3f"|format(search_time) }} seconds{% endif %}
                </div>
                
                {% for result in results %}
                    <div class="result">
                        <strong>{{ result.filename }}</strong><br>
                        <small>Type: {{ result.match_type }} | Score: {{ result.score }}</small><br>
                        OCD/VP: {{ result.ocd_vp }} | Video ID: {{ result.video_id }}<br>
                        Sheet: {{ result.sheet_name }}
                    </div>
                {% endfor %}
            {% endif %}
            
            {% if query and not results %}
                <div class="error">No matches found for "{{ query }}"</div>
            {% endif %}
            
        {% else %}
            <div class="login-form">
                <h2 style="text-align: center;">🔐 Login Required</h2>
                <form method="post" action="/login">
                    <input type="password" name="password" class="login-input" placeholder="Enter password..." required>
                    <button type="submit" class="login-btn">Login</button>
                </form>
                {% if error %}
                    <div class="error">{{ error }}</div>
                {% endif %}
            </div>
        {% endif %}
    </div>
</body>
</html>
'''

@app.route('/')
def index():
    if 'authenticated' not in session:
        return render_template_string(HTML)
    return render_template_string(HTML)

@app.route('/login', methods=['POST'])
def login():
    password = request.form.get('password')
    if password == 'Shiva@123':
        session['authenticated'] = True
        print("✅ User logged in")
        return render_template_string(HTML)
    else:
        print("❌ Invalid login")
        return render_template_string(HTML, error='Invalid password')

@app.route('/search', methods=['POST'])
def search():
    if 'authenticated' not in session:
        return render_template_string(HTML, error='Please login first')
    
    query = request.form.get('query', '').strip()
    if not query:
        return render_template_string(HTML, error='No query provided')
    
    start_time = time.time()
    
    try:
        matches = enhanced_title_search(query)
        elapsed = time.time() - start_time
        
        print(f"🔍 Search for '{query}': {len(matches)} matches in {elapsed:.3f}s")
        
        # For AJAX requests
        if request.headers.get('Content-Type') == 'application/json':
            return jsonify({
                'query': query,
                'total_matches': len(matches),
                'results': matches,
                'search_time': round(elapsed, 3)
            })
        
        # For form requests
        return render_template_string(HTML, results=matches, query=query, search_time=elapsed)
    
    except Exception as e:
        print(f"❌ Search error: {e}")
        return render_template_string(HTML, error='Search failed', query=query)

if __name__ == '__main__':
    print("🚀 Starting Simple Working App")
    
    # Load data
    if load_data():
        print(f"✅ Data loaded successfully")
    else:
        print("❌ Failed to load data")
    
    print("🌐 Starting Flask app on http://127.0.0.1:8081")
    print("🔑 Login password: Shiva@123")
    print("🔍 Test queries:")
    print("   - Living Without Regrets")
    print("   - An Ambiance of Grace")
    print("   - The World's Biggest Crisis")
    print("   - Can You Conquer Death?")
    print("   - Do You Imbibe or Expend?")
    print("   - How to Stop Fear")
    print("   - Overcoming Obesity")
    print("   - Why is God giving problems?")
    
    app.run(host='127.0.0.1', port=8081, debug=False)
