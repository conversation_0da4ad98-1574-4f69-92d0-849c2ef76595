#!/usr/bin/env python3
"""
Simple test for missing IDs
"""

import requests
import time

def test_ids():
    """Test the missing IDs"""
    base_url = "http://127.0.0.1:8080"
    
    # List of missing IDs to test
    missing_ids = [
        "OCD-16673", "OCD-16668", "OCD-16698", "OCD-15138", "OCD-16697", 
        "OCD-16677", "OCD-16693", "VP-9992", "VP-8882", "VP-5552", 
        "VP-3332", "VP-1112", "VP-0002", "VP-42222", "C9SQ3zxyYO9", 
        "DKrifQ0T_8Z", "A75b1NKWCC4"
    ]
    
    session = requests.Session()
    
    try:
        # Login
        print("Testing login...")
        login_response = session.post(f"{base_url}/login", data={'password': 'Shiva@123'})
        print(f"Login status: {login_response.status_code}")
        
        # Check cache status
        print("Checking cache status...")
        status_response = session.get(f"{base_url}/cache/status")
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"Cache loaded: {status_data.get('cache_loaded', False)}")
            print(f"Cache size: {status_data.get('cache_size', 0):,} records")
            print(f"Last updated: {status_data.get('last_updated', 'N/A')}")
            
            if status_data.get('cache_size', 0) == 0:
                print("CRITICAL: Cache is empty!")
                return
        
        # Test first few IDs
        print(f"Testing first 5 IDs...")
        found_count = 0
        
        for i, test_id in enumerate(missing_ids[:5], 1):
            print(f"[{i}/5] Testing: {test_id}")
            
            search_response = session.post(f"{base_url}/search", data={'query': test_id})
            
            if search_response.status_code == 200:
                result_data = search_response.json()
                total_matches = result_data.get('total_matches', 0)
                
                if total_matches > 0:
                    print(f"  FOUND: {total_matches} matches")
                    found_count += 1
                else:
                    print(f"  NOT FOUND")
            else:
                print(f"  Search error: {search_response.status_code}")
        
        print(f"Found: {found_count}/5 IDs")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_ids()
