#!/usr/bin/env python3
"""
Test specifically "Why is <PERSON> giving problems?" until it works
"""

import requests
import time

def test_god_query_step_by_step():
    """Test the God query step by step until it works"""
    
    print("🔍 TESTING SPECIFICALLY: 'Why is <PERSON> giving problems?'")
    print("=" * 70)
    print("Goal: Make this query return the expected result")
    
    session = requests.Session()
    
    # Login
    print("📋 STEP 1: LOGIN")
    login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    if login_response.status_code != 200:
        print("❌ Login failed")
        return False
    print("✅ Login successful")
    
    # Test that the target file exists with direct search
    print("\n📋 STEP 2: VERIFY TARGET FILE EXISTS")
    direct_search = "Why-Is-God-Giving-Problems"
    print(f"Testing direct search: '{direct_search}'")
    
    response = session.post("http://127.0.0.1:8080/search", data={'query': direct_search})
    if response.status_code == 200:
        result = response.json()
        matches = result.get('total_matches', 0)
        print(f"✅ Direct search found {matches} matches")
        
        if matches > 0:
            target_file = result['results'][0]['filename']
            print(f"✅ Target file: {target_file}")
        else:
            print("❌ Target file not found with direct search!")
            return False
    else:
        print("❌ Direct search failed!")
        return False
    
    # Test the sophisticated query
    print("\n📋 STEP 3: TEST SOPHISTICATED QUERY")
    query = "Why is God giving problems?"
    print(f"Testing: '{query}'")
    
    response = session.post("http://127.0.0.1:8080/search", data={'query': query})
    
    if response.status_code == 200:
        result = response.json()
        matches = result.get('total_matches', 0)
        search_time = result.get('search_time', 0)
        
        print(f"Response: {matches} matches in {search_time:.3f}s")
        
        if matches > 0:
            print("🎉 SUCCESS! Found matches:")
            for i, match in enumerate(result.get('results', []), 1):
                filename = match.get('filename', 'N/A')
                match_type = match.get('match_type', 'Unknown')
                matched_var = match.get('matched_variation', 'N/A')
                score = match.get('score', 0)
                
                print(f"   {i}. {filename}")
                print(f"      Type: {match_type}")
                print(f"      Matched: '{matched_var}'")
                print(f"      Score: {score}")
                
                # Check if this is the expected file
                if "Why-Is-God-Giving-Problems" in filename:
                    print(f"      ✅ THIS IS THE EXPECTED FILE!")
                    return True
            
            print("⚠️ Found matches but not the expected file")
            return False
        else:
            print("❌ No matches found")
            return False
    else:
        print(f"❌ Search failed: {response.status_code}")
        return False

def test_processing_steps():
    """Test the processing steps for the God query"""
    
    print("\n📋 STEP 4: TEST PROCESSING STEPS")
    print("=" * 70)
    
    import re
    
    query = "Why is God giving problems?"
    print(f"Original: '{query}'")
    
    # Step 1: Remove special characters
    cleaned = re.sub(r'[^\w\s]', '', query)
    print(f"Step 1 - Cleaned: '{cleaned}'")
    
    # Step 2: Replace spaces with hyphens
    processed = cleaned.strip().replace(' ', '-')
    print(f"Step 2 - Processed: '{processed}'")
    
    # Step 3: Split into words
    words = cleaned.strip().split()
    print(f"Step 3 - Words: {words}")
    
    # Step 4: Create splits
    if len(words) > 2:
        first_2 = '-'.join(words[:2])
        second_2 = '-'.join(words[2:])
        print(f"Step 4a - 2-word split: '{first_2}' + '{second_2}'")
        
        if len(words) > 3:
            first_3 = '-'.join(words[:3])
            second_3 = '-'.join(words[3:])
            print(f"Step 4b - 3-word split: '{first_3}' + '{second_3}'")
    
    # Step 5: Individual words
    meaningful_words = [w for w in words if len(w) > 3]
    print(f"Step 5 - Meaningful words: {meaningful_words}")
    
    # What should match
    target = "Why-Is-God-Giving-Problems"
    print(f"\nTarget filename contains: '{target}'")
    
    # Check which terms should match
    search_terms = [processed, second_2, second_3] + meaningful_words
    print(f"Search terms to test: {search_terms}")
    
    for term in search_terms:
        if term.lower() in target.lower():
            print(f"✅ '{term}' should match '{target}'")
        else:
            print(f"❌ '{term}' won't match '{target}'")

def test_individual_terms():
    """Test each search term individually"""
    
    print("\n📋 STEP 5: TEST INDIVIDUAL SEARCH TERMS")
    print("=" * 70)
    
    session = requests.Session()
    session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'})
    
    # Terms that should work based on processing
    search_terms = [
        "Why-is-God-giving-problems",  # Full processed
        "God-giving-problems",         # Second part of 2-word split
        "giving-problems",             # Second part of 3-word split
        "giving",                      # Individual word
        "problems"                     # Individual word
    ]
    
    for term in search_terms:
        print(f"\n🔍 Testing: '{term}'")
        
        response = session.post("http://127.0.0.1:8080/search", data={'query': term})
        
        if response.status_code == 200:
            result = response.json()
            matches = result.get('total_matches', 0)
            
            print(f"   Result: {matches} matches")
            
            if matches > 0:
                # Check if any match contains the target
                for match in result.get('results', [])[:3]:
                    filename = match.get('filename', 'N/A')
                    if "Why-Is-God-Giving-Problems" in filename:
                        print(f"   ✅ FOUND TARGET: {filename}")
                        return True
                    else:
                        print(f"   📄 Other match: {filename[:40]}...")
            else:
                print(f"   ❌ No matches")
        else:
            print(f"   ❌ Error: {response.status_code}")
    
    return False

if __name__ == "__main__":
    print("🚀 FOCUSED TEST: 'Why is God giving problems?'")
    print("Testing this one query until it works 100%")
    
    # Test step by step
    success = test_god_query_step_by_step()
    
    # Test processing steps
    test_processing_steps()
    
    # Test individual terms
    if not success:
        print("\n🔧 Sophisticated query didn't work, testing individual terms...")
        term_success = test_individual_terms()
        if term_success:
            print("✅ Individual terms work - need to fix sophisticated algorithm")
        else:
            print("❌ Even individual terms don't work - need to debug further")
    
    print("\n" + "=" * 70)
    print("🎯 FOCUSED TEST RESULTS")
    print("=" * 70)
    
    if success:
        print("🎉 SUCCESS! 'Why is God giving problems?' is working!")
    else:
        print("🔧 STILL NOT WORKING - need to debug and fix")
    
    print("\n🚀 Focused test complete!")
