#!/usr/bin/env python3
"""
Comprehensive test to verify the enhanced search functionality works 100%
"""

import requests
import json
import time

def comprehensive_test():
    """Test each case step by step until 100% success"""
    
    print("🔬 COMPREHENSIVE ENHANCED SEARCH TEST")
    print("=" * 80)
    print("Testing each case step by step until 100% success")
    
    session = requests.Session()
    
    # Step 1: Login
    print("\n📋 STEP 1: LOGIN")
    try:
        login_response = session.post("http://127.0.0.1:8080/login", data={'password': 'Shiva@123'}, timeout=10)
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Step 2: Test basic search to ensure system works
    print("\n📋 STEP 2: BASIC SEARCH TEST")
    try:
        basic_response = session.post("http://127.0.0.1:8080/search", data={'query': 'cancer'}, timeout=10)
        if basic_response.status_code == 200:
            basic_result = basic_response.json()
            basic_matches = basic_result.get('total_matches', 0)
            print(f"✅ Basic search works: {basic_matches} matches for 'cancer'")
        else:
            print(f"❌ Basic search failed: {basic_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Basic search error: {e}")
        return False
    
    # Step 3: Test direct search terms (we know these work)
    print("\n📋 STEP 3: DIRECT SEARCH TERMS TEST")
    direct_terms = [
        "Can-We-Prevent-Cancer",
        "Why-Is-God-Giving-Problems", 
        "On-Receiving-Grace",
        "You-Should-Not-Love-Shiva"
    ]
    
    direct_results = {}
    for term in direct_terms:
        try:
            response = session.post("http://127.0.0.1:8080/search", data={'query': term}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                matches = result.get('total_matches', 0)
                direct_results[term] = matches
                print(f"✅ '{term}': {matches} matches")
                
                if matches > 0:
                    first_match = result['results'][0]
                    filename = first_match.get('filename', 'N/A')
                    print(f"   📄 {filename[:60]}...")
            else:
                print(f"❌ '{term}': Failed ({response.status_code})")
                direct_results[term] = 0
        except Exception as e:
            print(f"❌ '{term}': Error - {e}")
            direct_results[term] = 0
    
    # Check if all direct terms work
    all_direct_work = all(count > 0 for count in direct_results.values())
    if not all_direct_work:
        print("❌ Direct search terms don't work - cannot proceed")
        return False
    
    print("✅ All direct search terms work!")
    
    # Step 4: Test the enhanced search queries
    print("\n📋 STEP 4: ENHANCED SEARCH QUERIES TEST")
    
    test_cases = [
        {
            'query': 'A tip to prevent cancer?',
            'expected_contains': 'Can-We-Prevent-Cancer',
            'description': 'Cancer prevention tips'
        },
        {
            'query': 'Why is God giving problems?',
            'expected_contains': 'Why-Is-God-Giving-Problems',
            'description': 'Why God gives problems'
        },
        {
            'query': 'Who will receive God\'s grace?',
            'expected_contains': 'On-Receiving-Grace',
            'description': 'Receiving God\'s grace'
        },
        {
            'query': 'Find Answers to Everything',
            'expected_contains': 'You-Should-Not-Love-Shiva',
            'description': 'Finding answers to everything'
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case['query']
        expected = test_case['expected_contains']
        description = test_case['description']
        
        print(f"\n🧪 TEST CASE {i}: {description}")
        print(f"   🔍 Query: '{query}'")
        print(f"   🎯 Expected to contain: '{expected}'")
        
        try:
            response = session.post("http://127.0.0.1:8080/search", data={'query': query}, timeout=15)
            
            print(f"   📡 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                matches = result.get('total_matches', 0)
                search_time = result.get('search_time', 0)
                
                print(f"   📊 Total matches: {matches}")
                print(f"   ⏱️  Search time: {search_time:.3f}s")
                
                if matches > 0:
                    print(f"   📋 Results:")
                    found_expected = False
                    
                    for j, match in enumerate(result.get('results', [])[:3], 1):
                        filename = match.get('filename', 'N/A')
                        match_type = match.get('match_type', 'Unknown')
                        score = match.get('score', 0)
                        
                        print(f"      {j}. {filename[:50]}...")
                        print(f"         Type: {match_type}")
                        print(f"         Score: {score}")
                        
                        # Check if this contains the expected term
                        if expected.lower() in filename.lower():
                            found_expected = True
                            print(f"         ✅ CONTAINS EXPECTED TERM!")
                    
                    if found_expected:
                        passed_tests += 1
                        print(f"   🎉 TEST CASE {i}: PASSED")
                    else:
                        print(f"   ❌ TEST CASE {i}: FAILED - Expected term not found")
                else:
                    print(f"   ❌ TEST CASE {i}: FAILED - No matches found")
            else:
                print(f"   ❌ TEST CASE {i}: FAILED - HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ TEST CASE {i}: ERROR - {e}")
    
    # Step 5: Results summary
    print(f"\n📊 FINAL RESULTS SUMMARY")
    print("=" * 80)
    
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"✅ Passed Tests: {passed_tests}/{total_tests}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print("🎉 PERFECT! 100% SUCCESS ACHIEVED!")
        print("✅ All enhanced search test cases are working!")
        print("🚀 Enhanced search functionality is ready for production!")
        return True
    elif success_rate >= 75.0:
        print("🎯 EXCELLENT! Most test cases passed!")
        print("🔧 Minor adjustments needed for perfect score")
        return False
    elif success_rate >= 50.0:
        print("🔧 GOOD PROGRESS! Partial success achieved")
        print("💡 Need to debug remaining test cases")
        return False
    else:
        print("❌ NEEDS MAJOR WORK! Most test cases failed")
        print("🔧 Requires significant debugging")
        return False

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE ENHANCED SEARCH TEST")
    print("Goal: Achieve 100% success rate for all test cases")
    print("Method: Step-by-step testing with detailed analysis")
    
    success = comprehensive_test()
    
    print("\n" + "=" * 80)
    print("🎯 COMPREHENSIVE TEST COMPLETE")
    print("=" * 80)
    
    if success:
        print("🎉 MISSION ACCOMPLISHED!")
        print("✅ Enhanced search functionality is working perfectly!")
    else:
        print("🔧 MISSION CONTINUES...")
        print("💡 Need to debug and fix remaining issues")
    
    print("\n🚀 Comprehensive test complete!")
